package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import java.util.EnumMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * This class has the utility to get the retry handler implementation based on the name
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WASRetryHandler {

  private static final Map<RetryHandlerName, RetryHandler> RETRY_HANDLER_MAP = new EnumMap<>(
      RetryHandlerName.class);

  /**
   * Adds a retry handler.
   *
   * @param retryHandlerName the retry handler name
   * @param retryHandler     the retry handler
   */
  public static void addHandler(RetryHandlerName retryHandlerName, RetryHandler retryHandler) {

    RETRY_HANDLER_MAP.put(retryHandlerName, retryHandler);
  }

  /**
   * Gets a retry handler.
   *
   * @param handlerName input handler name
   * @return action handler impl
   */
  public static RetryHandler getHandler(RetryHandlerName handlerName) {

    return RETRY_HANDLER_MAP
        .getOrDefault(handlerName, RETRY_HANDLER_MAP.get(RetryHandlerName.STATUS_CODE));
  }
}
