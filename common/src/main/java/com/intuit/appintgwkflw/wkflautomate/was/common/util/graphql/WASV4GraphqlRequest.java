package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.v4.ModelInterface;
import com.intuit.v4.Query;
import java.util.Map;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Getter;

/**
 * The HttpRequest object of WAS.
 *
 * @param <T> the type parameter
 * @param <U> the type parameter
 * <AUTHOR>
 */
@Getter
@Builder(toBuilder = true)
public final class WASV4GraphqlRequest {

  // downstream service url
  private final String url;

  // creation request paylod
  private final ModelInterface request;

  // query request paylod
  private final Query query;

  // auth type used to call downstream service
  @Default private final V4AuthType authType = V4AuthType.SYSTEM_OFFLINE;

  // custom headers
  private final Map<String, String> customHeaders;

  // target realm id used in system offline auth
  private final String targetRealmId;
  
  // For Metric Logging. Downstream service on which GraphQL request is being made.
  private ServiceName serviceName;
}
