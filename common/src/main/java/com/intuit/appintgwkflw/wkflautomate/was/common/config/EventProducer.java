package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR> Config class for EventProducer. It reads all the properties from Spring
 * config. All of these modules are very specific to eventing, hence made as part of event module
 * and not entity module
 */

@Getter
@Setter
public class EventProducer {

  private boolean enabled;
  private boolean enableSecondary;
  private String acks;
  private String retries;
  private String metadataMaxAgeMs;
  private String requestTimeoutMs;
  private String reconnectBackoffMs;
  private String retryBackoffMs;
  private String batchSizeBytes;
  private String lingerMs;
  private String environment;
  private Map<String, String> entityTopicsMapping;
  private Map<String, String> config;
}
