package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.Set;

/**
    Configuration for Camunda Request Response Logger
    Log request and response bodies from camunda for given ownerIds
    Please add ownerIds in config if you want to log request and response bodies of camunda for those owners
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "camunda-request-response-logging")
public class CamundaRequestResponseLoggerConfig {

    private Set<Long> ownerIds = Collections.emptySet();

}
