package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WAS_REST_TEMPLATE;

import com.intuit.appintgwkflw.wkflautomate.was.common.interceptor.HttpClientHeadersInterceptor;

import java.util.concurrent.TimeUnit;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 *     <p>Spring rest template configuration
 */
@Configuration
public class CommonConfig {

  @Autowired private WASHttpClientConfiguration wasHttpClientConfiguration;

  @Autowired private HttpClientHeadersInterceptor httpClientHeadersInterceptor;

  /**
   * configure http pooling client manager
   *
   * @return PoolingHttpClientConnectionManager
   */
  @Bean
  public PoolingHttpClientConnectionManager wasPoolingHttpClientConnectionManager() {
    PoolingHttpClientConnectionManager connectionManager =
        new PoolingHttpClientConnectionManager(
            wasHttpClientConfiguration.getTimeToLiveMillis(), TimeUnit.MILLISECONDS);
    connectionManager.setDefaultMaxPerRoute(wasHttpClientConfiguration.getMaxPerRoute());
    connectionManager.setMaxTotal(wasHttpClientConfiguration.getMaxTotal());
    connectionManager.setValidateAfterInactivity(
        wasHttpClientConfiguration.getValidateConnectionInactivityMillis());
    manageIdleConnections(connectionManager);
    return connectionManager;
  }

  /**
   * Creating an Idle Connection Monitor to remove connections that are no longer needed.Creating a
   * monitor thread to close idle and/or closed connections.
   *
   * @param connectionManager input polling http client
   */
  private void manageIdleConnections(PoolingHttpClientConnectionManager connectionManager) {
    WASIdleConnectionMonitor idleConnectionMonitor =
        new WASIdleConnectionMonitor(
            connectionManager, wasHttpClientConfiguration.getIdleConnectionTimeoutSecondsMillis());
    idleConnectionMonitor.start();
  }

  /**
   * creates request config timeout details
   *
   * @return
   */
  @Bean
  public RequestConfig requestConfig() {
    RequestConfig result =
        RequestConfig.custom()
            // timeout when requesting a connection from the connection manager
            .setConnectionRequestTimeout(
                wasHttpClientConfiguration.getConnectionRequestTimeoutMillis())
            // timeout until a connection is established
            .setConnectTimeout(wasHttpClientConfiguration.getConnectTimeoutMillis())
            // timeout for waiting for data
            .setSocketTimeout(wasHttpClientConfiguration.getSocketTimeoutMillis())
            .build();
    return result;
  }

  /**
   * creates the closeable http client with the Client headers interceptor that will add the INTUIT_TID and
   * Required Authorization headers
   *
   * @param poolingHttpClientConnectionManager input http connection manager
   * @param requestConfig input request config
   * @return
   */
  @Bean
  public CloseableHttpClient wasHttpClient(
      PoolingHttpClientConnectionManager poolingHttpClientConnectionManager,
      RequestConfig requestConfig) {
    CloseableHttpClient closeableHttpClient =
        HttpClientBuilder.create()
            .setConnectionManager(poolingHttpClientConnectionManager)
            .setDefaultRequestConfig(requestConfig)
            .addInterceptorFirst(httpClientHeadersInterceptor)
            .build();
    return closeableHttpClient;
  }

  /**
   * creates the res template with http connection manager and timeout config and adding logger
   * interceptor to log request response in debug mode.
   *
   * @return
   */
  @Bean(name = WAS_REST_TEMPLATE)
  public RestTemplate restTemplate() {
    HttpComponentsClientHttpRequestFactory httpRequestFactory =
        new HttpComponentsClientHttpRequestFactory();
    httpRequestFactory.setHttpClient(
        wasHttpClient(wasPoolingHttpClientConnectionManager(), requestConfig()));
    ClientHttpRequestFactory clientHttpRequestFactory =
        new BufferingClientHttpRequestFactory(httpRequestFactory);
    RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory);
    return restTemplate;
  }
}
