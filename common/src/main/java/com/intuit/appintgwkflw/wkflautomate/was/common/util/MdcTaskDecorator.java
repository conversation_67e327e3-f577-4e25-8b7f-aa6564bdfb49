package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;
import java.util.Optional;

/**
 * Author: <PERSON><PERSON>
 * Date: 19/12/19
 * Description:  Configure the @Async ThreadPool
 * original Runnable and maintain the MDC data around a delegation to its run() method.
 */
public class MdcTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        // Right now: Web thread context !
        // (Grab the current thread MDC data)
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        Optional<String> offeringId = WASContext.getOfferingId();
        return () -> {
            try {
                // Right now: @Async thread context !
                // (Restore the Web thread context's MDC data)
                MDC.setContextMap(contextMap);
                WASContext.setOfferingId(offeringId.orElse(null));
                runnable.run();
            } finally {
                MDC.clear();
                WASContext.clear();
            }
        };
    }
}