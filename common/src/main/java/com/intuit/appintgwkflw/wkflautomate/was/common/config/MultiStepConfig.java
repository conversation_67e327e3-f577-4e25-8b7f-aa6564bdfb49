package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;


/**
 * Configuration related to Multi Step Workflow Config.
 * Temporary change, till create definition of multi-condition is enabled for all entities.
 *
 * One sample config is given below:
 *
 * multi-step:
 *   workflowTemplates:
 *     customApproval:
 *       singleStepVersion: 1
 *   maxNumberOfApprovers: 6
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "multi-step")
public class MultiStepConfig {
    private Map<String, WorkflowTemplate> workflowTemplates;
    private int maxNumberOfSteps;
    private int maxNumberOfApprovers;
}
