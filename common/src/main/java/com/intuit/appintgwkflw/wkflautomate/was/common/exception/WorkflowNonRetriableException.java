package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;

/**
 * Exception for error which should not be retried
 * <AUTHOR>
 */
@Getter
public class WorkflowNonRetriableException extends WorkflowGeneralException {
  private static final long serialVersionUID = 1L;

  public WorkflowNonRetriableException(String workflowError) {
	 super(workflowError);
  }
  
  public WorkflowNonRetriableException(WorkflowError workflowError) {
    super(workflowError);
  }
  
  public WorkflowNonRetriableException(String errorMsg, WorkflowError workflowError) {
    super(workflowError, errorMsg);
  }
  public WorkflowNonRetriableException(WorkflowError workflowError, Object... errorMsg) {
    super(workflowError, errorMsg);
  }
}
