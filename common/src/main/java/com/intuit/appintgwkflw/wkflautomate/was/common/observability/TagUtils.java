package com.intuit.appintgwkflw.wkflautomate.was.common.observability;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import io.opentracing.Span;
import io.opentracing.tag.StringTag;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * This class provides utility methods to handle tags on traces.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class TagUtils {

  private final WASContextHandler contextHandler;

  /**
   * The common tags that need to be added to all traces.
   */
  public static final Map<WASContextEnums, StringTag> COMMON_TAGS = ImmutableMap.of(
      WASContextEnums.INTUIT_TID, new StringTag("intuit_tid"),
      WASContextEnums.OWNER_ID, new StringTag("intuit_realm_id")
  );

  /**
   * Add common tags to all spans.
   *
   * @param span the span
   */
  public void addCommonTags(final Span span) {

    final Map<String, String> contextMap = contextHandler.getAll();
    COMMON_TAGS.forEach((key, tag) -> {
      final String value = MapUtils.getString(contextMap, key.getValue());
      if (StringUtils.isNotBlank(value)) {
        tag.set(span, value);
      }
    });
  }
}
