package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.authz.WASAuthZClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AuthVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.identity.authz.sdk.client.AuthZClient;
import com.intuit.identity.authz.sdk.client.AuthZErrorCode;
import com.intuit.identity.authz.sdk.exception.AuthZException;
import com.intuit.identity.authz.sdk.model.AuthZDecision;
import com.intuit.identity.authz.sdk.model.AuthZRequest;
import com.intuit.identity.authz.sdk.model.AuthZResponse;
import com.intuit.v4.Authorization;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

/** <AUTHOR> */
public class AuthVerifierTest {

  @InjectMocks
  private AuthVerifier authVerifier;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private WASAuthZClient wasAuthZClient;

  @Mock
  private AuthZClient authZClient;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    authZClient = Mockito.mock(AuthZClient.class);
    Mockito.when(wasAuthZClient.getAuthZClient()).thenReturn(authZClient);
  }

  @Test
  public void populateAndValidateRealmIdNull() {
    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "");
    Authorization authorization = new Authorization();
    authorization.putRealm(null);
    Assert.assertNull(authVerifier.populateAndValidateRealmId(request, authorization).getRealm());
  }

  @Test
  public void populateAndValidateRealmIdEmpty() {
    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "");
    Authorization authorization = new Authorization();
    authorization.putRealm("");
    Assert.assertTrue(authVerifier.populateAndValidateRealmId(request, authorization).getRealm().isEmpty());
  }

  @Test
  public void handleNonRealmSystemUserAuthz() throws AuthZException {
    String header =
            "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_appid=Intuit.expertnetwrk.onboarding.practiceopscontroller,intuit_token=abcxyz,intuit_userid=9130354673516926";
    Authorization authorization =new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenReturn(new AuthZResponse(AuthZDecision.PERMIT, null));
    authVerifier.handleSystemUser(authorization);
    Assert.assertNotNull(authorization.getRealm());
    Assert.assertEquals(authorization.getAuthId(), authorization.getRealm());
    WASContext.clear();
  }


  @Test
  public void handleNonRealmSystemUserAuthz_RealmNotEmpty() throws AuthZException {
    String header =
            "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_appid=Intuit.expertnetwrk.onboarding.practiceopscontroller,intuit_token=abcxyz,intuit_userid=9130354673516926,intuit_realmid=12345";
    Authorization authorization =new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
    	.thenReturn(new AuthZResponse(AuthZDecision.PERMIT, null));
    authVerifier.handleSystemUser(authorization);
    Mockito.verify(wasAuthZClient, Mockito.times(1)).getAuthZClient();
    Assert.assertNotNull(authorization.getRealm());
    Assert.assertEquals("12345", authorization.getRealm());
    Assert.assertTrue(WASContext.isRealmSystemUser());
    Assert.assertFalse(WASContext.isNonRealmSystemUser());
    WASContext.clear();
  }

  @Test
  public void handleNonRealmSystemUserAuthz_ErrorThrown() throws AuthZException {
    String header =
            "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_appid=Intuit.expertnetwrk.onboarding.practiceopscontroller,intuit_token=abcxyz,intuit_userid=9130354673516926";
    Authorization authorization =new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenThrow(new AuthZException(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS));

    WorkflowGeneralException workflowGeneralException =
            Assertions.assertThrows(WorkflowGeneralException.class,
                    () -> authVerifier.handleSystemUser(authorization));
    Assert.assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, workflowGeneralException.getWorkflowError());
    WASContext.clear();
  }

  @Test
  public void handleNonRealmSystemUserAuthz_Deny() throws AuthZException {
    String header =
            "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_appid=Intuit.expertnetwrk.onboarding.practiceopscontroller,intuit_token=abcxyz,intuit_userid=9130354673516926";
    Authorization authorization =new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenReturn(new AuthZResponse(AuthZDecision.DENY, null));
    authVerifier.handleSystemUser(authorization);
    Assert.assertNull(authorization.getRealm());
    Assert.assertFalse(WASContext.isNonRealmSystemUser());
    WASContext.clear();
  }

  @Test
  public void populateAndValidateWithTicketVerification_Authz() throws AuthZException {
    String header =
            "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";

    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "123");

    Authorization authorization = new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenReturn(new AuthZResponse(AuthZDecision.PERMIT, null));
    Assert.assertEquals(
            "123", authVerifier.populateAndValidateRealmId(request, authorization).getRealm());
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(WASContextEnums.INTUIT_REALMID, "123");
  }

  @Test
  public void populateAndValidateWithTicketVerification_AuthzDeny() throws AuthZException {
    String header =
            "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";

    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "123");

    Authorization authorization = new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenReturn(new AuthZResponse(AuthZDecision.DENY, null));
    WorkflowGeneralException workflowGeneralException =
            Assertions.assertThrows(WorkflowGeneralException.class,
                    () -> authVerifier.populateAndValidateRealmId(request, authorization));
    Assert.assertEquals(WorkflowError.USER_NOT_REALM_MEMBER, workflowGeneralException.getWorkflowError());
  }

  @Test
  public void populateAndValidateWithTicketVerification_Authz_UnauthorizedException() throws AuthZException {
    String header =
            "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";

    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "123");

    Authorization authorization = new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenThrow(new AuthZException(AuthZErrorCode.AUTHZ_ERROR_UNAUTHORIZED_ACCESS));
    WorkflowGeneralException workflowGeneralException =
            Assertions.assertThrows(WorkflowGeneralException.class,
                    () -> authVerifier.populateAndValidateRealmId(request, authorization));
    Assert.assertEquals(WorkflowError.USER_NOT_REALM_MEMBER, workflowGeneralException.getWorkflowError());
  }

  @Test
  public void populateAndValidateWithTicketVerification_Authz_AnyOtherException() throws AuthZException {
    String header =
            "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_token=\"V1-226-X3v2bc99a7p0wmqhfc5hhy\",intuit_apikey=\"xxx\",intuit_userid=\"xxx\",intuit_apkey_version=\"1.0\" intuit_assetid: 86480sample2885,intuit_app_secret=xxx";

    MockHttpServletRequest request = new MockHttpServletRequest();
    request.addHeader("intuit_realmid", "123");

    Authorization authorization = new Authorization(header);
    Mockito.when(authZClient.authorize(Mockito.any(AuthZRequest.class)))
            .thenThrow(new AuthZException(AuthZErrorCode.AUTHZ_ERROR_PIP_FAILURE));
    WorkflowGeneralException workflowGeneralException =
            Assertions.assertThrows(WorkflowGeneralException.class,
                    () -> authVerifier.populateAndValidateRealmId(request, authorization));
    Assert.assertEquals(WorkflowError.TICKET_VERIFICATION_FAILED, workflowGeneralException.getWorkflowError());
  }

}
