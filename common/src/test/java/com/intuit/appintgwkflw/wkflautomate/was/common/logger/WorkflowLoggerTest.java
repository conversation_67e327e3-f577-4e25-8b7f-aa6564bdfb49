package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowLoggerTest {

  @Mock private WorkflowLogger workflowLogger;

  @Test
  public void testErrorMocked() {
    init();
    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.error(
          () -> {
            return WorkflowLoggerRequest.builder()
                .className(WorkflowLoggerTest.class.getSimpleName())
                .stackTrace(new NullPointerException())
                .message("error occured")
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER);
          });
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.error(Mockito.any()));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDebugMocked() {
    init();
    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.debug(
          () -> {
            return WorkflowLoggerRequest.builder()
                .className(WorkflowLoggerTest.class.getSimpleName())
                .message("error occured")
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER);
          });
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.debug(Mockito.any()));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testInfoMocked() {
    init();
    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.info(
          () -> {
            return WorkflowLoggerRequest.builder()
                .className(WorkflowLoggerTest.class.getSimpleName())
                .message("error occured")
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER);
          });
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.info(Mockito.any()));
    } catch (Exception e) {
      Assert.fail();
    }
  }

  private void init() {
    MDC.put(WASContextEnums.PROCESS_INSTANCE_ID.getValue(), "PID_TEST");
    MDC.put(WASContextEnums.DEFINITION_ID.getValue(), "DEF_ID_TEST");
    MDC.put(WASContextEnums.TEMPLATE_ID.getValue(), "TEMPLATE_ID_TEST");
    MDC.put(WASContextEnums.OWNER_ID.getValue(), "OWNER_ID_TEST");
  }
}
