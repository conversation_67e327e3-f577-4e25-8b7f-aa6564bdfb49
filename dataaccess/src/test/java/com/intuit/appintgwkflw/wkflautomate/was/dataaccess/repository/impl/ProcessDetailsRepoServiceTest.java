package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessesResponse;
import com.intuit.system.interfaces.BaseEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@RunWith(MockitoJUnitRunner.class)
public class ProcessDetailsRepoServiceTest {

    @Mock
    private ProcessDetailsRepository processDetailsRepository;

    @Mock
    private DomainEventRepository domainEventRepository;

    @InjectMocks
    private ProcessDetailsRepoService processDetailsRepoService;

    private ProcessDetails processDetails;

    private DomainEvent<BaseEntity> domainEvent;

    @Before
    public void init() {
        processDetails = new ProcessDetails();
        processDetails.setProcessId("testProcessId");
        processDetails.setProcessStatus(ProcessStatus.ACTIVE);
        processDetails.setEntityVersion(1);

        domainEvent = new DomainEvent<>();
        domainEvent.setEventId(UUID.randomUUID());
    }

    @Test
    public void testUpdateStatusAndPublishDomainEvent() {
        Mockito.doNothing().when(processDetailsRepository).updateProcessStatusAndEntityVersion(ArgumentMatchers.anyString(),
                ArgumentMatchers.any(), ArgumentMatchers.anyInt());
        Mockito.when(domainEventRepository.save(ArgumentMatchers.any(DomainEvent.class))).thenReturn(domainEvent);

        int result = processDetailsRepoService.updateStatusAndPublishDomainEvent(processDetails, domainEvent);

        Assert.assertEquals(1, result);
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .updateProcessStatusAndEntityVersion(ArgumentMatchers.anyString(), ArgumentMatchers.any(),
                        ArgumentMatchers.anyInt());
        Mockito.verify(domainEventRepository, Mockito.times(1)).save(ArgumentMatchers.any(DomainEvent.class));
    }

    @Test
    public void testUpdateStatus() {
        Mockito.when(processDetailsRepository.updateProcessStatus(ArgumentMatchers.anyString(),
                ArgumentMatchers.any())).thenReturn(1);

        int result = processDetailsRepoService.updateStatus("testProcessId", ProcessStatus.ACTIVE);

        Assert.assertEquals(1, result);
        Mockito.verify(processDetailsRepository, Mockito.times(1)).updateProcessStatus(ArgumentMatchers.anyString(),
                ArgumentMatchers.any());
    }

    @Test
    public void testFindIncompleteProcesses() {
        Mockito.when(processDetailsRepository.findByOwnerIdAndProcessStatusAndProcessIdNot(ArgumentMatchers.anyLong(),
                        ArgumentMatchers.any(), ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(Collections.singletonList(processDetails)));

        Optional<List<ProcessDetails>> result = processDetailsRepoService.findIncompleteProcesses(1L, "testProcessId");

        Assert.assertTrue(result.isPresent());
        Assert.assertEquals(1, result.get().size());
        Mockito.verify(processDetailsRepository, Mockito.times(1)).findByOwnerIdAndProcessStatusAndProcessIdNot(
                ArgumentMatchers.anyLong(), ArgumentMatchers.any(), ArgumentMatchers.anyString());
    }

    @Test
    public void testUpdateProcessStatus() {
        Mockito.when(processDetailsRepository.updateStatus(ArgumentMatchers.any(), ArgumentMatchers.anyList())).thenReturn(1);

        int result = processDetailsRepoService.updateProcessStatus(InternalStatus.MARKED_FOR_DELETE, List.of("testProcessId"));

        Assert.assertEquals(1, result);
        Mockito.verify(processDetailsRepository, Mockito.times(1)).updateStatus(ArgumentMatchers.any(),
                ArgumentMatchers.anyList());
    }

    @Test
    public void testFindByDefinitionDetailsListAndProcessStatus() {
        Mockito.when(processDetailsRepository.findByDefinitionDetailsInAndProcessStatus(ArgumentMatchers.anyList(),
                        ArgumentMatchers.any()))
                .thenReturn(Optional.of(Collections.singletonList(processDetails)));

        Optional<List<ProcessDetails>> result = processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                List.of(new DefinitionDetails()), ProcessStatus.ACTIVE);

        Assert.assertTrue(result.isPresent());
        Assert.assertEquals(1, result.get().size());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .findByDefinitionDetailsInAndProcessStatus(ArgumentMatchers.anyList(), ArgumentMatchers.any());
    }

    @Test
    public void testFindByRealmIdAndProcessStatus() {
        Mockito.when(processDetailsRepository.findByOwnerIdAndProcessStatus(ArgumentMatchers.anyLong(), ArgumentMatchers.any()))
                .thenReturn(Optional.of(Collections.singletonList(processDetails)));

        Optional<List<ProcessDetails>> result = processDetailsRepoService.findByRealmIdAndProcessStatus(1L, ProcessStatus.ACTIVE);

        Assert.assertTrue(result.isPresent());
        Assert.assertEquals(1, result.get().size());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .findByOwnerIdAndProcessStatus(ArgumentMatchers.anyLong(), ArgumentMatchers.any());
    }

    @Test
    public void testSaveOrUpdateProcess() {
        Mockito.when(processDetailsRepository.saveAll(ArgumentMatchers.anyList())).thenReturn(Collections.singletonList(processDetails));

        List<ProcessDetails> result = processDetailsRepoService.saveOrUpdateProcess(Collections.singletonList(processDetails));

        Assert.assertEquals(1, result.size());
        Mockito.verify(processDetailsRepository, Mockito.times(1)).saveAll(ArgumentMatchers.anyList());
    }

    @Test
    public void testGetAllProcessesForDefinition() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("testDefinitionId");

        Mockito.when(processDetailsRepository.findByOwnerIdAndDefinitionDetailsIn(ArgumentMatchers.any(),
                        ArgumentMatchers.any()))
                .thenReturn(Collections.singletonList(processDetails));

        WorkflowProcessesResponse result = processDetailsRepoService.getAllProcessesForDefinition("testDefinitionId");

        Assert.assertEquals(1, result.getProcesses().size());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .findByOwnerIdAndDefinitionDetailsIn(ArgumentMatchers.any(), ArgumentMatchers.any());
    }

    @Test
    public void testFindByProcessId() {
        Mockito.when(processDetailsRepository.findByProcessId(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(processDetails));

        Optional<ProcessDetails> result = processDetailsRepoService.findByProcessId("testProcessId");

        Assert.assertTrue(result.isPresent());
        Assert.assertEquals("testProcessId", result.get().getProcessId());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .findByProcessId(ArgumentMatchers.anyString());
    }

    @Test
    public void testFindByProcessIdWithoutDefinitionData() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setDefinitionId("testDefinitionId");

        Mockito.when(processDetailsRepository.findDefinitionDetailsUsingProcessId(ArgumentMatchers.
                anyString())).thenReturn(Optional.of(definitionDetails));

        Optional<DefinitionDetails> result = processDetailsRepoService.findByProcessIdWithoutDefinitionData("testProcessId");

        Assert.assertTrue(result.isPresent());
        Assert.assertEquals("testDefinitionId", result.get().getDefinitionId());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .findDefinitionDetailsUsingProcessId(ArgumentMatchers.anyString());
    }

    @Test
    public void testGetProcessDetails() {
        Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData(ArgumentMatchers.anyString()))
                .thenReturn(Optional.of(processDetails));

        ProcessDetails result = processDetailsRepoService.getProcessDetails("testProcessId");

        Assert.assertNotNull(result);
        Assert.assertEquals("testProcessId", result.getProcessId());
        Mockito.verify(processDetailsRepository, Mockito.times(1))
                .getProcessDetailsWithoutDefinitionData(ArgumentMatchers.anyString());
    }

    @Test
    public void testUpdateEntityVersion() {
        Mockito.when(processDetailsRepository
                .updateEntityVersion(ArgumentMatchers.anyString(), ArgumentMatchers.anyInt())).thenReturn(1);

        ProcessDetails result = processDetailsRepoService.updateEntityVersion(processDetails);

        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(2), result.getEntityVersion());
        Mockito.verify(processDetailsRepository, Mockito.times(1)).updateEntityVersion(
                ArgumentMatchers.anyString(), ArgumentMatchers.anyInt());
    }
}