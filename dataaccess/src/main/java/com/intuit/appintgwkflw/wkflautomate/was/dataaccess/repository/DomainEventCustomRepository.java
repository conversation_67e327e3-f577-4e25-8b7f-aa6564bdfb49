package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;

import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

/**
 * <AUTHOR>
 *     <p>Custom Repositoryb for Domain Events
 */
public interface DomainEventCustomRepository {

  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<DomainEvent>> findDomainEventByPartitionKey(String partitionKey);

  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<?>> findByPartitionKey(String partitionKey);

  @ServiceMetric(serviceName = WAS_DB)
  DomainEvent findByEventId(String eventId);
}
