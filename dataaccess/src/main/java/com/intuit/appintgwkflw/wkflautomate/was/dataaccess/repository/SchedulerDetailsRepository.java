package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.WAS_DB;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

@Retry(name = ResiliencyConstants.WAS_DB)
public interface SchedulerDetailsRepository
    extends JpaRepository<SchedulerDetails, String> {

  /**
   * Find scheduler details on the basis of given definitionDetails.
   * @param definitionDetails
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<SchedulerDetails>> findByDefinitionDetails(DefinitionDetails definitionDetails);

  /**
   * This method deletes all the scheduler details for the given definitionDetails
   * @param definitionDetails
   * @return
   */
  @Transactional
  @ServiceMetric(serviceName = WAS_DB)
  long deleteByDefinitionDetailsIn(final List<DefinitionDetails> definitionDetails);

  /** update the definitionId for multiple scheduler ids */
  @Transactional
  @Modifying
  @Query(
      "update SchedulerDetails sd set sd.definitionDetails.definitionId= :definitionId where sd.schedulerId in (:schedulerIds)")
  @ServiceMetric(serviceName = WAS_DB)
  int updateDefinitionIdForSchedulers(
      @Param("definitionId") String definitionId, @Param("schedulerIds") List<String> schedulerIds);

  @Transactional
  @Modifying
  @Query(
        "update SchedulerDetails sd set sd.isMigrated = :status where sd.definitionDetails.definitionId = :definitionId")
  @ServiceMetric(serviceName = WAS_DB)
  int setMigrationFlagForDefinitionId(@Param("definitionId") String definitionId, @Param("status") Boolean status);

  /**
   * Find scheduler details on the basis of given definitionDetailsList.
   *
   * @param definitionDetailList
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  Optional<List<SchedulerDetails>> findByDefinitionDetailsIn(
      final List<DefinitionDetails> definitionDetailList);

  /**
   * Find scheduler details on the basis of given definitionDetailsList without definitionData
   *
   * @param definitionDetailList
   * @return
   */
  @ServiceMetric(serviceName = WAS_DB)
  @Query(
      "select new SchedulerDetails (sd.schedulerId, sd.schedulerAction, sd.definitionDetails.definitionId) from SchedulerDetails sd where sd.definitionDetails IN :definitionDetails")
  Optional<List<SchedulerDetails>> findByDefinitionDetailsInWithoutDefinitionData(
      @Param("definitionDetails") final List<DefinitionDetails> definitionDetailList);
}
