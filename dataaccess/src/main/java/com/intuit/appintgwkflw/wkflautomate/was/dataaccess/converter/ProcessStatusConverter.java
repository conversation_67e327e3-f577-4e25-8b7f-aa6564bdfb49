package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import javax.persistence.AttributeConverter;

public class ProcessStatusConverter implements AttributeConverter<ProcessStatus, String> {

  @Override
  public String convertToDatabaseColumn(ProcessStatus attribute) {
    return attribute != null ? attribute.getProcessStatus() : null;
  }

  @Override
  public ProcessStatus convertToEntityAttribute(String dbData) {
    return dbData != null ? ProcessStatus.lookupProcessStatus(dbData) : null;
  }
}
