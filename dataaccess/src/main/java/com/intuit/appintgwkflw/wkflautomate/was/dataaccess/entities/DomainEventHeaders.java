package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Builder
@Data
@Getter
@Setter
@AllArgsConstructor
@RequiredArgsConstructor
public class DomainEventHeaders implements Serializable {

  // Identity 2.0 accountid which represent companyId/realmId or userId
  @JsonProperty("accountid")
  private String accountId;

  @JsonProperty("entityChangeAction")
  private EntityChangeAction entityChangeAction;

  @JsonProperty("entitychangeaction")
  private EntityChangeAction entitychangeaction;

  @JsonProperty("entityid")
  private String entityId;

  // Fully qualified IEDM entity type. e.g.
  // 'curation.smallbusiness.qbo.commerce.indirecttax.taxconfiguration.entities.TaxRate'
  @JsonProperty("entitytype")
  private String entityType;

  // A monotonically increasing number
  @JsonProperty("entityVersion")
  private Integer entityVersion;

  @JsonProperty("entityversion")
  private Integer entityversion;

  @JsonProperty("intuittid")
  private String intuitTid;

  @JsonProperty("trace")
  private String trace;

  @JsonProperty("offeringId")
  private String offeringId;

  @JsonProperty("schemaVersion")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String schemaVersion; // Represents the version of the schema. This field is included in JSON only if it is not null. This is required for the DEM to SM migration.

  @JsonProperty("intuitEntityType")
  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String intuitEntityType; // Represents the Intuit entity type. This field is included in JSON only if it is not null. This is required for the DEM to SM migration.

  @JsonProperty("entityversion")
  public Integer getEntityversion() {
    return entityversion;
  }

  @JsonProperty("entitychangeaction")
  public EntityChangeAction getEntitychangeaction() {
    return entitychangeaction;
  }

  @JsonProperty("entityVersion")
  public Integer getEntityVersion() {
    return entityVersion;
  }

  @JsonProperty("entityChangeAction")
  public EntityChangeAction getEntityChangeAction() {
    return entityChangeAction;
  }

}
