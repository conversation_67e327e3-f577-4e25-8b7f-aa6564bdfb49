l1: appintgwkflw
l2: wkflautomate
git_org: appintgwkflw-wkflautomate
service_name: wkflatmnsvc
asset_id: 7065039507767760447
asset_alias: Intuit.appintgwkflw.wkflautomate.wfas

code_repo: github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service.git
deploy_repo: github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service-deployment.git

defaultBranch: master
argocd_server: qbo.argocd.tools-k8s-prd.a.intuit.com:443
argocd_password: argocd-wkflatmnsvc
argocd_project: appintgwkflw-wkflautomate-wkflatmnsvc
argocd_project_enabled: true
# argocd_tracking_revision: master
argocd_manifest_input_branch: master
argocd_manifest_pending_branch: master-pending
renderManifest: true

registry: docker.intuit.com
repo: appintgwkflw/wkflautomate/wkflatmnsvc/service
slack_channel: "#was-build-report"

security_escalation:
  email: <EMAIL>, <EMAIL>, <EMAIL>
  subject: "[WAS] Security Issues Found"
  message: One or more security issues were found as part of the recent build. You can view the results here.

# change this to false once ready for PRD
preprodOnly: false

# change this to false to disable automatic cr creation
autoCreateCR: true

# change this to true for enable Code Analysis stage
SonarQubeAnalysis: true

#change this to true for stop your pipeline in quality gate error
SonarQubeEnforce: false

#Enable/Disable jira transition
enableJiraTransition: true

# enable this to true once test suite has matured in confidence to help automate deployments
enableScorecardReadinessCheck: true
# to bypass the scorecard readiness check's fall back, gitOpsApproval, set this to true
skipScorecardManualFallback: true
# Skips manual approval
skipManualApproval: true


environments:
  qal-usw2-eks:
    ingress_endpoint: https://appintgwkflw-qal-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-qal
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: false
  e2e-usw2-eks:
    ingress_endpoint: https://appintgwkflw-e2e-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-e2e
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: true
  prf-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prf-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-prf
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: false # disable perf deployments from here
      karate: false
  prd-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prd-wkflatmnsvc-albc-root.sbgqboprodusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-prd
    region: usw2
    iks_type: prd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: true
  qalb-usw2-eks:
    ingress_endpoint: https://appintgwkflw-qalb-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-qalb
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: true
  e2eb-usw2-eks:
    ingress_endpoint: https://appintgwkflw-e2eb-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-e2eb
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: true
  prfb-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prfb-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-prfb
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: false # disable perf deployments from here
      karate: false
  prdb-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prdb-wkflatmnsvc-albc-root.sbgqboprodusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-prdb
    region: usw2
    iks_type: prd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: true
  qalb-use2-eks:
    ingress_endpoint: https://appintgwkflw-qalb-wkflatmnsvc-albc-root.qboppduse2.iks2.a.intuit.com
    cluster: https://eksapi-qbo-ppd-use2-k8s-c9ea6b9478cd7512.elb.us-east-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-use2-qalb
    region: use2
    iks_type: ppd
    manifest_format: Kustomize
    use2:
      deploy: true
      karate: false
  e2eb-use2-eks:
    ingress_endpoint: https://appintgwkflw-e2eb-wkflatmnsvc-albc-root.qboppduse2.iks2.a.intuit.com
    cluster: https://eksapi-qbo-ppd-use2-k8s-c9ea6b9478cd7512.elb.us-east-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-use2-e2eb
    region: use2
    iks_type: ppd
    manifest_format: Kustomize
    use2:
      deploy: true
      karate: false
  prdb-use2-eks:
    ingress_endpoint: https://appintgwkflw-prdb-wkflatmnsvc-albc-root.qboprduse2.iks2.a.intuit.com
    cluster: https://eksapi-qbo-prd-use2-k8s-5a5b15c6b5e2d993.elb.us-east-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-use2-prdb
    region: use2
    iks_type: prd
    manifest_format: Kustomize
    use2:
      deploy: true
      karate: false
  e2ec-usw2-eks:
    ingress_endpoint: https://appintgwkflw-e2ec-wkflatmnsvc-albc-root.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-e2ec
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: false
  e2ec-use2-eks:
    ingress_endpoint: https://appintgwkflw-e2ec-wkflatmnsvc-albc-root.qboppduse2.iks2.a.intuit.com
    cluster: https://eksapi-qbo-ppd-use2-k8s-c9ea6b9478cd7512.elb.us-east-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-use2-e2ec
    region: use2
    iks_type: ppd
    manifest_format: Kustomize
    use2:
      deploy: true
      karate: false
  prdc-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prdc-wkflatmnsvc-albc-root.sbgqboprodusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-usw2-prdc
    region: usw2
    iks_type: prd
    manifest_format: Kustomize
    usw2:
      deploy: true
      karate: false
  prdc-use2-eks:
    ingress_endpoint: https://appintgwkflw-prdc-wkflatmnsvc-albc-root.qboprduse2.iks2.a.intuit.com
    cluster: https://eksapi-qbo-prd-use2-k8s-5a5b15c6b5e2d993.elb.us-east-2.amazonaws.com
    namespace: appintgwkflw-wkflatmnsvc-use2-prdc
    region: use2
    iks_type: prd
    manifest_format: Kustomize
    use2:
      deploy: true
      karate: false

ow_reports_sendTo: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>

whitelist_approval_sendTo: <EMAIL>;<EMAIL>
submitters: sgupta5,bagarwal5
whitelist_approval_notifyTo: "@sgupta5 @bagarwal5"

serverUrl: https://pub-sonarqube-ibp-prod-us-west-2-app.prod1-ibp.a.intuit.com/
githubCredentialsId: github-svc-sbseg-ci


PR_FILE_LIMIT: 35

#CR description
changeDescription: "\
 \n\n
 Provide a summary of the change\n
 Workflow Automation Service release.\n

 Who are the 2 people executing the change (Two sets of eyes)?\n
 * (on-call from offering)\n
 * (on-call from the platform)\n

 What will the impact be to Applications/Services and the customer experience (internal and external), and the factors that determine that impact? \n
 no impact is expected.\n
 * No customer impact is anticipated\n

 If the change is being performed during business hours, please provide business justification.\n
 What alerts or customer support calls are expected during this maintenance?  If alerts are to be disabled, what is the plan to have them re-enabled after the maintenance, including any systems in maintenance mode in Spectrum?\n
 * Change will be performed in IST time which will be non-business hours for QBO customers\n

 What is your communication plan?  Please include how the start, end, and updates regarding the maintenance will be communicated and who specifically will be included.\n
 * The updates will be communicated via Slack channels #sbseg-adv-sitroom and #sbseg-qboav-dev.\n

 Additional Details:\n
 * Release Playbook- https://wiki.intuit.com/pages/viewpage.action?spaceKey=QBOEN&title=Workflow+Platform+-+Release+Playbook\n
 * Wavefront dashboard - https://intuit.wavefront.com/dashboards/QBO-AV-Workflow-Automation-Service-v2\n
 * Splunk dashboard - https://sbg.splunk.intuit.com/en-GB/app/intu_sbg_qbo_search/workflow_automation_service__fcis\n
 * ArgoCD SLA - https://qbo.argocd.tools-k8s-prd.a.intuit.com/applications/appintgwkflw-wkflatmnsvc-usw2-prd\n
 * ArgoCD SLB - https://qbo.argocd.tools-k8s-prd.a.intuit.com/applications/appintgwkflw-wkflatmnsvc-usw2-prdb\n
 * ArgoCD SLB East - https://qbo.argocd.tools-k8s-prd.a.intuit.com/applications/appintgwkflw-wkflatmnsvc-use2-prdb"
