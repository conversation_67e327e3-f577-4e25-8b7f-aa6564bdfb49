
populateDbParams(){
eval DB_CLUSTER_ID=\$config_"$app"_"$env"_"$region"_clusterId
eval AWS_REGION=\$config_"$app"_"$env"_"$region"_region
eval DB_NAME=\$config_"$app"_"$env"_"$region"_dbname
eval BASTION_HOST=\$config_"$app"_"$env"_"$region"_bastionhost
eval WAS_DB_USER_WRITE=\$config_"$app"_"$env"_"$region"_iamwriteuser
eval WAS_DB_USER_READ=\$config_"$app"_"$env"_"$region"_iamreaduser

if [ -z "${DB_PORT}" ]
then
  eval DB_PORT=\$config_"$app"_"$env"_"$region"_port
  export DB_PORT
fi
}

checkawscli(){
if [[ $(command -v aws) == "" ]]; then
    echo "Installing aws cli"
    curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
 	sudo installer -pkg AWSCLIV2.pkg -target /
 	aws --version
else
    echo "aws cli Already installed."
    aws --version
fi
}

# execute aws temporary key for pre-prod and prod
generateAWSTemporaryKey()
{
if [ $env == "PRODA" ] || [ $env == "PRODB" ]
then
  echo ":: Please enter ldap username password ::" && eiamCli login && eiamCli getAWSTempCredentials -a 756818480257 -r PowerUser -p prd && export AWS_PROFILE=prd
else
  echo ":: Please enter ldap username password ::" && eiamCli login && eiamCli getAWSTempCredentials -a 862299615669 -r PowerUser -p ppd && export AWS_PROFILE=ppd
fi
}

validateDbParams(){
if [ -z "${DB_CLUSTER_ID}" ]
then
  echo "Unable to resolve DB_CLUSTER_ID. Make sure that DB_CLUSTER_ID (${DB_CLUSTER_ID}) is correct and have a valid Olympus/AWS session."
  exit 1
elif [ -z "${AWS_REGION}" ]
then
  echo "Unable to resolve AWS_REGION. Make sure that AWS_REGION (${DB_CLUSTER_ID}) is correct and have a valid Olympus/AWS session."
  exit 1
elif [ -z "${accessType}" ]
then
  echo "Invalid accessType selection, please rerun and input READ/WRITE"
  exit 1
elif [ -z "${DB_PORT}" ]
then
  echo "Invalid DB_PORT selection, check if config is correct."
  exit 1
fi
}



validateUser () {
 if [ -z "${WAS_DB_USER}" ]
 then
   echo "Invalid WAS_DB_USER, check if config is correct."
   exit 1 
 fi
}

getRdsHost(){
if [ ! -z "$accessType" ] && [ $accessType == "READ" ]
then
  echo "Generating Reader DB password"
  export WAS_DB_USER=${WAS_DB_USER_READ}
  validateUser
  export AWS_REGION=${AWS_REGION}
  export DB_CLUSTER_ID=${DB_CLUSTER_ID}
  export RDSHOST=$( \
    aws rds describe-db-clusters \
      --db-cluster-identifier ${DB_CLUSTER_ID} \
      --query "DBClusters[0].ReaderEndpoint" \
      --region ${AWS_REGION} \
      --output text \
  )
  
#aws rds describe-db-clusters --db-cluster-identifier ${DB_CLUSTER_ID}  --query 'DBClusters[].[IAMDatabaseAuthenticationEnabled]' --output table
  
elif [ ! -z "$accessType" ] && [ $accessType == "WRITE" ]
then
  echo "Generating Writer DB password"
  export WAS_DB_USER=${WAS_DB_USER_WRITE}
  validateUser
  export AWS_REGION=${AWS_REGION}
  export DB_CLUSTER_ID=${DB_CLUSTER_ID}
  export RDSHOST=$( \
    aws rds describe-db-clusters \
      --db-cluster-identifier ${DB_CLUSTER_ID} \
      --query "DBClusters[0].Endpoint" \
      --region ${AWS_REGION} \
      --output text \
  )
fi

if [ -z "${RDSHOST}" ]
then
  echo "Unable to resolve RDSHOST. Make sure that DB_CLUSTER_ID (${DB_CLUSTER_ID}) is correct and have a valid Olympus/AWS session."
  exit 1
fi
}

generatePwd(){
export WAS_DB_PWD=$(aws rds generate-db-auth-token \
  --hostname ${RDSHOST} \
  --port ${DB_PORT} \
  --region ${AWS_REGION} \
  --username ${WAS_DB_USER})
  
echo ${WAS_DB_PWD} | tr -d '\n' | pbcopy
}

outputDetails(){
echo "$(tput setaf 3):::::::::::::::::: Details ::::::::::::::::::$(tput sgr 0)"
echo "$(tput setaf 13) RDSHOST: $(tput setaf 10)  ${RDSHOST}"
echo "$(tput setaf 13) username: $(tput setaf 10) ${WAS_DB_USER}"
echo "$(tput setaf 13) Password: $(tput setaf 10) password is copied to clipboard and is also set in \${WAS_DB_PWD} environment variable."
echo "$(tput setaf 13) PORT: $(tput setaf 10)     ${DB_PORT}"
echo "$(tput setaf 13) REGION: $(tput setaf 10)   ${AWS_REGION} $(tput sgr 0)"
echo "$(tput setaf 13) DB-NAME: $(tput setaf 10)  ${DB_NAME} $(tput sgr 0)"
echo "$(tput setaf 13) Bastion-Host: $(tput setaf 10)  ${BASTION_HOST} $(tput sgr 0)"
echo "$(tput setaf 3):::::::::::::::::: ...... ::::::::::::::::::$(tput sgr 0)"

echo "You can now use workbench to connect to RDS using these credentials or Try running the following command."
echo "$(tput setaf 13) psql -u ${WAS_DB_USER} -h ${RDSHOST} -p ${DB_PORT} dbname=${DB_NAME}"
echo ""
}