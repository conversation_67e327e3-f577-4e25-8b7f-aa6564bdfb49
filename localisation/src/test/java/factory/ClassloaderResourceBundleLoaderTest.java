package factory;

import static java.util.ResourceBundle.Control.TTL_NO_EXPIRATION_CONTROL;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE;
import java.io.IOException;
import java.util.Locale;
import java.util.ResourceBundle;
import org.junit.Assert;
import org.junit.Before;

import org.junit.Test;

public class ClassloaderResourceBundleLoaderTest {

  private ClassloaderResourceBundleLoader classloaderResourceBundleLoader;
  private final Locale CANADA_FRENCH_LOCALE = new Locale("fr", "ca");

  @Before
  public void init() {
    classloaderResourceBundleLoader = new ClassloaderResourceBundleLoader();
  }

  @Test
  public void getTypeShouldReturnClassloaderBundleType() {
    Assert.assertTrue(
        classloaderResourceBundleLoader
            .getType()
            .equals(ResourceBundleLoaderType.CLASSLOADER_BUNDLE_TYPE));
  }

  @Test
  public void needReloadsWithAnyLocaleAndAnyBundlePathShouldReturnFalse() {
    // no reload in case of classloader
    Assert.assertFalse(
        classloaderResourceBundleLoader.needsReload("ANY_BASE_PATH", Locale.ROOT, 0L));
  }

  @Test
  public void needReloadsWithAnyLocaleAndAEmptyBundlePathShouldReturnFalse() {
    // no reload in case of classloader
    Assert.assertFalse(classloaderResourceBundleLoader.needsReload("", Locale.ROOT, 0L));
  }

  @Test
  public void needReloadsWithAnyLocaleAndANullBundlePathShouldReturnFalse() {
    // no reload in case of classloader
    Assert.assertFalse(classloaderResourceBundleLoader.needsReload(null, Locale.ROOT, 0L));
  }

  @Test
  public void testGetBundleSeparator() {
    Assert.assertTrue(classloaderResourceBundleLoader.getBundleSeparator() == '/');
  }

  @Test
  public void getTimeToLiveShouldReturnNoExpirationControl() {
    Assert.assertTrue(classloaderResourceBundleLoader.getTimeToLive() == TTL_NO_EXPIRATION_CONTROL);
  }

  @Test
  public void testResourceNameWithCanadaFrenchLocale() {
    String resourceName = "templates/sla/templateName/nls/fr_CA/fileName_fr_CA.properties";
    Assert.assertTrue(
        resourceName.equals(
            classloaderResourceBundleLoader.getResourceName(
                "templates.sla.templateName.nls.fileName", CANADA_FRENCH_LOCALE)));
  }

  @Test
  public void testResourceNameWithEmptyLocale() {
    String resourceName =
        "templates/sla/templateName/nls/"
            + RESOURCE_BUNDLE.DEFAULT_FOLDER_NAME
            + "/fileName.properties";
    Assert.assertTrue(
        resourceName.equals(
            classloaderResourceBundleLoader.getResourceName(
                "templates.sla.templateName.nls.fileName", Locale.ROOT)));
  }

  @Test
  public void testResourceNameWithEmptyPath() {
    Assert.assertEquals("", classloaderResourceBundleLoader.getResourceName("", Locale.ROOT));
  }

  @Test
  public void testResourceNameWithNullPath() {
    Assert.assertNull(classloaderResourceBundleLoader.getResourceName(null, Locale.ROOT));
  }

  @Test
  public void testResourceBundleWithCanadaFrenchLocale() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle(
            "templates.sla.CustomWorkflowTemplate.nls.workflowContent", CANADA_FRENCH_LOCALE);
    Assert.assertNotNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithEmptyBasePath() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle("", Locale.ROOT);
    Assert.assertNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithNullBasePath() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle(null, Locale.ROOT);
    Assert.assertNull(resourceBundle);
  }

  @Test
  public void testResourceBundleWithEmptyLocale() throws IOException {
    ResourceBundle resourceBundle =
        classloaderResourceBundleLoader.getResourceBundle(
            "templates.sla.CustomWorkflowTemplate.nls.workflowContent", Locale.ROOT);
    Assert.assertNotNull(resourceBundle);
  }
}
