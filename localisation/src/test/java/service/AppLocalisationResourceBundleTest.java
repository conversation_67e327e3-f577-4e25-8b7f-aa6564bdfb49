package service;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.LocalisationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.LocalizedResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.AppLocalisationResourceBundle;
import java.util.Locale;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class AppLocalisationResourceBundleTest {

  private ClassloaderResourceBundleLoader classloaderResourceBundleLoader;
  private AppLocalisationResourceBundle appLocalisationResourceBundle;
  private LocalizedResourceBundle localizedResourceBundle;
  private LocalisationConfig localisationConfig;

  private final String KEY = "invoice.reminder.sendPushNotification.Subject";
  private final String INVALID_KEY = "invoice.reminder.sendPushNotification.Subject_INVALID";
  private final String CANADA_FRENCH_LOCALE = "fr_CA";
  private final String TEMPLATE_NAME = "CustomWorkflowTemplate";
  private final String TEMPLATE_NAME_INVALID = "CustomWorkflowTemplate_INVALID";
  private final String FILE_NAME = "workflowContent";

  @Before
  public void init() {
    localisationConfig = new LocalisationConfig();
    localisationConfig.setBasePath("templates.sla.");
    localisationConfig.setFolderName(TEMPLATE_NAME);
    localisationConfig.setFileName(FILE_NAME);
    classloaderResourceBundleLoader = new ClassloaderResourceBundleLoader();
    localizedResourceBundle = new LocalizedResourceBundle();
    appLocalisationResourceBundle = new AppLocalisationResourceBundle(localizedResourceBundle,localisationConfig);
    ResourceBundleLoaderFactory.addLoader(
        classloaderResourceBundleLoader.getType(), classloaderResourceBundleLoader);
  }

  @Test
  public void testStringWithDefaultTemplateNameAndFileNameAndDefaultLocaleAndValidKey() {
    Assert.assertNotNull(appLocalisationResourceBundle.getString(KEY));
  }

  @Test
  public void
      testStringWithDefaultTemplateNameAndFileNameAndDefaultLocaleAndInvalidKeyThrowsLKNPE() {
    try {
      appLocalisationResourceBundle.getString(INVALID_KEY);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithDefaultTemplateNameAndFileNameAndValidKeyAndCanadaFrenchLocale() {
    Assert.assertNotNull(appLocalisationResourceBundle.getString(KEY, CANADA_FRENCH_LOCALE));
  }

  @Test
  public void
      getStringWithDefaultTemplateNameAndFileNameAndInvalidKeyAndCanadaFrenchLocaleThrowsLKNPE() {
    try {
      appLocalisationResourceBundle.getString(INVALID_KEY, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }


  @Test
  public void testStringWithDefaultFileNameAndTemplateNameAndValidKeyAndCanadaFrenchLocale() {
    Assert.assertNotNull(appLocalisationResourceBundle.getString(KEY, TEMPLATE_NAME, CANADA_FRENCH_LOCALE));
  }

  @Test
  public void
      testStringWithDefaultFileNameAndTemplateNameAndInvalidKeyAndCanadaFrenchLocaleThrowsLKNPE() {
    try {
      appLocalisationResourceBundle.getString(INVALID_KEY, TEMPLATE_NAME, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void
      testStringWithDefaultFileNameAndInvalidTemplateNameAndValidLocaleAndCanadaFrenchLocaleThrowsInputInvalidError() {
    try {
      appLocalisationResourceBundle.getString(KEY, TEMPLATE_NAME_INVALID, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.RESOURCE_BUNDLE_NOT_FOUND, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithTemplateNameAndFileNameAndValidKeyAndCanadaFrenchLocale() {
    Assert.assertNotNull(
        appLocalisationResourceBundle.getString(KEY, TEMPLATE_NAME, FILE_NAME, CANADA_FRENCH_LOCALE));
  }

  @Test
  public void testStringWithTemplateNameAndFileNameAndInValidKeyAndCanadaFrenchLocaleThrowsLKNPE() {
    try {
      appLocalisationResourceBundle.getString(INVALID_KEY, TEMPLATE_NAME, FILE_NAME, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void
      testStringWithEmptyTemplateNameAndFileNameAndValidKeyAndCanadaFrenchLocaleThrowsInputInvalid() {
    try {
      appLocalisationResourceBundle.getString(KEY, "", FILE_NAME, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void
      testStringWithTemplateNameAndEmptyFileNameAndValidKeyAndCanadaFrenchLocaleThrowsInputInvalid() {
    try {
      appLocalisationResourceBundle.getString(KEY, TEMPLATE_NAME, "", CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithValidBundlePathAndKeyAndFrenchCanadaLocale() {
    String bundlePath = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
    Assert.assertNotNull(appLocalisationResourceBundle.getString(bundlePath, KEY, new Locale("fr", "ca")));
  }

  @Test
  public void testStringWithEmptyBundlePathAndKeyAndFrenchCanadaLocaleThrowsInputInvalid() {
    try {
      appLocalisationResourceBundle.getString("", KEY, new Locale("fr", "ca"));
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithBundlePathAndEmptyKeyAndFrenchCanadaLocaleThrowsInputInvalid() {
    String bundlePath = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
    try {
      appLocalisationResourceBundle.getString(bundlePath, "", new Locale("fr", "ca"));
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithBundlePathAndKeyAndNullLocaleThrowsInputInvalid() {
    String bundlePath = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
    try {
      appLocalisationResourceBundle.getString(bundlePath, KEY, (Locale) null);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithInvalidTemplateNameThrowsResourceBundleNotFoundError() {
    try {
      appLocalisationResourceBundle.getString(KEY, TEMPLATE_NAME_INVALID, FILE_NAME, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.RESOURCE_BUNDLE_NOT_FOUND, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void
      testStringWithValidBundlePathAndKeyAndFrenchCanadaLocaleKeyMissingInFrenchCanadaLocaleThrowsLKNPE() {
    String bundlePath = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
    try {
      appLocalisationResourceBundle.getString(
          bundlePath, "bill.notification.sendNotification.Subject", new Locale("fr", "ca"));
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void
      testStringWithValidBundlePathAndKeyAndEnUSLocaleKeyMissingInEnUSLocaleAndPresentInDefaultLocale() {
    String bundlePath = "templates.sla.CustomWorkflowTemplate.nls.workflowContent";
    Assert.assertNotNull(
        appLocalisationResourceBundle.getString(
            bundlePath, "present.in.default.folder.only", new Locale("en", "US")));
  }

  @Test
  public void getBundleWithValidPathAndCanadaFrenchLocale() {
    Assert.assertNotNull(
        appLocalisationResourceBundle.getBundle(
            "templates.sla.CustomWorkflowTemplate.nls.workflowContent", new Locale("fr", "ca")));
  }

  @Test
  public void getBundleWithInvalidBundlePath() {
    Assert.assertNull(
        appLocalisationResourceBundle.getBundle(
            "templates.sla.CustomWorkflowTemplate.nls.workflowContent_INVALID",
            new Locale("fr", "ca")));
  }
}
