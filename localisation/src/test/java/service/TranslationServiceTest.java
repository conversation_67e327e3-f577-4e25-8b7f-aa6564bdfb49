package service;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.ExternalServiceMapping;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.LocalisationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ClassloaderResourceBundleLoader;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.HelpVariableRecordTypeTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.RecordTypeTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.factory.ResourceBundleLoaderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.handlers.UrlTokenHandler;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.LocalizedResourceBundle;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.AppLocalisationResourceBundle;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class TranslationServiceTest {
  TranslationService translationService = null;
  LocalisationConfig localisationConfig;
  private final String CANADA_FRENCH_LOCALE = "fr_CA";
  private final String recordType = "Invoice";
  private final String KEY = "~invoice.reminder.sendPushNotification.Subject";
  private final String INVALID_KEY = "~invoice.reminder.sendPushNotification.Subject_INVALID";
  private final String KEY_WITH_TOKEN_PARAMETER = "~invoice.approval.sendCompanyEmail.Message";

  @Before
  public void init() {
    localisationConfig = new LocalisationConfig();
    localisationConfig.setBasePath("templates.sla.");
    localisationConfig.setFolderName("CustomWorkflowTemplate");
    localisationConfig.setFileName("workflowContent");
    ClassloaderResourceBundleLoader classloaderResourceBundleLoader =
        new ClassloaderResourceBundleLoader();
    ResourceBundleLoaderFactory.addLoader(
        classloaderResourceBundleLoader.getType(), classloaderResourceBundleLoader);
    AppLocalisationResourceBundle appLocalisationResourceBundle =
        new AppLocalisationResourceBundle(new LocalizedResourceBundle(), localisationConfig);
    ExternalServiceMapping externalServiceMapping = new ExternalServiceMapping();
    externalServiceMapping.setUrls(
        Map.of("taskManagerUrl", "https://app.qal.qbo.intuit.com/app/taskmanager"));
    translationService =
        new TranslationService(
                appLocalisationResourceBundle,
            List.of(
                new UrlTokenHandler(externalServiceMapping),
                new RecordTypeTokenHandler(appLocalisationResourceBundle),
                new HelpVariableRecordTypeTokenHandler()));
    translationService.setTokenHandlerMap();
  }

  @Test
  public void testStringWithValidKeyAndFrenchCanadaLocale() {
    Assert.assertNotNull(translationService.getString(KEY, CANADA_FRENCH_LOCALE));
  }

  @Test
  public void getStringWithInvalidKeyAndFrenchCanadaLocaleThrowsLKNP() {
    try {
      translationService.getString(INVALID_KEY, CANADA_FRENCH_LOCALE);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithEmptyKeyAndFrenchCanadaLocale() {
    Assert.assertEquals("", translationService.getString("", CANADA_FRENCH_LOCALE));
  }

  @Test
  public void testStringWithNullKeyAndFrenchCanadaLocale() {
    Assert.assertEquals(null, translationService.getString(null, CANADA_FRENCH_LOCALE));
  }

  @Test
  public void testStringWithWrongFormatKeyKeyAndFrenchCanadaLocale() {
    Assert.assertEquals("abc", translationService.getString("abc", CANADA_FRENCH_LOCALE));
  }

  @Test
  public void getStringWithValidKeyAndEmptyLocaleMustReturnDefaultKeyLocaleValue() {
    Assert.assertNotNull(translationService.getString(KEY, ""));
  }

  @Test
  public void getStringWithValidKeyAndNullLocaleReturnDefaultKeyLocaleValue() {
    Assert.assertNotNull(translationService.getString(KEY, null));
  }

  @Test
  public void testStringWithValidKeyAndFrenchCanadaLocaleWithoutTokenParameter() {
    Assert.assertNotNull(
        translationService.getFormattedString(KEY, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void
      testFormattedStringWithInvalidKeyAndFrenchCanadaLocaleWithoutTokenParameterThrowsLKNP() {
    try {
      translationService.getFormattedString(INVALID_KEY, CANADA_FRENCH_LOCALE, recordType);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(
          LocalisationError.LOCALISED_KEY_NOT_PRESENT, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testFormattedStringWithEmptyKeyAndFrenchCanadaLocaleWithoutTokenParameter() {
    Assert.assertEquals(
        "", translationService.getFormattedString("", CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormattedStringWithNullKeyAndFrenchCanadaLocaleWithoutTokenParameter() {
    Assert.assertEquals(
        null, translationService.getFormattedString(null, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void testFormattedStringWithWrongFormatKeyKeyAndFrenchCanadaLocaleWithoutTokenParameter() {
    Assert.assertEquals(
        "abc", translationService.getFormattedString("abc", CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void
      testFormattedStringWithValidKeyAndEmptyLocaleMustReturnDefaultKeyLocaleValueWithoutTokenParameter() {
    Assert.assertNotNull(translationService.getFormattedString(KEY, "", recordType));
  }

  @Test
  public void
      testFormattedStringWithValidKeyAndNullLocaleReturnDefaultKeyLocaleValueWithoutTokenParameter() {
    Assert.assertNotNull(translationService.getFormattedString(KEY, null, recordType));
  }

  @Test
  public void
      testStringWithValidKeyAndFrenchCanadaLocaleWithoutTokenParameterAndNullRecordTypeThrowsInputInvalid() {
    try {
      translationService.getFormattedString(KEY, CANADA_FRENCH_LOCALE, null);
      Assert.fail();
    } catch (LocalisationGeneralException localisationGeneralException) {
      Assert.assertEquals(LocalisationError.INPUT_INVALID, localisationGeneralException.getLocalisationError());
    }
  }

  @Test
  public void testStringWithValidKeyAndFrenchCanadaLocaleWithTokenParameter() {
    Assert.assertNotNull(
        translationService.getFormattedString(
            KEY_WITH_TOKEN_PARAMETER, CANADA_FRENCH_LOCALE, recordType));
  }

  @Test
  public void
      testFormattedStringWithValidKeyAndEmptyLocaleMustReturnDefaultKeyLocaleValueWithTokenParameter() {
    Assert.assertNotNull(
        translationService.getFormattedString(KEY_WITH_TOKEN_PARAMETER, "", recordType));
  }

  @Test
  public void
      testFormattedStringWithValidKeyAndNullLocaleReturnDefaultKeyLocaleValueWithTokenParameter() {
    Assert.assertNotNull(translationService.getFormattedString(KEY, null, recordType));
  }

  @Test
  public void testFormattedStringWithTokenParameters() {
    Assert.assertEquals(
        "fr_CAHi i am testing token parameters for invoice fr_CAInvoice fr_CAinvoices [[Invoice Number]] https://app.qal.qbo.intuit.com/app/taskmanager",
        translationService.getFormattedString(
            "~token.parameter.key", CANADA_FRENCH_LOCALE, recordType));
  }
  @Test
  public void testFormattedStringWithoutRecordTypeTokenParameters() {
    Assert.assertEquals(
        "fr_CAHi i am testing token parameters for invoice %Record% %record%s [[%Record% Number]] https://app.qal.qbo.intuit.com/app/taskmanager",
        translationService.getFormattedString(
            "~token.parameter.key", CANADA_FRENCH_LOCALE));
  }

  @Test
  public void testGetTokensWithTokensInLocalisedString() {
    Set<String> tokenSet =
        translationService.getTokens("Hi I am testing tokens here {token1}, {token2}");
    Assert.assertEquals(2, tokenSet.size());
    Assert.assertEquals(true, tokenSet.contains("token1"));
    Assert.assertEquals(true, tokenSet.contains("token2"));
  }
  @Test
  public void testGetTokensWithNoTokensInLocalisedString() {
    Set<String> tokenSet =
        translationService.getTokens("Hi I am testing tokens here. ");
    Assert.assertEquals(0, tokenSet.size());
  }

  @Test
  public void testGetTokensWithBlankLocalisedString() {
    Set<String> tokenSet =
        translationService.getTokens(" ");
    Assert.assertEquals(0, tokenSet.size());
  }

  @Test
  public void testGetTokensWithNullLocalisedString() {
    Set<String> tokenSet = translationService.getTokens(null);
    Assert.assertEquals(0, tokenSet.size());
  }


}
