package com.intuit.appintgwkflw.wkflautomate.was.localisation.factory;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.ResourceBundleLoaderType;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/** This class used to load the resource bundle using <code>ClassLoader</Code>. */
@Component
public class ClassloaderResourceBundleLoader implements ResourceBundleLoader {

  /**
   * This method returns the classloader bundle type <code>
   * ResourceBundleLocatorType.CLASSLOADER_BUNDLE_TYPE</code>
   *
   * @return it returns <code>ResourceBundleLocatorType.CLASSLOADER_BUNDLE_TYPE</code>
   */
  @Override
  public ResourceBundleLoaderType getType() {
    return ResourceBundleLoaderType.CLASSLOADER_BUNDLE_TYPE;
  }

  /**
   * This method calls the <code>getResourceName</code> parent method for creating exact bundle
   * path.(Appends locale as folder name before file and as suffix in file name).
   *
   * @param basePath base path of the resource bundle
   * @param locale locale of the resource bundle
   * @return the resource bundle instance, or <code>null</code> if none could be found.
   * @throws IOException If file not found
   */
  @Override
  public ResourceBundle getResourceBundle(String basePath, Locale locale) throws IOException {
    String resourceName = getResourceName(basePath, locale);
    if (StringUtils.isEmpty(resourceName)) {
      return null;
    }
    ClassLoader loader = this.getClass().getClassLoader();
    InputStream inputStream = loader.getResourceAsStream(resourceName);
    if (inputStream == null) {
      return null;
    }
    try (java.io.Reader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8)) {
      return new PropertyResourceBundle(reader);
    }
  }
}
