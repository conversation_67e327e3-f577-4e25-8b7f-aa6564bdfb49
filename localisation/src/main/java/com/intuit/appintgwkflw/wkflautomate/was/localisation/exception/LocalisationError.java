package com.intuit.appintgwkflw.wkflautomate.was.localisation.exception;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationConstants;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public enum LocalisationError {
  OTHER_TEST("WTESTXXX", "Some Error Occurred due to %s", "Some unknown error has occurred",HttpStatus.INTERNAL_SERVER_ERROR),
  UNKNOWN_ERROR(
      "L0001", "Some unknown error occured", "some unknown error occured", HttpStatus.BAD_REQUEST),
  INVALID_LOCALISATION_ERROR_INPUT(
      "L0002",
      "Workflow Error name cannot be null or empty",
      "Workflow Error cannot be null or empty",
      HttpStatus.BAD_REQUEST),
  INPUT_INVALID("L0003", "Input %s cannot be null or empty", LocalisationConstants.INPUT_CANNOT_BE_NULL_OR_EMPTY,
          HttpStatus.BAD_REQUEST),
  RESOURCE_BUNDLE_NOT_FOUND(
          "L0004",
          "Resource bundle for path = %s, locale = %s and key = %s not found",
          "Resource bundle not found",
          HttpStatus.INTERNAL_SERVER_ERROR),
  LOCALISED_KEY_NOT_PRESENT(
          "L0005",
          "Localised Key = %s for path = %s and locale = %s not present",
          "Localised Key not present",
          HttpStatus.INTERNAL_SERVER_ERROR),
  INTERNAL_EXCEPTION("L0006", "Some internal exception Occurred",
          "Some internal exception has occurred", HttpStatus.INTERNAL_SERVER_ERROR);

  private static final Map<String, LocalisationError> workflowErrorMap = getWorkflowErrorMap();
  private final String errorCode;
  private final String errorDescription;
  private final HttpStatus status;

  public void setErrorMessage(final String errorMessage) {
    this.errorMessage = errorMessage;
  }

  private String errorMessage;



  private LocalisationError(
      final String errorCode,
      final String errorMessage,
      final String errorDescription,
      final HttpStatus status) {

    this.errorCode = errorCode;
    this.errorMessage = errorMessage;
    this.errorDescription = errorDescription;
    this.status = status;
  }

  private static Map<String, LocalisationError> getWorkflowErrorMap() {

    final Map<String, LocalisationError> possibleErrorsMap = new HashMap<>();
    for (final LocalisationError localisationError : values()) {
      possibleErrorsMap.put(localisationError.name(), localisationError);
    }
    return possibleErrorsMap;
  }

  public static LocalisationError value(final String type) {
    return workflowErrorMap.getOrDefault(type, UNKNOWN_ERROR);
  }

  public String getErrorCode() {
    return this.errorCode;
  }

  public String getErrorDescription() {
    return this.errorDescription;
  }

  public HttpStatus getStatus() {
    return this.status;
  }

  public String getErrorMessage() {
    return this.errorMessage;
  }
}
