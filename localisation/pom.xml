<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <version>1.1.20</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>was-localisation</artifactId>

    <properties>
    <tomcat-embed-core.version>10.1.19</tomcat-embed-core.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- To Fix CVEs due to embedded tomcat, UCS is using this module, and it is blocking release -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>9.0.99</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>


        <dependency>
            <groupId>com.intuit.appintgwkflw-wkflautomate.workflow-resources</groupId>
            <artifactId>workflow-resources</artifactId>
            <version>${workflows-resources.version}</version>
        </dependency>

    </dependencies>


</project>
