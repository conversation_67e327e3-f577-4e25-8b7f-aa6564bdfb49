package com.intuit.appintgwkflw.wkflautomate.was.entity.slack.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class SlackRequestModel {

  private String channelId;
  private String nextCursor;

  private int limit = 100; // default value

  private String oldest; // start time of message reading

  private String latest; // end time of message reading
}
