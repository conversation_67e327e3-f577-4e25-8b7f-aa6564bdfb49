package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventMetaData {
  private static final String WAS_UUID_PREFIX = "was-notify-";

  /**
   * ISO format.
   */
  public static final String PATTERN = "yyyy-MM-dd'T'hh:mm:ssZ";

  /**
   * event auth id.
   */
  private Long authId;

  /**
   * event creation date.
   */
  private String createdDate;

  /**
   * Intuit transaction Id.
   */
  private String intuitTid;

  // following fields are optional fields.
  // for business applications like QBO instead of authId realmId can be passed.
  /**
   * if this event for realm then realmId is passed.
   */
  private Long realmId;

  /**
   * targetRealmId that will be passed to generate system offline ticket by oinp
   */
  private Long targetRealmId;

  /**
   * Primary identifier that will be passed as part of event meta data
   */
  private Long primaryId;
  /**
   * if caller wants to pass the push notification ios device tokens then it can
   * be passed with this property.
   */
  @RedactSensitiveField
  private List<String> iosDeviceTokens;
  /**
   * if caller wants to pass the push notification to android device tokens then
   * it can be passed with this property.
   */
  @RedactSensitiveField
  private List<String> androidDeviceTokens;
  /**
   * Well-formed IETF BCP 47 language tag representing the locale. Ex: en-US
   * the Locale object from the locale string in the metadata. If there is
   * no locale in the metadata it defaults to en-US in OINP service.
   */
  private String locale;

  public String getIntuitTid(){
    if (StringUtils.isEmpty(this.intuitTid)) {
      this.intuitTid = WAS_UUID_PREFIX + UUID.randomUUID();
    }
    return this.intuitTid;
  }

  public String getCreatedDate(){
    if (StringUtils.isEmpty(this.createdDate)) {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN);
      this.createdDate = simpleDateFormat.format(new Date());
    }
    return this.createdDate;
  }

  public String getLocale() {
    if (Objects.isNull(this.locale)) {
      this.locale = Locale.US.toLanguageTag();
    }
    return this.locale;
  }

  public void setLocale(String locale) {
    if (!ObjectUtils.isEmpty(locale)) {
      this.locale = locale.replace(WorkflowConstants.UNDERSCORE, WorkflowConstants.HYPHEN);
    } else {
      this.locale = Locale.US.toLanguageTag();
    }
  }
}
