package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

/**
 * This enum contains all the workflow names
 */
@Getter
public enum WorkflowNameEnum {
  CUSTOM_REMINDER("customReminder"),
  CUSTOM_SCHEDULED_ACTIONS("customScheduledActions"),
  CUSTOM_UPDATE_ENTITY("customUpdateEntity"),
  CUSTOM_SEND_ENTITY("customSendEntity"),
  //TODO: remove this after complete scheduling migration
  SCHEDULED_ACTIONS("scheduledActions"),
  REMINDER("reminder");
  private String name;

  WorkflowNameEnum(String name) {
    this.name = name;
  }

  public static WorkflowNameEnum fromName(String name) {
    for (WorkflowNameEnum workflowName : WorkflowNameEnum.values()) {
      if (workflowName.name.equals(name)) {
        return workflowName;
      }
    }
    throw new UnsupportedOperationException("The workflow name " + name + " is not supported!");
  }
}
