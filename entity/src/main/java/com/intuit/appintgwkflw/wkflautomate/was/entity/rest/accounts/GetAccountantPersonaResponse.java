package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/** <AUTHOR> */
@Data
@JsonIgnoreProperties
public class GetAccountantPersonaResponse {

  @JsonProperty(value = "accountantFirmAssociation")
  private List<AccountantFirmAssociation> accountantFirmAssociations;
}
