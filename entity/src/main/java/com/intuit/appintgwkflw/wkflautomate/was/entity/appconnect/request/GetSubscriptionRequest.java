package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import lombok.Builder;
import lombok.Getter;

@Getter
public class GetSubscriptionRequest extends AppActionBaseRequest {

  private String intuitAppId;

  private String companyId;

  @Builder
  public GetSubscriptionRequest(String endpoint, String intuitAppId, String companyId) {
    super(endpoint);
    this.intuitAppId = intuitAppId;
    this.companyId = companyId;
  }
}
