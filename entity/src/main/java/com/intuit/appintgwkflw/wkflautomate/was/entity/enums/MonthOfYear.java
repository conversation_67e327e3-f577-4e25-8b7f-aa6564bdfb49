package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum MonthOfYear {
  JANUARY("JANUARY", 1),
  FEBRUARY("FEBRUARY", 2),
  MARCH("MARCH", 3),
  APRIL("APRIL", 4),
  MAY("MAY", 5),
  JUNE("JUNE", 6),
  JULY("JULY", 7),
  AUGUST("AUGUST", 8),
  SEPTEMBER("SEPTEMBER", 9),
  OCTOBER("OCTOBER", 10),
  NOVEMBER("NOVEMBER", 11),
  DECEMBER("DECEMBER", 12);

  private final String name;
  private final int index;
  private static Map<String, Integer> monthOfYearIndexMap;

  private static Map<String, Integer> monthOfYearNameIndexMapping() {
    if (ObjectUtils.isEmpty(monthOfYearIndexMap)) {
      monthOfYearIndexMap = new HashMap<>();
      for (MonthOfYear s : values()) {
        monthOfYearIndexMap.put(s.name, s.index);
      }
    }
    return monthOfYearIndexMap;
  }

  public static int getMonthOfYearIndex(String monthOfYear) {
    return monthOfYearNameIndexMapping().get(monthOfYear);
  }
}
