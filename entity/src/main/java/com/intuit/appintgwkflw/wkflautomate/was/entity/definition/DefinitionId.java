package com.intuit.appintgwkflw.wkflautomate.was.entity.definition;

import lombok.Builder;
import lombok.Getter;

import java.text.MessageFormat;

/**
 * Pojo containing the Id object to be set into bpmn object
 */
@Builder(toBuilder = true)
@Getter
public class DefinitionId {
  private String realmId;
  private String uniqueId;
  private String entityId;

  @Override
  public String toString() {
    return MessageFormat.format("{0}_{1}_{2}", entityId, realmId, uniqueId);
  }
}
