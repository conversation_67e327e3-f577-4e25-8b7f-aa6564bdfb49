package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;

import java.util.Set;
import lombok.Data;

/**
 * This class stores the Entity Set for a particular Entity Type.
 * for example: Invoice has a set of Attributes which are enabled for it, the default attributes for it.
 * Along with help variables which are used in the workflow.
 * */
@Data
public class EntitySet {

   private String id;
   private String source;
   private Set<String> attributes;
   private Set<String> defaultAttributes;
   private Set<String> helpVariables;
}
