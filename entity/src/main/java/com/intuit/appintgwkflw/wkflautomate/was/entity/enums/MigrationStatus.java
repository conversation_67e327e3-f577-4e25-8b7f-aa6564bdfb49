package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum MigrationStatus {
  COMPLETED("completed");

  private String status;

  MigrationStatus(String status) {
    this.status = status;
  }

  @Override
  public String toString() {
    return status;
  }

  public static MigrationStatus fromType(String type) {
    for (MigrationStatus migrationStatus : MigrationStatus.values()) {
      if (migrationStatus.getStatus().equals(type)) {
        return migrationStatus;
      }
    }
    return null;
  }
}
