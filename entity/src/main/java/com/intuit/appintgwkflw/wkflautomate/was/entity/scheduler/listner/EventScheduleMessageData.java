package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventScheduleMessageData {
  private String scheduleId;
  private String requestId;
  private String messageId;

  /**
   * Allowed values can be found in com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.Scope
   */
  private String scope;
}
