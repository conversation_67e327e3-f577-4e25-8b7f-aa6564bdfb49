package com.intuit.appintgwkflw.wkflautomate.telemetry.logger;

import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * prepare the logging format for the workflow logging.
 *
 * <p>Logging format : key1=value1 key2=value2
 *
 * <AUTHOR>
 */
public class SimpleLoggingFormat implements WorkflowLoggingFormat {

  @Override
  public String format(final Map<String, String> loggingParams) {

    if (CollectionUtils.isEmpty(loggingParams)) {
      return null;
    }

    return loggingParams
        .entrySet()
        .stream()
        .map(Object::toString)
        .collect(Collectors.joining(" "));
  }
}
