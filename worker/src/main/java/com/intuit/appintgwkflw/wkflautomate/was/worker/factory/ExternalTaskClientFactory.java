package com.intuit.appintgwkflw.wkflautomate.was.worker.factory;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.AUTHORIZATION_HEADER;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Client;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.camunda.bpm.client.ExternalTaskClient;
import org.camunda.bpm.client.ExternalTaskClientBuilder;
import org.camunda.bpm.client.backoff.BackoffStrategy;

import java.util.Optional;

/**
 * A factory for creating {@link ExternalTaskClient}.
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExternalTaskClientFactory {

  /**
   * Create external task client.
   *
   * @param client the client
   * @param worker the worker
   * @param offlineTicketClient the offline ticket client
   * @return the external task client
   */
  public static ExternalTaskClient createExternalTaskClient(
      final Client client,
      final Worker worker,
      final OfflineTicketClient offlineTicketClient,
      final String endPoint,
      final WASContextHandler contextHandler) {

    // prepare back-off strategy for external task
    WorkflowVerfiy.verifyNull(
        client.getBackoffStrategyName(), WorkflowError.INPUT_INVALID, "backoffStrategyName");

    // Left -> has the global properties and Right ->  will have the new properties to override
    final ImmutablePair<Client, Optional<Client>> clientPair =
        new ImmutablePair<>(client, Optional.ofNullable(worker.getClient()));

    final BackoffStrategy backoffStrategy =
        clientPair.getRight().isPresent()
            ? clientPair.getRight().get().getBackoffStrategyName().getBackoffStrategy(worker,clientPair)
            : clientPair.getLeft().getBackoffStrategyName().getBackoffStrategy(worker,clientPair);

    // prepare external task builder object
    final ExternalTaskClientBuilder externalTaskClientBuilder =
        ExternalTaskClient.create()
            .baseUrl(endPoint)
            .maxTasks(worker.getMaxTasks())
            .backoffStrategy(backoffStrategy);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className("ExternalTaskClientFactory")
                .message(
                    "Worker details for topicName=%s, maxTasks=%s, lockDuration=%s",
                    worker.getTopicName(), worker.getMaxTasks(), worker.getLockDuration())
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK));

    //  WorkerId needs to be same, when eventing is enabled(Hence Lock extension). This value will
    // be used to extend the lock
    if (StringUtils.isNotEmpty(worker.getWorkerId())) {
      //    All Workers  for this topic will have the same name.
      externalTaskClientBuilder.workerId(worker.getWorkerId());
    }

    // if back off is disabled then disable back off in external task and set async timeout
    if (ObjectUtils.isNotEmpty(worker.getClient())
            && ObjectUtils.isNotEmpty(worker.getClient().getBackOffDisable())
        ? worker.getClient().getBackOffDisable()
        : client.getBackOffDisable()) {
      externalTaskClientBuilder.disableBackoffStrategy();
      WorkflowVerfiy.verifyNull(
          worker.getAsyncResponseTimeout(), WorkflowError.ASYNC_RESPONSE_TIMEOUT);
      externalTaskClientBuilder.asyncResponseTimeout(worker.getAsyncResponseTimeout());
    }

    // fetching offline ticket and setting it in AUTHORIZATION header
    externalTaskClientBuilder.addInterceptor(
        requestContext -> {
          requestContext.addHeader(
                  AUTHORIZATION_HEADER, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
          WorkflowLogger.info(
              () ->
                  WorkflowLoggerRequest.builder()
                      .className("ExternalTaskClientFactory")
                      .message(
                          "Initiating Fetch n lock for topic=%s, offeringId=%s",
                          worker.getTopicName(), worker.getOfferingId())
                      .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                      .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK));
        });

    // Bootstraps the Camunda client
    return externalTaskClientBuilder.build();
  }
}
