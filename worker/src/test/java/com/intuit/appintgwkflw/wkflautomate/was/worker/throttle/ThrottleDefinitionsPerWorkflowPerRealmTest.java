package com.intuit.appintgwkflw.wkflautomate.was.worker.throttle;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleServiceHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleDefinitionsPerWorkflowPerRealmTest {
  @Mock
  private ProceedingJoinPoint proceedingJoinPoint;
  @Mock private ThrottleConfigs throttleConfigs;
  @Mock private ThrottleHelper throttleHelper;
  @Mock private DefinitionInstance definitionInstance;
  private final String realmId = "1234567890";
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @InjectMocks
  @Spy private ThrottleDefinitionsPerWorkflowPerRealm throttleHandler;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(throttleHandler, "throttleHelper", throttleHelper);
    ReflectionTestUtils.setField(throttleHandler, "throttleConfigs", throttleConfigs);
  }

  @Test
  public void testGetAttribute() {
    Assert.assertEquals(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM, throttleHandler.getAttribute());
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingNotPresent() throws Throwable {
    ThrottleServiceHandlers.addHandler(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM, throttleHandler);
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());

    boolean response = throttleHandler.canContinueExecution(proceedingJoinPoint);

    Assert.assertTrue(response);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    Mockito.verify(definitionInstance, Mockito.times(0)).getTemplateDetails();
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingDisabled() throws Throwable {
    ThrottleServiceHandlers.addHandler(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM, throttleHandler);
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    boolean response = throttleHandler.canContinueExecution(proceedingJoinPoint);

    Assert.assertTrue(response);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    Mockito.verify(definitionInstance, Mockito.times(0)).getTemplateDetails();
  }

  @Test
  public void testIsThrottlingEnabled_WhenThrottlingEnabledForDefnPerWorkflowPerRealmInConfigButBypassedLater() {
    when(throttleConfigs.isDefinitionsPerWorkflowPerRealm()).thenReturn(true);
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(false);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
  }

  @Test
  public void getDefinitionCount_FromDB() {
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
    Mockito.verify(definitionDetailsRepository).getCountOfDefinitionsPerWorkflowPerRealm(Mockito.eq("customTestTemp"), Mockito.eq(Long.valueOf(realmId)));
    Assert.assertEquals("customTestTemp", response.getRight());
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingEnabled_ErrorBreached() throws Throwable {
    when(throttleConfigs.isDefinitionsPerWorkflowPerRealm()).thenReturn(true);
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionDetailsRepository.getCountOfDefinitionsPerWorkflowPerRealm(Mockito.eq("customTestTemp"), Mockito.eq(Long.valueOf(realmId)))).thenReturn(3);
    when(throttleHelper.isThrottlingEnabledForWorkflow("customTestTemp", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)).thenReturn(true);
    when(throttleHelper.getThreshold("customTestTemp", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)).thenReturn(3);

    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      throttleHandler.canContinueExecution(proceedingJoinPoint);
    });

    String expectedMessage = String.format(WorkflowError.DEFINITIONS_PER_WORKFLOW_PER_REALM_THRESHOLD_BREACHED.getErrorMessage(), "customTestTemp", realmId);
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
    Mockito.verify(proceedingJoinPoint, Mockito.times(0)).proceed();
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingEnabled_WarnBreached() throws Throwable {
    Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
    throttleWarnDiffMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM, 2);

    when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);
    when(throttleConfigs.isDefinitionsPerWorkflowPerRealm()).thenReturn(true);
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionDetailsRepository.getCountOfDefinitionsPerWorkflowPerRealm(Mockito.eq("customTestTemp"), Mockito.eq(Long.valueOf(realmId)))).thenReturn(2);
    when(throttleHelper.isThrottlingEnabledForWorkflow("customTestTemp", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)).thenReturn(true);
    when(throttleHelper.getThreshold("customTestTemp", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)).thenReturn(4);

    boolean response = throttleHandler.canContinueExecution(proceedingJoinPoint);
    Assert.assertTrue(response);
    Mockito.verify(throttleHandler).executeWarn(proceedingJoinPoint, 3);
  }

  @Test
  public void testExecuteWarn() {
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());

    try {
      throttleHandler.executeWarn(proceedingJoinPoint, 15);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExecuteFailure() {
    when(definitionInstance.getTemplateDetails()).thenReturn(getTemplateDetails("customTestTemp"));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());

    Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
      throttleHandler.executeFailure(proceedingJoinPoint, 15);
    });

    String expectedMessage = String.format(WorkflowError.DEFINITIONS_PER_WORKFLOW_PER_REALM_THRESHOLD_BREACHED.getErrorMessage(), "customTestTemp", realmId);
    String actualMessage = exception.getMessage();
    assertTrue(actualMessage.contains(expectedMessage));
  }

  @Test
  public void testGetWarnDiff_WarnConfigPresent() {
    Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
    throttleWarnDiffMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM, 10);

    when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);
    Assert.assertEquals((Integer) 10, throttleHandler.getWarnDiff());
  }

  @Test
  public void testGetWarnDiff_WarnConfigNotPresent() {
    Assert.assertEquals((Integer) 0, throttleHandler.getWarnDiff());
  }


  private TemplateDetails getTemplateDetails(String tempName) {
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName(tempName);
    return templateDetails;
  }

  private Object[] joinPointArgs() {
    Object[] args = new Object[3];
    args[0] = definitionInstance;
    args[1] = realmId;
    return args;
  }

}