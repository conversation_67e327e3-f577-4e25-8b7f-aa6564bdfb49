package com.intuit.appintgwkflw.wkflautomate.was;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import javax.servlet.http.HttpServletRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/rest")
public class MockController {

  public static boolean serverDown = false;
  public static boolean serverDownNonRetry = false;
  
  public static int count = 0;

  @GetMapping(path = "/health/mock/deploy")
  public ResponseEntity deployDefn(HttpServletRequest httpServletRequest) {
    return new ResponseEntity<>("All Ok", HttpStatus.OK);
  }

  @PostMapping(path = "/decision-definition/{id}/evaluate")
  public ResponseEntity decisionDefinition(HttpServletRequest httpServletRequest) {
    count++;
    if (serverDown) {
      WorkflowLogger.logInfo("Server is down:" + count);
      return new ResponseEntity<>(new Exception(), HttpStatus.INTERNAL_SERVER_ERROR);
    
    } else if (serverDownNonRetry) {
      WorkflowLogger.logInfo("Server is down with non retriable excption");
      return new ResponseEntity<>(new Exception(), HttpStatus.NOT_IMPLEMENTED);
    
    } else {
      WorkflowLogger.logInfo("Server is UP");
      return new ResponseEntity<>(
          "[\n"
              + "  {\n"
              + "    \"result\": { \"value\" : \"management\", \"type\" : \"String\", \"valueInfo\" : null }\n"
              + "  }\n"
              + "]",
          HttpStatus.OK);
    }
  }
}
