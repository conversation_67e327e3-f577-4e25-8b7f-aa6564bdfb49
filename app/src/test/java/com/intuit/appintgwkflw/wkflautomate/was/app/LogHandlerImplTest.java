package com.intuit.appintgwkflw.wkflautomate.was.app;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.internal.verification.VerificationModeFactory.atLeast;

@RunWith(MockitoJUnitRunner.class)
public class LogHandlerImplTest {

  @InjectMocks private WASContextHandler contextHandler = new MDCContextHandler();

  @Mock private WASContextHandler contextHandlerImpl;

  @Test
  public void testAddKey() {
    contextHandler.addKey(WASContextEnums.OWNER_ID, "REALM-ID");
    assertEquals("REALM-ID", contextHandler.get(WASContextEnums.OWNER_ID));
  }

  @Test
  public void testLogTime() {
    contextHandlerImpl.logTime("method-name", 1000);
    contextHandlerImpl.logTime("method-name", 1000);
    contextHandlerImpl.logTime("method-name", 1000);
    verify(contextHandlerImpl, atLeast(3)).logTime("method-name", 1000);
  }
}
