package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTemplateResponse;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.Response;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;


@Import(TemplateController.class)
@RunWith(SpringRunner.class)
public class TemplateControllerTest {

    @Autowired
    TemplateController templateController;
    @MockBean
    TemplateService templateService;

    private static final String INVOICE_APPROVAL_BPMN =
            "src/test/resources/bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_INVALID =
            "src/test/resources/bpmn/invoiceapproval.xml";
    private static final String CONTENT_TYPE = "application/octet-stream";

    @Test
    public void testSaveTemplate() throws Exception {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        String templateMetadata = "testmetadata";
        WorkflowGenericResponse expectedSaveRespnse = WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).build();
        Mockito.doReturn(expectedSaveRespnse).when(templateService).saveTemplate(Mockito.any(), Mockito.any());
        WorkflowGenericResponse saveResponse = templateController.saveTemplate(templates, templateMetadata);
        Assert.assertEquals(saveResponse, expectedSaveRespnse);
    }

    @Test
    public void testUpdateTemplate() throws Exception {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        String templateMetadata = "testmetadata";
        WorkflowGenericResponse expectedUpdateRespnse = WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).build();
        Mockito.doReturn(expectedUpdateRespnse).when(templateService).updateTemplate(Mockito.any(), Mockito.any());
        WorkflowGenericResponse updateResponse = templateController.updateTemplate(templates, templateMetadata);
        Assert.assertEquals(updateResponse, expectedUpdateRespnse);
    }

    @Test
    public void testUpdateTemplateStatus() {
        WorkflowGenericResponse expectedUpdateTemplateStatusRespnse = WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).build();
        Mockito.doReturn(expectedUpdateTemplateStatusRespnse).when(templateService).updateTemplateStatus(Mockito.anyString(), Mockito.anyString());
        WorkflowGenericResponse updateTemplateStatusResponse = templateController.updateTemplateStatus(INVOICE_APPROVAL_BPMN, "\\");
        Assert.assertEquals(updateTemplateStatusResponse, expectedUpdateTemplateStatusRespnse);
    }

    @Test
    public void testFetchTemplate() {
        File file = new File(Constants.TEMPLATE_ZIP_FILE_NAME);
        Response.ResponseBuilder expectedFetchTemplateRespnse = Response.ok((Object)file);
        expectedFetchTemplateRespnse.header("Content-Disposition", "attachment; filename="+ Constants.TEMPLATE_ZIP_FILE_NAME);
        Mockito.doNothing().when(templateService).fetchTemplates(Mockito.anyString(), Mockito.anyString());
        Response FetchTemplateResponse = templateController.fetchTemplate(INVOICE_APPROVAL_BPMN, "");
        Assert.assertEquals(FetchTemplateResponse.getHeaders(), expectedFetchTemplateRespnse.build().getHeaders());
    }

    @Test
    public void testFetchTemplateByVersion() {
        String testXml = "testXml";
        WorkflowTemplateResponse response = new WorkflowTemplateResponse(testXml);
        WorkflowGenericResponse expectedResponse = WorkflowGenericResponse.builder()
                .status(ResponseStatus.SUCCESS)
                .response(response)
                .build();
        Mockito.doReturn(expectedResponse).when(templateService).fetchTemplateByVersion(Mockito.any(), Mockito.anyInt());
        WorkflowGenericResponse fetchTemplateResponse = templateController.fetchTemplateByVersion(INVOICE_APPROVAL_BPMN, 1);
        Assert.assertEquals(expectedResponse, fetchTemplateResponse);
    }

    @Test
    public void testValidateTemplate() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        Mockito.doReturn(true).when(templateService).validateTemplate(Mockito.any(), Mockito.any());
        boolean saveResponse = templateController.validateTemplate(templates, "testMetaData");
        Assert.assertTrue(saveResponse);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testValidateTemplateFailInvalidFileFormat() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN_INVALID);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        Mockito.doThrow(WorkflowGeneralException.class).when(templateService).validateTemplate(Mockito.any(), Mockito.any());
        templateController.validateTemplate(templates, "testMetaData");
    }

    @Test
    public void testValidateTemplateFailIAsyncFalse() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        Mockito.doReturn(false).when(templateService).validateTemplate(Mockito.any(), Mockito.any());
        Assertions.assertDoesNotThrow(() -> templateController.validateTemplate(templates, "testMetaData"));
        Assert.assertFalse(templateController.validateTemplate(templates, "testMetaData"));
    }

}
