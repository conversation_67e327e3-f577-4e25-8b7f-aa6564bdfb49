package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.intuit.appintgwkflw.wkflautomate.was.core.history.HistoryService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Import(HistoryController.class)
public class HistoryControllerTest {

  @Autowired
  private HistoryController historyController;

  @MockBean
  private HistoryService historyService;

  @Test
  public void whenGetProcessDetails_thenSuccess() {

    final WorkflowGenericResponse expectedResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedResponse).when(historyService).getProcessDetails(any());

    final WorkflowGenericResponse getProcessDetailsResponse = historyController
        .getProcessDetails(UUID.randomUUID().toString());
    Assert.assertEquals(expectedResponse, getProcessDetailsResponse);
  }
  
	@Test
	public void testGetProcessVariableDetails() {
		final WorkflowGenericResponse expectedResponse = WorkflowGenericResponse.builder()
				.status(ResponseStatus.SUCCESS).build();
		doReturn(expectedResponse).when(historyService).getProcessVariableDetails(any());

		final WorkflowGenericResponse getProcessDetailsResponse = historyController
				.getProcessVariableDetails(new ProcessVariableDetailsRequest(), 0, 50, false);
		Assert.assertEquals(expectedResponse, getProcessDetailsResponse);
	}
}