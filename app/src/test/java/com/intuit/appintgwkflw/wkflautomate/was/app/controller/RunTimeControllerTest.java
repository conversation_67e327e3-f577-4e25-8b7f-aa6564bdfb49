package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import java.util.HashMap;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Import(RunTimeController.class)
public class RunTimeControllerTest {

  @Autowired
  private RunTimeController runTimeController;

  @MockBean
  private RunTimeService runTimeService;

  @Test
  public void whenTrigger_thenSuccess() throws Exception {

    WorkflowGenericResponse expectedResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedResponse).when(runTimeService).processTriggerMessage(any());

    WorkflowGenericResponse triggerResponse = runTimeController
        .trigger(new HashMap<>());
    Assert.assertEquals(expectedResponse, triggerResponse);
  }

  @Test
  public void whenEvaluateRules_thenSuccess() throws Exception {

    WorkflowGenericResponse expectedResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedResponse).when(runTimeService).processEvaluateRulesMessage(any());

    WorkflowGenericResponse evaluateResponse = runTimeController
        .evaluateRules(new HashMap<>());
    Assert.assertEquals(expectedResponse, evaluateResponse);
  }
}