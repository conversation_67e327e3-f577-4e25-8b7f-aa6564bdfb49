package com.intuit.appintgwkflw.wkflautomate.was.observability;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MetricsConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.config.MeterFilter;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for metrics.
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class MetricConfiguration {

  private static final String METRIC_PREFIX = "wkflwatmnsvc.micrometer";

  private static final Tags INTUIT_TAG = Tags.of("intuit_alert", "true");

  private static final String METRIC_HIKARI_ACTIVE = "hikaricp.connections.active";
  private static final String METRIC_HIKARI_IDLE = "hikaricp.connections.idle";
  private static final String METRIC_HIKARI_PENDING = "hikaricp.connections.pending";
  private static final String METRIC_HIKARI_MAX = "hikaricp.connections.max";

  private final MetricsConfig metricsConfig;

  /**
   * Filter certain metrics and add required custom naming and tags.
   *
   * @return the meter filter
   */
  @Bean
  MeterFilter meterFilter() {

    return new MeterFilter() {

      @Override
      public Meter.Id map(final Meter.Id id) {

        return nameMatches(id) ? transform(id) : id;
      }

      private boolean nameMatches(final Meter.Id id) {

        return emitAppMetrics(id.getName())
            || emitServiceMetrics(id.getName())
            || emitHikariMetrics(id.getName());
      }

      private boolean emitAppMetrics(String name) {

        return StringUtils.startsWithAny(
            name,
            Type.API_METRIC.getLowerCaseValue(),
            Type.APPLICATION_METRIC.getLowerCaseValue(),
            Type.APP_CONNECT_METRIC.getLowerCaseValue(),
            Type.EVENT_METRIC.getLowerCaseValue(),
            ResiliencyConstants.CIRCUIT_BREAKER_STATE_METRIC,
            Type.EXTERNAL_TASK_METRIC.getLowerCaseValue(),
            Type.CAMUNDA_METRIC.getLowerCaseValue(),
            Type.WAS_METRIC.getLowerCaseValue());

      }

      private boolean emitServiceMetrics(String name) {

        return metricsConfig.isEmitServiceMetrics()
            && StringUtils.startsWithAny(
            name,
            Type.SERVICE_METRIC.getLowerCaseValue());
      }

      private boolean emitHikariMetrics(String name) {

        return metricsConfig.isEmitHikariMetrics()
            && StringUtils.startsWithAny(
            name,
            METRIC_HIKARI_ACTIVE,
            METRIC_HIKARI_IDLE,
            METRIC_HIKARI_PENDING,
            METRIC_HIKARI_MAX);
      }

      private Meter.Id transform(final Meter.Id id) {

        return id.withName(String.format("%s.%s", METRIC_PREFIX, id.getName()))
            .withTags(Tags.concat(INTUIT_TAG, id.getTagsAsIterable()));
      }
    };
  }
}
