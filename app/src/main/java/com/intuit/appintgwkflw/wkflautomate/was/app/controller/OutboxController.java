package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.service.DomainEventTestService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 *     <p>Controller Method to Query Outbox table for entityId for various IEDM entities like
 *     Process, Activity etc
 */
@RestController
@RequestMapping("/v1/outbox")
@AllArgsConstructor
public class OutboxController {

  private DomainEventTestService domainEventTestService;

  /**
   * Get Event by event id.
   *
   * @param eventId
   * @return
   */
  @Metric(name = MetricName.GET_DOMAIN_EVENT_LIST, type = Type.API_METRIC)
  @GetMapping("/getEventById")
  public DomainEvent getEventsByEntityId(@RequestParam("eventId") final String eventId) {
    return domainEventTestService.getEventById(eventId);
  }

  /**
   * Get header metadata details by partitionKey.
   *
   * @param partitionKey
   * @return
   */
  @Metric(name = MetricName.GET_DOMAIN_EVENT, type = Type.API_METRIC)
  @GetMapping("/getEventListByPartitionKey")
  public List<DomainEvent> getEventsByPartitionKey(
      @RequestParam("partitionKey") final String partitionKey) {
    return domainEventTestService.getEventsByPartitionKey(partitionKey);
  }

  /**
   * Aggregate function to return the count of events for a partitionKey
   *
   * @param partitionKey
   * @return
   */
  @Metric(name = MetricName.GET_DOMAIN_EVENT_LIST_COUNT, type = Type.API_METRIC)
  @GetMapping("/getTotalEventsPublished")
  public long getCountByEventsPublished(@RequestParam("partitionKey") final String partitionKey) {
    return domainEventTestService.getCountByEventsPublishedByPartitionKey(partitionKey);
  }
}
