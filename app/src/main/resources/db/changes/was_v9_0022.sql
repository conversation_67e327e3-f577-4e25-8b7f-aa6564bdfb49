-- For reference, Here's the jira: https://jira.intuit.com/browse/QBOES-20770
create table de_definition_activity_details
(
    id varchar(255) not null
        constraint de_definition_activity_details_pkey
            primary key,
    activity_id varchar(255),
    parent_id varchar(255)
        constraint de_definition_activity_details_parent_id
            references de_definition_activity_details,
    user_attributes jsonb,
    definition_details_definition_id varchar(255)
        constraint de_definition_activity_details_definition_id
            references de_definition_details
);

create index if not exists definition_details_idx
    on de_definition_activity_details (definition_details_definition_id);