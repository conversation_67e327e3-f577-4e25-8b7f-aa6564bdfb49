CREATE TABLE IF NOT EXISTS was.de_scheduler_details
(
    scheduler_id                     varchar(255) not null
        primary key,
    definition_details_definition_id varchar(255)
        constraint definition_id
            references de_definition_details,
    scheduler_action          varchar(255) not null,
    owner_id                         bigint       not null
);

create index if not exists scheduler_details_definition_details_definition_id_idx
    on was.de_scheduler_details (definition_details_definition_id);

