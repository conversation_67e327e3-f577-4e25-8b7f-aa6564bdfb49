CREATE TABLE IF NOT EXISTS was.outbox
(
    event_id        uuid                        NOT NULL PRIMARY KEY,
    topic           VARCHAR(255)                NOT NULL,
    partition_key   VARCHAR(255)                NOT NULL,
    utc_time        TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    region          VARCHAR(255)                NOT NULL,
    headers         JSONB,
    payload         JSONB
);

CREATE PUBLICATION data_capture_outbox_publication
    FOR TABLE was.outbox
    WITH (publish = 'insert');

