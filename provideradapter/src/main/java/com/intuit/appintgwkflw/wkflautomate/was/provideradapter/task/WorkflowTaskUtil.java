package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.Expression;
import com.intuit.v4.query.extensions.PreparedQuery;
import com.intuit.v4.workflows.tasks.Task;
import com.intuit.v4.workflows.tasks.TaskTypeEnum;
import lombok.experimental.UtilityClass;

/**
 * 
 * <AUTHOR>
 *
 */
@UtilityClass
public class WorkflowTaskUtil {

  private static final String RECORD_ID = "recordId";
  private static final String TYPE = "type";
  private static final String ARGS = "args";
  private static final String PROPERTY = "property";
  private static final String WORKFLOW_NAME = "workflowName";
  private static final int MAX_LIMIT = 25;
  private static final int MIN_LIMIT = 5;
  private static final int DEFAULT_OFFSET = 0;
  private static final String TASK_TYPE = "type";

  /**
   * Prepares RecordTaskRequestDetails obj for processing and resulting record based tasks.
   * 
   * @param query
   * @return RecordTaskRequestDetails
   */
  @SuppressWarnings("deprecation")
  public static WorkflowTaskRequestDetails getTaskRequest(QueryHelper query) {

    final PreparedQuery preparedQuery = query.getPreparedQuery();
    WorkflowVerfiy.verifyNull(preparedQuery, WorkflowError.INPUT_INVALID, RECORD_ID);
    
    WorkflowVerfiy.verify(preparedQuery.getLimit() != null && 
        (preparedQuery.getLimit() > MAX_LIMIT || preparedQuery.getLimit() < MIN_LIMIT),
        WorkflowError.INVALID_LIMIT, MIN_LIMIT, MAX_LIMIT);

    WorkflowTaskRequestDetails taskRequest =
        WorkflowTaskRequestDetails.builder().recordId(getRecordId(query))
            .taskTypes(new ArrayList<>())
            .workflowNames(new ArrayList<>())
            .limit(preparedQuery.getLimit() != null
                ? preparedQuery.getLimit() : MAX_LIMIT)
            .offset(StringUtils.isNotBlank(preparedQuery.getOffset())
                ? Integer.parseInt(preparedQuery.getOffset())
                : DEFAULT_OFFSET)
            .build();

    WorkflowLogger.logInfo("Get Task Request received for limit: %s, offset: %s, query: %s",
        preparedQuery.getLimit(), preparedQuery.getOffset(), query);

    processQuery(query, taskRequest);
    
    /**
     * Checks if tasks filter is empty adds default filter of HUMAN_TASK. For any other task type
     * filter, responds with error.
     */
    if (CollectionUtils.isEmpty(taskRequest.getTaskTypes())) {
      taskRequest.getTaskTypes().add(TaskType.HUMAN_TASK);
    }

    WorkflowVerfiy.verify(
        taskRequest.getTaskTypes().stream()
          .anyMatch(taskType -> !taskType.equals(TaskType.HUMAN_TASK)),
            WorkflowError.INPUT_INVALID, TASK_TYPE);
    return taskRequest;
  }

  /**
   * fetches recordId for which the task details need to be fetched.
   * 
   * @param query
   * @return recordId
   */
  private static String getRecordId(QueryHelper query) {
    final Optional<Map<?, ?>> recordTaskDetailsInputOptional = query.getArg(WorkflowConstants.BY);
    final Map<?, ?> recordTaskDetailsMap = recordTaskDetailsInputOptional.orElseThrow(
        () -> new WorkflowGeneralException(WorkflowError.INPUT_INVALID, WorkflowConstants.BY));

    final Task.TaskDetailsInput recordTaskDetailsInput =
        ObjectConverter.convertObject(recordTaskDetailsMap, Task.TaskDetailsInput.class);

    WorkflowVerfiy.verify(StringUtils.isBlank(recordTaskDetailsInput.getRecordId()),
        WorkflowError.INPUT_INVALID, RECORD_ID);
    return recordTaskDetailsInput.getRecordId();
  }

  /**
   * Parses and fetches query filters and set them in taskRequest object.
   * 
   * @param query
   * @param taskRequest
   */
  @SuppressWarnings({"unchecked", "rawtypes"})
  private static void processQuery(QueryHelper query, WorkflowTaskRequestDetails taskRequest) {
    Expression whereExpression = query.getPreparedQuery().getWhere();
    if (null == whereExpression) {
      return;
    }
    LOGICAL_OPERATORS whereLogicalOperator =
        LOGICAL_OPERATORS.fromDenotions(whereExpression.getOp());
    if (null != whereLogicalOperator) {
      WorkflowVerfiy.verify(whereLogicalOperator != LOGICAL_OPERATORS.AND,
          WorkflowError.INPUT_INVALID, "LogicalOperator");
      List<com.intuit.v4.query.Expression> filterExpressions =
          ((ArrayList) whereExpression.get(ARGS));
      Optional.ofNullable(filterExpressions)
          .ifPresent(expressions -> expressions.stream()
              .forEach(filterExpression -> fetchWorkflowNamesAndTaskTypes(taskRequest,
                  filterExpression)));
    } else {
      fetchWorkflowNamesAndTaskTypes(taskRequest, whereExpression);
    }
  }

  /**
   * Parses and fetches query filters and set them in workflowIds or taskType list.
   * 
   * @param taskTypes
   * @param workflowIds
   * @param expression
   */
  @SuppressWarnings("unchecked")
  private static void fetchWorkflowNamesAndTaskTypes(WorkflowTaskRequestDetails taskRequest,
      Expression expression) {
    if (TYPE.equals(expression.get(PROPERTY))) {
      if (expression.get(ARGS) instanceof List) {
        taskRequest.getTaskTypes().addAll(
          ((List<String>)expression.get(ARGS)).stream()
            .map(TaskType::valueOf).collect(Collectors.toList()));
      } else {
        taskRequest.getTaskTypes().add((TaskType) expression.get(ARGS));
      }
    } else if (WORKFLOW_NAME.equals(expression.get(PROPERTY))) {
      if (expression.get(ARGS) instanceof List) {
        taskRequest.getWorkflowNames().addAll((List<String>) expression.get(ARGS));
      } else {
        taskRequest.getWorkflowNames().add((String) expression.get(ARGS));
      }
    } else {
      throw new WorkflowGeneralException(WorkflowError.INPUT_INVALID, "Sub-Filters");
    }
  }

}