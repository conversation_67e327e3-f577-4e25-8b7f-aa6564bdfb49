## Before PR

1. Create a fork of the codebase and checkout the code.
1. Create a feature branch that represents your changes, for e.g. 'perf-changes'.
1. Follow the developer guide for setting up the workspace locally.
1. Follow the best practices mentioned here while making the changes.
1. Write proper unit tests for the change.
1. Do a basic sanity test for your change by verifying the expected results on Postman.
1. If your change affects more than one component in the platform, run the Overwatch tests to verify there is no regression.
1. Make your code changes and push to your branch.

**Note:** If you are doing a POC, please take a look at one of the examples to understand how to get started.

## During PR

1. Limit the maximum number of files in each PR to 10. Break your PR into multiple logical parts if the change is bigger.
2. Create a `Draft PR` and get it reviewed internally within your team. We require at-least one internal review before looking at your PR.
3. Give the Jira Id in the PR title.
4. In the PR description, provide the following details:
   1. Details of the change
   2. Details of testing done
   3. Screenshots of Postman or Overwatch tests
5. Select the appropriate priority for your PR.
6. Link your PR to the original issue.
7. Ensure that `codecov` results are above 90% coverage (with 100% diff coverage).
8. Publish the PR once it's ready for review.


## After PR
1. Drop a message in Slack:`##workflow-capability-support` and tag `@workflow-oncall`.
1. We will take a look at the PR according to the given SLA.

**Note:** Turnaround time for making changes related to review comments will not be considered as part of SLA.

## Best Practices

### Coding
1. Use `GoogleJavaStyle` to format your code. Use `Save Actions` plugin to automate the formatting.
1. Use `SonarLint` plugin to ensure there are no code smells and bugs in the code.
1. Use `@AllArgsConstructor` to automatically perform a constructor based injection. Do not use `@Autowired` on fields.
1. Use streams in favour of traditional loops wherever possible.
1. Use `StringUtils.isBlank` for null and empty checks.
1. Use `private final` class level variables. Ideally they should be immutable and part of constructor.
1. Do not have any static methods in your class unless it's a pure utility class. Use the `@UtilityClass` annotation for such classes.
1. Every code change should be accompanied by a unit test.
1. When returning lists with no values, prefer to return empty lists instead of null.
