<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/DMN/20151101/dmn.xsd" id="Definitions_06f1q8k" name="DRD" namespace="http://camunda.org/schema/1.0/dmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <decision id="tweetApproval" name="Tweet Approval">
    <decisionTable id="decisionTable_1">
      <input id="input_1" label="Email">
        <inputExpression id="inputExpression_1" typeRef="string">
          <text>email</text>
        </inputExpression>
      </input>
      <input id="InputClause_04c24t2" label="content">
        <inputExpression id="LiteralExpression_1ne40a2" typeRef="string">
          <text>content</text>
        </inputExpression>
      </input>
      <output id="output_1" label="approved" name="approved" typeRef="boolean" />
      <rule id="DecisionRule_1pqppxy">
        <inputEntry id="UnaryTests_03rsgk5">
          <text>"<EMAIL>"</text>
        </inputEntry>
        <inputEntry id="UnaryTests_1lygdh4">
          <text>-</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1ys3zbr">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1k6jfv2">
        <inputEntry id="UnaryTests_10ddo9b">
          <text>"<EMAIL>"</text>
        </inputEntry>
        <inputEntry id="UnaryTests_0593e8r">
          <text>-</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_04n1cvy">
          <text>false</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_1yp6ai8">
        <inputEntry id="UnaryTests_02niyz2">
          <text>-</text>
        </inputEntry>
        <inputEntry id="UnaryTests_03syxjr" expressionLanguage="juel">
          <text>content.contains("camunda rocks")</text>
        </inputEntry>
        <outputEntry id="LiteralExpression_0eo0nty">
          <text>true</text>
        </outputEntry>
      </rule>
      <rule id="DecisionRule_0t0cu6d">
        <inputEntry id="UnaryTests_0cts8hn">
          <text></text>
        </inputEntry>
        <inputEntry id="UnaryTests_17jqiq0">
          <text></text>
        </inputEntry>
        <outputEntry id="LiteralExpression_1ymn14o">
          <text>false</text>
        </outputEntry>
      </rule>
    </decisionTable>
  </decision>
</definitions>
