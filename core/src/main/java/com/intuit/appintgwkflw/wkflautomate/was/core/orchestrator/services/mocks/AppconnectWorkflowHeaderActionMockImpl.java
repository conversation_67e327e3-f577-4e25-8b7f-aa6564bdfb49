package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import static java.lang.Boolean.TRUE;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.RestAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import org.springframework.http.HttpStatus;

/**
 * This implementation is only used in the prf environment. The intention to use this is to mock the
 * app connect network calls in the prf environment. 
 *
 * <AUTHOR>
 */
public class AppconnectWorkflowHeaderActionMockImpl implements RestAction {

  @SuppressWarnings("unchecked")
  @Override
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> execute(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {

    // mocking time taken by app connect
    try {
      Thread.sleep(ThreadLocalRandom.current().nextInt(100, 6000));
    } catch (InterruptedException e) {
    }
    WorkflowTaskHandlerResponse re = new WorkflowTaskHandlerResponse();
    re.setSuccess(TRUE.toString());
   Map<String, Object> data = new HashMap<>();
   data.put("projectId", "1234");
   re.setData(data);
    WASHttpResponse<RESPONSE> response =
        (WASHttpResponse<RESPONSE>)
            WASHttpResponse.<WorkflowTaskHandlerResponse>builder()
                .isSuccess2xx(true)
                .response(re)
                .status(HttpStatus.OK)
                .build();

    return response;
  }

}
