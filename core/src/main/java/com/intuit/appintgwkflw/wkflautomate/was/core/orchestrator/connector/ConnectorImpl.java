package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.connector;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CONNECTOR_TID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CONTENT_TYPE_JSON;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;


import com.cronutils.utils.StringUtils;
import com.cronutils.utils.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.connector.GenericConnector;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;

import java.util.*;

import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ApprovalErrorResponseObject;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

/**
 * Implementation of the generic connector to implement header and api client.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ConnectorImpl extends GenericConnector {
  private WASHttpClient client;
  private OfflineTicketClient offlineTicketClient;
  private WASContextHandler wasContextHandler;
  /**
   * Use System offline ticket
   *
   * @return
   */
  @Override
  protected HttpHeaders populateHeader() {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(HttpHeaders.CONTENT_TYPE, CONTENT_TYPE_JSON);
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.
                    getSystemOfflineHeaderWithContextRealmForOfflineJob(wasContextHandler.get(WASContextEnums.OWNER_ID)));
    httpHeaders.set(INTUIT_TID,
        Optional.ofNullable(wasContextHandler.get(WASContextEnums.INTUIT_TID))
            .orElse(CONNECTOR_TID.concat(UUID.randomUUID().toString())));
    return httpHeaders;
  }

  /**
   * Make a API request with the body generated to the requestUrl and return response
   *
   * @param method
   * @param bodyParameters
   * @param queryVariables
   * @param parameters
   * @param requestUrl
   * @return
   */
  @Override
  protected WASHttpResponse<Object> executeRequest(String method, List<Map<String, String>> bodyParameters, List<Map<String, String>> queryVariables, Map<String, Map<String, Object>> parameters, String requestUrl){
    WASHttpRequest<Object, Object> wasHttpRequest =
        WASHttpRequest.<Object, Object>builder()
            .httpMethod(HttpMethod.valueOf(method))
            .request(generateRequestBody(bodyParameters, queryVariables, parameters))
            .requestHeaders(populateHeader())
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(requestUrl)
            .build();

    WASHttpResponse<Object> response = client.httpResponse(wasHttpRequest);
    if(!response.isSuccess2xx()){
      return handleFailure(response);
    }
    return response;
  }

  @VisibleForTesting
  WASHttpResponse<Object> handleFailure(WASHttpResponse<Object> response)  {
    // Handle 4xx error
    //
    if(null != response.getStatus() && response.getStatus().is4xxClientError()) {
      //Sample error str:
      // {"status":"BAD_REQUEST",
      // "data":null,
      // "errorDetails":
      // {"errorCode":"A30","errorMessage":"No approval lifecycle found for this entityId=1001, entityType=invoice","errorDescription":"No approval lifecycle found for this entity"}}
      WorkflowLogger.logError("Error from Approval Service error=%s, downstreamError=%s",
          response.getError(), response.getDownstreamError());
      String errorStr = response.getDownstreamError();
      if (!StringUtils.isEmpty(errorStr)) {
        ApprovalErrorResponseObject approvalResponseObject = ObjectConverter.fromJson(errorStr, ApprovalErrorResponseObject.class);
        if (ObjectUtils.isNotEmpty(approvalResponseObject)
                && (approvalResponseObject.getErrorDetails().getErrorCode().equals(WorkflowConstants.APPROVAL_GROUP_BAD_REQUEST_ERROR_CODE))) {
          return response;
        }
      }
    }
    throw new WorkflowGeneralException(WorkflowError.APPROVAL_SERVICE_CALL_FAILED);
  }

}
