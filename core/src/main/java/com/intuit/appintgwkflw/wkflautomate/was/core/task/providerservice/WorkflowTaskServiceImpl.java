package com.intuit.appintgwkflw.wkflautomate.was.core.task.providerservice;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.VALUE_IDENTIFIER;
import com.fasterxml.jackson.databind.JsonNode;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.ExternalTaskRestHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Permission;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.TaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.WasAuthorizeRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.TaskValueDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task.WorkflowTaskRequestDetails;
import com.intuit.v4.workflows.tasks.Task;
import com.intuit.v4.workflows.tasks.TaskAttribute;
import com.intuit.v4.workflows.tasks.TaskTypeEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * Performs different Query and mutation operations
 */
@Component
@AllArgsConstructor
public class WorkflowTaskServiceImpl implements WorkflowTaskService {

  private static final String START_TIME = "startTime";

  private ActivityDetailsRepository activityDetailsRepository;

  private ActivityProgressDetailsRepository activityProgressDetailsRepository;

  private ProcessDetailsRepository processDetailsRepository;
  
  private WASContextHandler contextHandler;

  private CamundaServiceManager camundaServiceManager;

  private ExternalTaskRestHandler externalTaskRestHandler;

  private AccessVerifier accessVerifier;

  /**
   * Fetches Tasks based on custom operation in {@link Task.WorkflowTaskDetailsInput}
   *
   * @param taskRequest@return details of Tasks
   * @throws WorkflowGeneralException if invalid input is provided
   */
  @Override
  @Metric(name = MetricName.GET_WORKFLOW_TASKS, type = Type.API_METRIC)
  public List<Task> getWorkflowTasks(TaskRequest taskRequest) {

    final Map<?, ?> workflowTaskDetailsMap = taskRequest.getQuery();
    final Map<String, String> headers = taskRequest.getHeaders();

    final Task.WorkflowTaskDetailsInput workflowTaskDetailsInput =
        ObjectConverter.convertObject(workflowTaskDetailsMap, Task.WorkflowTaskDetailsInput.class);
    
    WorkflowVerfiy.verify(workflowTaskDetailsInput.getWorkflowId(),
            WorkflowError.INPUT_INVALID, "workflowId");
    
    final Long workflowOwnerId =
        this.processDetailsRepository
            .findOwnerId(workflowTaskDetailsInput.getWorkflowId())
            .orElseThrow(
                this.invalidInputError(WorkflowError.WORKFLOW_NOT_FOUND, "Incorrect workflowId"));
    final Long requestOwnerId = Long.valueOf(this.contextHandler.get(WASContextEnums.OWNER_ID));

    final ActivityProgressDetails activityProgressDtls =
        getActivityProgressDetails(workflowTaskDetailsInput.getActivityInstanceId(), false);
    final String taskOwnerId = getTaskOwner(activityProgressDtls);
    final String domain = getDomain(activityProgressDtls);

    this.authoriseAccess(
        WasAuthorizeRequest.builder()
            .permission(Permission.TASK_READ)
            .RequestOwnerId(requestOwnerId)
            .workflowOwnerId(workflowOwnerId)
            .onBehalfOf(headers.getOrDefault(WorkflowConstants.ON_BEHALF_OF, null))
            .assignee(headers.getOrDefault(WorkflowConstants.ASSIGNEE, null))
            .taskOwnerId(StringUtils.isNotEmpty(taskOwnerId) ? Long.parseLong(taskOwnerId) : null)
            .domain(domain)
            .taskType(null != activityProgressDtls ?
                activityProgressDtls.getActivityDefinitionDetail().getType().name() : null)
            .build());

    return Optional.ofNullable(workflowTaskDetailsInput)
        .map(t -> this.fetchResults(workflowTaskDetailsInput, workflowOwnerId))
        .orElseThrow(this.invalidInputError(WorkflowError.INPUT_INVALID, WorkflowConstants.BY));
  }

  @Override
  @Metric(name = MetricName.UPDATE_WORKFLOW_TASK, type = Type.API_METRIC)
  public Task updateWorkflowTasks(TaskRequest taskRequest) {
    final Task task = taskRequest.getTask();
    // headers
    final Map<String, String> requestHeaders = taskRequest.getHeaders();
    // Request Validation
    this.validateTaskRequest(task);
    final Long requestOwnerId = Long.valueOf(this.contextHandler.get(WASContextEnums.OWNER_ID));

    final ProcessDetails processDetails =
        ObjectUtils.isNotEmpty(task.getWorkflowId())
            ? processDetailsRepository
                .getProcessDetailsWithoutDefinitionData(task.getWorkflowId())
                .orElseThrow(
                    () ->
                        new WorkflowGeneralException(
                            WorkflowError.WORKFLOW_NOT_FOUND, "Incorrect workflowId"))
            : processDetailsRepository
                .findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(
                    task.getRecordId(), task.getWorkflowName())
                .orElseThrow(
                    () ->
                        new WorkflowGeneralException(
                            WorkflowError.WORKFLOW_NOT_FOUND,
                            "Incorrect combination of recordId and workflowName"));

    final Long workflowOwnerId = processDetails.getOwnerId();

    final ActivityProgressDetails activityProgressDtls =
        getActivityProgressDetails(task.getActivityInstanceId(), true);
    // check if taskType=Milestone and throw validation Error
    WorkflowVerfiy.verify(
        TaskType.MILESTONE.equals(activityProgressDtls.getActivityDefinitionDetail().getType()),
        WorkflowError.MUTATION_NOT_SUPPORTED,
        TaskType.MILESTONE);
    final String taskOwnerId = getTaskOwner(activityProgressDtls);
    final String domain = getDomain(activityProgressDtls);

    // Authorize cross realm access
    this.authoriseAccess(
        WasAuthorizeRequest.builder()
            .permission(Permission.TASK_UPDATE)
            .RequestOwnerId(requestOwnerId)
            .workflowOwnerId(workflowOwnerId)
            .onBehalfOf(requestHeaders.getOrDefault(WorkflowConstants.ON_BEHALF_OF, null))
            .assignee(requestHeaders.getOrDefault(WorkflowConstants.ASSIGNEE, null))
            .taskOwnerId(StringUtils.isNotEmpty(taskOwnerId) ? Long.parseLong(taskOwnerId) : null)
            .taskType(activityProgressDtls.getActivityDefinitionDetail().getType().name())
            .domain(domain)
            .build());

    // Prepare localVariable Map
    Map<String, Object> localVariables = prepareAttributeMap(task.getAttributes());

    this.validateTaskAttributes(localVariables.keySet(), activityProgressDtls);

    ExternalTaskCompleted event =
        ExternalTaskCompleted.builder()
            .localVariables(localVariables)
            .status(task.getStatus())
            .errorMessage(
                ObjectUtils.isNotEmpty(task.getError()) ? task.getError().getMessage() : null)
            .errorDetails(
                ObjectUtils.isNotEmpty(task.getError()) ? task.getError().getDetail() : null)
            .build();

    Map<String, String> headers = prepareHeader(task);
    externalTaskRestHandler.executeUpdate(task, event, headers);
    return task;
  }

  /**
   * Get ActivityProgressDetail using activityInstanceId.
   *
   * @param activityInstanceId - Activity Instance Id
   * @param isMutateReq        - boolean flag to check if ActivityInstanceId is not present and it
   *                           is a mutateReq.
   * @return
   */
  private ActivityProgressDetails getActivityProgressDetails(final String activityInstanceId,
      boolean isMutateReq) {
    if (StringUtils.isEmpty(activityInstanceId)) {
      if (isMutateReq) {
        throw invalidInputError(WorkflowError.INPUT_INVALID, "activityInstanceId").get();
      }
      return null;
    }
    final Optional<ActivityProgressDetails> activityProgressDtlOptn =
        activityProgressDetailsRepository.findById(activityInstanceId);

    return activityProgressDtlOptn.orElseThrow(() ->
        invalidInputError(WorkflowError.INPUT_INVALID, "activityInstanceId").get());
  }

  /**
   * Fetch ownerId from activity progress Details.
   *
   * @param activityProgressDtl
   * @return
   */
  private String getTaskOwner(final ActivityProgressDetails activityProgressDtl) {
    if (null == activityProgressDtl) {
      return null;
    }
    TaskAttributes taskAttributes = ObjectConverter.fromJson(activityProgressDtl.getAttributes(),
        TaskAttributes.class);
    return (String) Optional.ofNullable(taskAttributes)
    		.map(TaskAttributes::getRuntimeAttributes)
    		.map(taskAttr -> taskAttr.get(WorkflowConstants.OWNER_ID))
    		.orElse(null);
  }

  /**
   * Fetch domain from activity Details.
   *
   * @param activityProgressDtl
   * @return
   */
  private String getDomain(final ActivityProgressDetails activityProgressDtl) {
    if (null == activityProgressDtl) {
      return null;
    }
    WorkflowActivityAttributes activityAttributes = ObjectConverter
        .fromJson(activityProgressDtl.getActivityDefinitionDetail().getAttributes(),
            WorkflowActivityAttributes.class);
	return Optional.ofNullable(activityAttributes)
			.map(WorkflowActivityAttributes::getModelAttributes)
			.map(modelAttr -> modelAttr.get(ActivityConstants.ACTIVITY_DOMAIN))
			.orElse(null);
  }

  /**
   * Task Validation is performed here
   *
   * @param task
   */
  private void validateTaskRequest(Task task) {
    // Throw exception when both externalRefId and activityInstanceId are null or both have
    // different values
    boolean constraintPredicate =
        ObjectUtils.isNotEmpty(task.getExternalRefId())
                && ObjectUtils.isNotEmpty(task.getActivityInstanceId())
            || ObjectUtils.isEmpty(task.getExternalRefId())
                && ObjectUtils.isEmpty(task.getActivityInstanceId());
    WorkflowVerfiy.verify(
        constraintPredicate,
        WorkflowError.INVALID_TASK_INPUT,
        "Both externalTaskRefId and activityInstanceId cannot be present or null");

    boolean constraintPredicate2 =
        ObjectUtils.isNotEmpty(task.getWorkflowId())
            || (ObjectUtils.isNotEmpty(task.getRecordId())
                && ObjectUtils.isNotEmpty(task.getWorkflowName()));

    WorkflowVerfiy.verify(
        !constraintPredicate2,
        WorkflowError.INVALID_TASK_INPUT,
        "Missing workflowId or recordId and workflowName is missing");

    // Error Validation. In case of Failed status, custom error message and/or error details should
    // be passed for creating an incident.
    boolean constraintPredicate3 =
        ExternalTaskStatus.FAILED.getStatus().equalsIgnoreCase(task.getStatus())
            && ObjectUtils.isEmpty(task.getError());

    WorkflowVerfiy.verify(
        constraintPredicate3,
        WorkflowError.INVALID_TASK_INPUT,
        "Missing error details for creating incident");

    // Throw Validation error if there is no status field and attributes to mutate
    boolean constraintPredicate4 =
        ObjectUtils.isEmpty(task.getStatus()) && ObjectUtils.isEmpty(task.getAttributes());
    WorkflowVerfiy.verify(
        constraintPredicate4,
        WorkflowError.INVALID_TASK_INPUT,
        "Missing status and Task Attribute Details");

    // Check for null keys
    List<TaskAttribute> invalidAttributes =
        task.getAttributes().stream()
            .filter(attribute -> StringUtils.isBlank(attribute.getName()))
            .collect(Collectors.toList());

    WorkflowVerfiy.verify(
        CollectionUtils.isNotEmpty(invalidAttributes),
        WorkflowError.INVALID_TASK_INPUT,
        "Invalid/Null Keys");
  }

  /**
   * NOTE: Null Keys will be ignored <br>
   * </>Utility method to return map in below format.
   *
   * <p>"variables":{ "local":{ "value":"test" }}
   *
   * @param attributes : Map<String, String></>
   * @return
   */
  private Map<String, Object> prepareAttributeMap(List<TaskAttribute> attributes) {
    Map<String, Object> variables = new HashMap<>();
    attributes.stream()
        .filter(attribute -> StringUtils.isNotBlank(attribute.getName()))
        .forEach(
            attribute ->
                variables.put(
                    attribute.getName(),
                    Collections.singletonMap(VALUE_IDENTIFIER, attribute.getValue())));

    return variables;
  }

  private Map<String, String> prepareHeader(Task task) {
    Map<String, String> headers = new HashMap<>();
    // TODO: What needs to be set here?
    headers.put(
        EventHeaderConstants.IDEMPOTENCY_KEY, contextHandler.get(WASContextEnums.INTUIT_TID));
    // setting TID
    headers.put(EventHeaderConstants.INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID));
    headers.put(EventHeaderConstants.OFFERING_ID, WASContext.getOfferingId().get());
    headers.put(EventHeaderConstants.OWNER_ID, contextHandler.get(WASContextEnums.OWNER_ID));
    return headers;
  }

  /**
   * @param wasAuthorizeRequest
   * @throws WorkflowGeneralException : In case Requesting Realm has no authorization over resource
   */
  private void authoriseAccess(WasAuthorizeRequest wasAuthorizeRequest) {
    if (isAuthzCheckRequired(wasAuthorizeRequest)) {
      // calling AuthZ API
      boolean hasAccess = true;
      if (TaskType.HUMAN_TASK.name().equals(wasAuthorizeRequest.getTaskType())) {
    	hasAccess = accessVerifier.verifyHumanTaskAccess(wasAuthorizeRequest);
      } else {
    	hasAccess = accessVerifier.verifyAccess(wasAuthorizeRequest);
      }
      WorkflowVerfiy.verify(!hasAccess, WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS);
    }
  }

  /**
   * @param wasAuthorizeRequest :: Authorization Request.
   * @return boolean :: flag for Authz Call.
   */
  private boolean isAuthzCheckRequired(WasAuthorizeRequest wasAuthorizeRequest) {
    return wasAuthorizeRequest.getWorkflowOwnerId().longValue() != wasAuthorizeRequest
        .getRequestOwnerId().longValue();
  }

  /**
   * computes List of workflow Tasks based on input
   * @param workflowTaskDetailsInput query info
   * @return tasks details list
   * @throws WorkflowGeneralException if invalid input is provided
   */
  private List<Task> fetchResults(Task.WorkflowTaskDetailsInput workflowTaskDetailsInput , Long workflowOwnerId) {
    
    int val = calculateBitmask(workflowTaskDetailsInput);
    switch (val) {
      case 0xC:
        return fetchTasksByWorkflowId(workflowTaskDetailsInput.getWorkflowId(), workflowOwnerId);
      case 0xE:
        return fetchTasksByWorkflowAndActivityId(workflowTaskDetailsInput.getWorkflowId(),
            workflowTaskDetailsInput.getActivityId(),workflowOwnerId);
      case 0xF:
        return Optional.ofNullable(fetchTasksByTaskId(workflowTaskDetailsInput.getWorkflowId(),
            workflowTaskDetailsInput.getActivityInstanceId())).map(Collections::singletonList)
            .orElse(Collections.emptyList());
      default:
        throw invalidInputError(WorkflowError.INPUT_INVALID,"activityId").get();
    }
  }

  /**
   *  0xF if workflowId, activityId and activityInstanceId are present
   *  0xE if workflowId and activityId is present
   *  0xC if only workflowId is present
   *  else it means invalid input, exception should be thrown.
   * @param workflowTaskDetailsInput input to be queried
   * @return operation code
   */
  private int calculateBitmask(Task.WorkflowTaskDetailsInput workflowTaskDetailsInput) {
    int val = 1;
    val = (val << 1) | (StringUtils.isBlank(workflowTaskDetailsInput.getWorkflowId()) ? 0 : 1);
    val = (val << 1) | (StringUtils.isBlank(workflowTaskDetailsInput.getActivityId()) ? 0 : 1);
    val = (val << 1) | (StringUtils.isBlank(workflowTaskDetailsInput.getActivityInstanceId()) ? 0 : 1);
    return val;
  }

  /**
   * Fetches Tasks based on workflowId/processInstanceId
   *
   * @param workflowId - process InstanceId
   * @param workflowOwnerId
   * @return list of tasks
   */
  private List<Task> fetchTasksByWorkflowId(String workflowId, Long workflowOwnerId) {
    List<ActivityDetail> activityDetails =
            activityDetailsRepository.findByProcessId(workflowId, workflowOwnerId);
    return getTasksFromActivityDetails(activityDetails, workflowId);
  }

  /**
   * fetches the task based on activityId and processInstanceId
   *
   * @param workflowId - processInstanceId
   * @param activityId - taskActivityId
   * @param workflowOwnerId
   * @return
   */
  private List<Task> fetchTasksByWorkflowAndActivityId(
          String workflowId, String activityId, Long workflowOwnerId) {
    List<ActivityDetail> activityDetails =
            activityDetailsRepository.findByProcessIdAndActivityId(
            workflowId, activityId, workflowOwnerId);
    return getTasksFromActivityDetails(activityDetails, workflowId);
  }

  /**
   * Maps ActivityDetail list to Task list
   *
   * @param activityDetails activity details
   * @param workflowId processInstanceId
   * @return list of Tasks
   */
  private List<Task> getTasksFromActivityDetails(
          List<ActivityDetail> activityDetails, String workflowId) {
    return activityDetails.stream()
        .map(
            activityDetail -> {
              Task task = new Task();
              mapActivityDetailsTask(task, activityDetail, workflowId);
              Map<String, TaskValueDefinition> attributeMap = new HashMap<>();
              attributeMap.putAll(populateAttributesFromActivityDefinition(activityDetail));
              task.setAttributes(populateTaskAttributes(attributeMap));
              return task;
            })
        .collect(Collectors.toList());
  }

  /**
   * fetches task based on externalTaskId If the response map from camunda isn't empty, the camunda
   * response map is given preference In case its empty, runtime attributes from activity progress
   * details table will be given preference
   *
   * @param taskId external Task Id
   * @param workflowId processInstanceId
   * @return
   */
  private Task fetchTasksByTaskId(String workflowId, String taskId) {
	  /**
	   * For cross-realm request requestOwnerId can be different from taskOwnerId.
	   * Hence, DB Query without ownerId is made.  
	   */
    Optional<ActivityProgressDetails> activityProgressDetailsOptional =
        activityProgressDetailsRepository.findById(taskId);
    return activityProgressDetailsOptional.map(activityProgressDetails -> {
      return prepareTask(workflowId, taskId, activityProgressDetails);
    }).orElse(null);
  }

  /**
   * 
   * @param workflowId
   * @param taskId
   * @param activityProgressDetails
   * @return
   */
  private Task prepareTask(String workflowId, String taskId,
      ActivityProgressDetails activityProgressDetails) {
    Task task = new Task();
    Map<String, TaskValueDefinition> attributeMap = new HashMap<>();
    mapActivityDetailsTask(task, activityProgressDetails.getActivityDefinitionDetail(),
        activityProgressDetails.getProcessDetails().getProcessId());
    mapActivityProgressDetails(task, activityProgressDetails);
    Map<String, TaskValueDefinition> attributesFromCamunda = 
        getRuntimeAttributesFromCamunda(workflowId, taskId);
    attributeMap.putAll(populateAttributesFromActivityDefinition(
        activityProgressDetails.getActivityDefinitionDetail()));
    attributeMap.putAll(populateAttributesFromActivityProgress(activityProgressDetails));
    attributeMap.putAll(MapUtils.isEmpty(attributesFromCamunda)?
        Map.of(): attributesFromCamunda);
    task.setAttributes(populateTaskAttributes(attributeMap));
    return task;
  }

  /**
   * Map Task and ActivityDetail
   */
  private void mapActivityDetailsTask(Task task, ActivityDetail activityDetail, String workflowId) {
    task.setActivityId(activityDetail.getActivityId());
    task.setName(activityDetail.getActivityName());
    task.setType(TaskTypeEnum.fromValue(activityDetail.getType().name()));
    task.setActivityType(activityDetail.getActivityType());
    task.setWorkflowId(workflowId);
  }

  /**
   * Map Task and ActivityProgressDetails
   */
  private void mapActivityProgressDetails(Task task,
                                          ActivityProgressDetails activityProgressDetails) {
    task.setActivityInstanceId(activityProgressDetails.getId());
    task.setStatus(activityProgressDetails.getStatus());
    task.setExternalRefId(Optional.ofNullable(activityProgressDetails.getTxnDetails())
        .map(TransactionDetails::getTxnId).orElse(null));
    task.setCreatedDate(new DateTime(activityProgressDetails.getStartTime(), DateTimeZone.UTC));
    task.setUpdatedDate(Optional.ofNullable(activityProgressDetails.getUpdatedTime())
        .map(timestamp -> new DateTime(activityProgressDetails.getUpdatedTime(), DateTimeZone.UTC))
        .orElse(null));
    task.setCompletedDate(Optional.ofNullable(activityProgressDetails.getEndTime())
        .map(timestamp -> new DateTime(activityProgressDetails.getEndTime(), DateTimeZone.UTC))
        .orElse(null));
    task.setWorkflowName(activityProgressDetails.getProcessDetails()
        .getDefinitionDetails().getTemplateDetails().getTemplateName());
  }

  /**
   * Populates TaskAttributes from ActivityDetail
   * @param activityDetail activityDetails from DB
   *
   */
  private Map<String, TaskValueDefinition> populateAttributesFromActivityDefinition(
          ActivityDetail activityDetail) {
    WorkflowActivityAttributes workflowActivityAttributes =
        ObjectConverter.fromJson(activityDetail.getAttributes(), WorkflowActivityAttributes.class);
    Map<String, TaskValueDefinition> taskAttributeMap = new HashMap<>();
    Optional.ofNullable(workflowActivityAttributes)
        .ifPresent(workflowAttributes -> {
          taskAttributeMap.putAll(populateAttributes(workflowAttributes.getModelAttributes(),Boolean.TRUE.booleanValue()));
          taskAttributeMap.putAll(populateAttributes(workflowAttributes.getRuntimeAttributes(),Boolean.FALSE.booleanValue()));
        });
    return taskAttributeMap;
  }

  /**
   * Populates TaskAttributes from ActivityProgressDetails and ActivityDetail
   */
  private Map<String, TaskValueDefinition> populateAttributesFromActivityProgress(ActivityProgressDetails activityProgressDetails) {
    WorkflowActivityAttributes workflowActivityAttributes =
        ObjectConverter
            .fromJson(activityProgressDetails.getAttributes(), WorkflowActivityAttributes.class);
    Map<String, TaskValueDefinition> taskAttributeMap = new HashMap<>();
    Optional.ofNullable(workflowActivityAttributes).ifPresent(
        workflowAttributes -> taskAttributeMap.putAll(populateAttributes(workflowAttributes.getRuntimeAttributes(),Boolean.FALSE.booleanValue())));
    return taskAttributeMap;
  }

  /**
   * Computes Map of String, TaskValueDefinition
   * @param source map of String, ?
   * @return Map
   */
  private Map<String, TaskValueDefinition> populateAttributes(Map<String, ?> source, boolean readOnly) {
    Map<String, TaskValueDefinition> attributeMap = new HashMap<>();
    Optional.ofNullable(source).ifPresent(sourceMap -> {
      JsonNode jsonNode = ObjectConverter.convertObject(source, JsonNode.class);
      Optional.ofNullable(jsonNode).ifPresent(
          node -> node.fieldNames().forEachRemaining(
              field -> {
                String val = null;
                JsonNode jn = jsonNode.get(field);
                if (jn.isArray() || jn.isObject()){
                  val = ObjectConverter.toJson(jn);
                }
                else if (!jn.isNull()){
                  val = jn.asText();
                }
                TaskValueDefinition vars = TaskValueDefinition.builder().value(val).readOnly(readOnly).build();
                attributeMap.put(field, vars);
              }));
    });
    return attributeMap;
  }

  /** Populates TaskAttribute list based on keys in attributes map. */
  private List<TaskAttribute> populateTaskAttributes(Map<String, TaskValueDefinition> attributes) {
    return Optional.ofNullable(attributes)
        .map(
            map ->
                map.entrySet().stream()
                    .map(
                        attribute -> {
                          TaskAttribute taskAttribute = new TaskAttribute();
                          taskAttribute.setName(attribute.getKey());
                          taskAttribute.setValue(attribute.getValue().getValue());
                          taskAttribute.setReadOnly(attribute.getValue().isReadOnly());
                          return taskAttribute;
                        })
                    .collect(Collectors.toList()))
        .orElse(Collections.emptyList());
  }

  /**
   * Supplier for WorkflowGeneralException
   */
  private Supplier<WorkflowGeneralException> invalidInputError(
          WorkflowError workflowError, String... arg) {
    return () -> new WorkflowGeneralException(workflowError, arg);
  }
  /**
   * Fetch task variables from Camunda
   */
  private Map<String, TaskValueDefinition> getRuntimeAttributesFromCamunda(String workflowId, String taskId) {
    return populateAttributes(camundaServiceManager.getExternalTaskVariables(workflowId, taskId),Boolean.FALSE.booleanValue());
  }

  /**
   * @param requestAttributeKeys : Mutation Request Variable Key Set
   * @param activityProgressDetails : ActivityProgressDetails object
   * @throws WorkflowGeneralException : If localVariables contains keys from modelAttributes
   *     (Extension Properties)
   */
  private void validateTaskAttributes(
      Set<String> requestAttributeKeys, ActivityProgressDetails activityProgressDetails) {

    // Throwing Error for invalid task id
    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(activityProgressDetails), WorkflowError.CAMUNDA_TASK_NOT_FOUND,
        activityProgressDetails.getId());

    // Testing if task is already completed(success) then throw error
    WorkflowVerfiy.verify(
        ObjectUtils.isNotEmpty(activityProgressDetails.getStatus())
            && ExternalTaskStatus.SUCCESS
                .getStatus()
                .equalsIgnoreCase(activityProgressDetails.getStatus()),
        WorkflowError.EXTERNAL_TASK_NOT_FOUND);

    Set<String> modelAttributeKeys =
        Optional.ofNullable(
                ObjectConverter.fromJson(
                    activityProgressDetails.getAttributes(), WorkflowActivityAttributes.class))
            .map(
                workflowAttributes ->
                    populateAttributes(workflowAttributes.getModelAttributes(), Boolean.TRUE)
                        .keySet())
            .orElse(Collections.emptySet());

    // check for overlapping keys
    Set<String> intersection = new HashSet<>(requestAttributeKeys);
    intersection.retainAll(modelAttributeKeys);
    WorkflowVerfiy.verify(
        intersection.size() > 0,
        WorkflowError.EXTENSION_ATTRIBUTES_MUTATION_ERROR,
        intersection.toString());
  }
  
  
  /**
   * Paginated Query to Get Workflow Tasks using recordId as filter.
   */
  @Override
  @Metric(name = MetricName.GET_WKFL_TASKS, type = Type.API_METRIC)
  public List<Task> getWkflTasks(WorkflowTaskRequestDetails taskRequest){
    List<String> processIds = null;
    if(CollectionUtils.isEmpty(taskRequest.getWorkflowNames())) {
      processIds = processDetailsRepository.findByRecordId(taskRequest.getRecordId());
    } else {
      processIds = processDetailsRepository.findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(
          taskRequest.getRecordId(), taskRequest.getWorkflowNames());
    }
    
    WorkflowVerfiy.verify(CollectionUtils.isEmpty(processIds),
        WorkflowError.WORKFLOW_NOT_FOUND,
        "Incorrect combination of recordId and workflowName provided");
    
    return fetchTasks(taskRequest, processIds);
  }

  
  /**
   * Fetch task for given TaskRequest and ProcessIds.
   * @param taskRequest
   * @param processIds
   * @return
   */
  private List<Task> fetchTasks(WorkflowTaskRequestDetails taskRequest, List<String> processIds) {
    int startPageNo = taskRequest.getOffset() > taskRequest.getLimit() ? 
        (taskRequest.getOffset() / taskRequest.getLimit()) : 0;

    List<Task> tasks = new ArrayList<>();
    /**
     * ForEach and Streaming can't work as internal break condition provided.
     */
    for (int pageNo = startPageNo; tasks.size() < taskRequest.getLimit(); pageNo++) {
      PageRequest pageRequest =
          PageRequest.of(pageNo, taskRequest.getLimit(), Sort.by(START_TIME).descending());
      /**
       * Paginated request will give objects which might have got responded in previous paginated request.
       * Spring Data only support limit based Pagination.
       */
      List<ActivityProgressDetails> activityProgressDetails =
          activityProgressDetailsRepository.findByProcessDetailsAndTypeIn(processIds, 
              taskRequest.getTaskTypes(), pageRequest);
      if (CollectionUtils.isEmpty(activityProgressDetails)) {
        break;
      }
      int offset = (taskRequest.getLimit() * pageNo) < taskRequest.getOffset() ? 
          taskRequest.getOffset() : (taskRequest.getLimit() * pageNo);
      tasks.addAll(fetchAuthorizedTasks(offset, taskRequest.getLimit(), activityProgressDetails));
    }
    int toIndex = tasks.size() < taskRequest.getLimit() ? tasks.size() : taskRequest.getLimit();
    /**
     * Remove additional object received in final Pagination call.
     */
    return tasks.subList(0, toIndex);
  }
  
  
  /**
   * For the given offset and limit, 
   * @param offset
   * @param limit
   * @param activityProgressDetails
   * @return
   */
  private List<Task> fetchAuthorizedTasks(int offset, int limit,
      List<ActivityProgressDetails> activityProgressDetails) {
    
    /**
     * Remove object from list to reach offset sent in the request.
     * Offset will vary as 2 downstream are involved for filtering - after DB query, AuthZ filter is applied.
     *        
     * There are 15 tasks : Task1, Task2,....Task15.
     * 
     * Consider for a realm=1234, following Tasks qualify to response. 
     * Task1, Task2, Task3, Task6, Task7, Task9, Task11, Task12.
     * 
     * For Request with realm=1234, offset=0, limit=5.
     * Response:: Task1(offset=0), Task2(offset=1), Task3(offset=2), Task6((offset=5)), Task7(offset=6).
     * 
     * Subsequent Request should be:: realm=1234, offset=7, limit=5.
     * Response:: Task9(offset=8), Task11(offset=10), Task12(offset=11).
     * 
     * From above example, for subsequent request Page=1 is queried from DB will give-
     * Task6(offset=5), Task7(offset=6), Task8(offset=7), Task9(offset=8), Task10(offset=9)
     * We need to skip till offset = 6.
     * To achieve skipping of object, following code is implemented.
     * 
     */
    final List<ActivityProgressDetails> activityProgressDetailList =
        activityProgressDetails.subList((offset % limit), activityProgressDetails.size());
    
    Map<String, Boolean> taskMap = authorizedTaskMap(activityProgressDetailList);
    
    List<Task> tasks = new ArrayList<>();
    /**
     * Iteration on activityProgressDetailList to maintain order.
     */
    IntStream.range(0, activityProgressDetailList.size()).forEach(iter -> {
      ActivityProgressDetails activityProgressDetail = activityProgressDetailList.get(iter);
      Task task = prepareTask(activityProgressDetail.getProcessDetails().getProcessId(),
          activityProgressDetail.getId(), activityProgressDetail);
      /**
       * Set offset here.
       */
      task.setOffset(offset+iter);
      /**
       * Filter after setting offset. This will allow skipping previous responded objects.
       * For processOwner = requestOwner, authZ check is not required. 
       * Hence, they are set as true. For others AuthZ will respond true or false.
       */
      if(taskMap.getOrDefault(task.getActivityInstanceId(), Boolean.TRUE)) {
        tasks.add(task);
      }
    });
    return tasks;
  }

  /**
   *  
   * @param offset
   * @param limit
   * @param activityProgressDetails
   * @return
   */
  
  
  /**
   * Map<TaskId, Boolean> is returned letting you inform which all tasks are authorised for the requestor. 
   * @param activityProgressDetails
   * @return Map<TaskId, Boolean>
   */
  private Map<String, Boolean> authorizedTaskMap(
      final List<ActivityProgressDetails> activityProgressDetails) {

    final Long requestOwnerId = Long.valueOf(this.contextHandler.get(WASContextEnums.OWNER_ID));

    Stream<WasAuthorizeRequest> authRequests = (Stream<WasAuthorizeRequest>) activityProgressDetails
        .stream().map(activityProgressDetail -> {
          final Long workflowOwnerId = activityProgressDetail.getProcessDetails().getOwnerId();
          final String taskOwnerId = getTaskOwner(activityProgressDetail);
          final String domain = getDomain(activityProgressDetail);

          return WasAuthorizeRequest.builder().permission(Permission.TASK_READ)
              .RequestOwnerId(requestOwnerId).workflowOwnerId(workflowOwnerId)
              .taskOwnerId(StringUtils.isNotEmpty(taskOwnerId) ? Long.parseLong(taskOwnerId) : null)
              .taskType(activityProgressDetail.getActivityDefinitionDetail().getType().name())
              .domain(domain).taskId(activityProgressDetail.getId()).build();
        });

    return accessVerifier.verifyBatchHumanTaskAccess(
        authRequests.filter(this::isAuthzCheckRequired).collect(Collectors.toList()));
  }

}