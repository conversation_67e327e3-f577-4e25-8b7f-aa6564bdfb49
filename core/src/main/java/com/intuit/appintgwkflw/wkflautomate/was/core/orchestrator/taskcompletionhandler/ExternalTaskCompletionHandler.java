package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.EventConsumerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskFailure;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskSuccess;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * External Task Completion Handler.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class ExternalTaskCompletionHandler implements TaskCompletionHandler{
    private CamundaRunTimeServiceRest camundaRest;
    private MetricLogger metricLogger;
    private StateTransitionService stateTransitionService;
    private ActivityProgressDetailsRepository activityProgressDetailRepo;

  /**
   * Completes External task in camunda
   *
   * @param event event payload
   * @param headers event headers
   */
  @Override
  public void completeTask(ExternalTaskCompleted event, Map<String, String> headers) {
    String entityId = headers.get(EventHeaderConstants.ENTITY_ID);

    Pair<String, String> taskIdAndWorkerId =
        EventConsumerUtil.getTaskIdAndWorkerId(entityId, getName());
    EventingLoggerUtil.logInfo(
        "Executing ExternalTaskComplete event step=eventCompletionStarted taskId=%s, entityId=%s, taskType=%s",
        this.getClass().getSimpleName(),
        taskIdAndWorkerId.getLeft(),
        entityId,
        getName().getEntityType());
    Long transitionTime = transitionTime();
    try {
      invokeCompleteCall(event, taskIdAndWorkerId);
    } catch (WorkflowGeneralException ex) {
      /**
       * Task not found exception is expected for DLQ Retry on Complete pass and stateTransition
       * failure. For such case, ignore exception and retry of stateTranstition event is required.
       */
      if (!WorkflowError.CAMUNDA_TASK_NOT_FOUND.equals(ex.getWorkflowError())
          && !WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR.equals(ex.getWorkflowError())) {
        /** re-throw other exception. */
        throw ex;
      }
    }
    /**
     * State Transition Event Publish based on checking entry in DB. ExternalTaskDetail is set null,
     * as this will be fetched later for complete status publish.
     */
    publishStateTransitionEvent(
        taskIdAndWorkerId.getKey(),
        ActivityConstants.TASK_STATUS_COMPLETE,
        headers,
        null,
        transitionTime);

    EventingLoggerUtil.logInfo(
        "ExternalTask marked complete in Camunda step=eventCompletionSuccessful taskId=%s, entityId=%s, taskType=%s",
        this.getClass().getSimpleName(),
        taskIdAndWorkerId.getLeft(),
        entityId,
        getName().getEntityType());
  }

	/**
	 *
	 * @return  Datetime on which Camunda API is getting invoked for transition of task.
	 */
	private Long transitionTime() {
		return Instant.now().toEpochMilli();
	}

  /**
   * @param event : {@link ExternalTaskCompleted}
   * @param taskIdAndWorkerId : {@link Pair<String,String>}
   *     <p>Make Call to Complete Task/Custom Complete Task API based on worker id input.
   */
  private void invokeCompleteCall(
      ExternalTaskCompleted event, Pair<String, String> taskIdAndWorkerId) {
    String workerId = taskIdAndWorkerId.getValue().trim();
    ExternalTaskSuccess success =
        ExternalTaskSuccess.builder()
            .variables(event.getVariables())
            .workerId(workerId)
            .localVariables(event.getLocalVariables())
            .build();
    if (StringUtils.isBlank(workerId) || ObjectUtils.isEmpty(workerId)) {
      camundaRest.customCompleteTask(success, taskIdAndWorkerId.getKey());
    } else {
      camundaRest.completeTask(success, taskIdAndWorkerId.getKey());
    }
  }

  /**
   * Creates an incident in camunda
   *
   * @param event event payload
   * @param headers event headers
   */
  @Override
  public void invokeFailure(ExternalTaskCompleted event, Map<String, String> headers) {
    Pair<String, String> taskIdAndWorkerId =
        EventConsumerUtil.getTaskIdAndWorkerId(
            headers.get(EventHeaderConstants.ENTITY_ID), getName());
    String workerId =
        taskIdAndWorkerId.getValue().trim(); // Trimming to remove empty trailing space
    // In case worker id is Empty, we will be setting null there and the flow will be handled via
    // custom Failure Task Endpoint
    ExternalTaskFailure failure =
        ExternalTaskFailure.builder()
            .workerId(
                ObjectUtils.isNotEmpty(workerId) && StringUtils.isNotBlank(workerId)
                    ? workerId
                    : null)
            .errorDetails(event.getErrorDetails())
            .errorMessage(event.getErrorMessage())
			.retries(0) // retries should be 0 to create an incident
            .build();
    createIncident(failure, taskIdAndWorkerId.getKey());
  }

	@Override
	public void handleFailure(Map<String, String> headers, Exception e) {
		metricLogger.logErrorMetric(MetricName.EVENT_EXTERNAL_TASK, Type.EVENT_METRIC, e);
		Pair<String, String> taskIdAndWorkerId = EventConsumerUtil
				.getTaskIdAndWorkerId(headers.get(EventHeaderConstants.ENTITY_ID), getName());

		// If event execution fails, and the task exists create an incident
		createIncident(getDefaultFailure(taskIdAndWorkerId.getValue(), e),
				taskIdAndWorkerId.getKey());
	}

	@Override
	public void extendLock(ExternalTaskCompleted event, Map<String, String> headers) {
		WorkflowVerfiy.verifyNull(event.getExtendDuration(), WorkflowError.INPUT_INVALID, "extendDuration");
		Pair<String, String> taskIdAndWorkerId =
				EventConsumerUtil.getTaskIdAndWorkerId(
						headers.get(EventHeaderConstants.ENTITY_ID), getName());
		String workerId =
				taskIdAndWorkerId.getValue().trim(); // Trimming to remove empty trailing space
		ExtendExternalTask extendRequest =
				ExtendExternalTask.builder()
						.workerId(StringUtils.isNotBlank(workerId) ? workerId : null)
						.newDuration(event.getExtendDuration())
				.build();
		camundaRest.extendLock(extendRequest, taskIdAndWorkerId.getLeft());
	}

	@Override
    public EventEntityType getName() {
        return EventEntityType.EXTERNALTASK;
    }

  /**
   * Creates incident in camunda.
   *
   * @param failure
   * @param taskId
   */
  private void createIncident(ExternalTaskFailure failure, String taskId) {
    /**
     * In case of Mutation API, we're passing workerId=" ". In case retry flows kicks in then it
     * should go to custom failure API where workerId is handled internally by Custom Failure API.
     */
    if (StringUtils.isBlank(failure.getWorkerId())) {
      camundaRest.customFailureTask(failure, taskId);
    } else {
      camundaRest.failureTask(failure, taskId);
    }

  }

	/**
	 * Generates an failure object with default message, which is used to create an incident
	 *
	 * @param workerId
	 * @param e
	 * @return
	 */
	private ExternalTaskFailure getDefaultFailure(String workerId, Exception e) {
		String errorMsg = "Exception while trying to close task. Creating incident";
		if (e instanceof WorkflowGeneralException &&
				null != ((WorkflowGeneralException) e).getWorkflowError()) {
			errorMsg = ((WorkflowGeneralException) e).getWorkflowError().name();
		}
		return ExternalTaskFailure.builder()
				.workerId(workerId)
				.errorDetails(ExceptionUtils.getStackTrace(e))
				.errorMessage(errorMsg)
				.build();
	}

	/**
	 * Update task status handling. Will update status at External Task scope and publish at Workflow
	 * Transition event bus.
	 *
	 * @param event
	 * @param headers
	 */
	@Override
	public void updateStatus(ExternalTaskCompleted event, Map<String, String> headers) {
		String entityId = headers.get(EventHeaderConstants.ENTITY_ID);
		ExternalTaskStatus statusEnum = ExternalTaskStatus.fromStatus(event.getStatus());
		final String status = event.getStatus().toUpperCase();
		Pair<String, String> taskIdAndWorkerId = EventConsumerUtil
				.getTaskIdAndWorkerId(entityId, getName());
		EventingLoggerUtil.logInfo(
				"Updating ExternalTaskStatus event step=TaskUpdateStarted taskId=%s, entityId=%s, taskType=%s status=%s",
				this.getClass().getSimpleName(), taskIdAndWorkerId.getLeft(), entityId,
				getName().getEntityType(), status);

		ExternalTaskDetail externalTaskDetail = camundaRest
				.getExtenalTaskDetails(taskIdAndWorkerId.getLeft());

		Long transitionTime = transitionTime();
		//Update Variables and status in Camunda.
		updateStatusInCamunda(statusEnum, status, externalTaskDetail, event);

		//State Transition Event Publish based on checking entry in DB.
		publishStateTransitionEvent(taskIdAndWorkerId.getKey(), ActivityConstants.TASK_EVENT_TYPE_UPDATE,
        		headers, externalTaskDetail, transitionTime);

		EventingLoggerUtil.logInfo(
				"Updating ExternalTaskStatus event step=TaskUpdateCompleted taskId=%s, entityId=%s, taskType=%s status=%s",
				this.getClass().getSimpleName(), taskIdAndWorkerId.getLeft(), entityId,
				getName().getEntityType(), status);
	}

	/**
	 * Update local variable in camunda for task update event.
	 *
	 * @param statusEnum         - status enum for backward compatibility with Camunda update.
	 * @param status             - free text status string for update events.
	 * @param externalTaskDetail - External Task details having executionId on which Camunda update
	 *                           variable to call.
	 */
	private void updateStatusInCamunda(ExternalTaskStatus statusEnum, final String status,
			ExternalTaskDetail externalTaskDetail, ExternalTaskCompleted event) {
		Map<String, Object> variableMap = Map
				.of(WorkflowConstants.EXT_ACTIVITY_STATUS_VARIABLE, statusMapJson(statusEnum, status));
		CamundaUpdateRequest executionUpdateRequest =
				CamundaServiceHelper
						.prepareExecutionUpdateRequest(externalTaskDetail.getExecutionId(), variableMap);
		if(!MapUtils.isEmpty(event.getLocalVariables())) {
			executionUpdateRequest.getModifications().putAll(CamundaServiceHelper
					.prepareModificationMap(event.getLocalVariables()));
		}
		camundaRest.updateExecutionVariables(executionUpdateRequest);
	}

	/**
	 * Status in Json string to save in Camunda Execution Update Call.
	 * @param statusEnum - Classified status.
	 * @param status - Free string status.
	 * @return json string having status of task.
	 */
	private String statusMapJson(ExternalTaskStatus statusEnum, final String status) {
		Map<String, Object> statusMap = new HashMap<>();
		statusMap
				.put(WorkflowConstants.UPDATE_TIME_VARIABLE, String.valueOf(System.currentTimeMillis()));
		statusMap
				.put(WorkflowConstants.ACTIVITY_STATUS_VARIABLE, null != statusEnum ? statusEnum.name() :
						status);

		return  ObjectConverter.toJson(statusMap);
	}


	/**
	 * Checks eventType validation for publishing state transition event and signals to publish if
	 * found.
	 *
	 * @param externalTaskId     :: External Task id
	 * @param eventType          :: type of event to publish state transition event.
	 * @param headers            :: Map of headers.
	 * @param externalTaskDetail :: Fetched externalTask Camunda details to be used to fetch
	 *                           variables.
	 * @param transitionTime     :: time on which Camunda API was invoked for transition of task.
	 */
	public void publishStateTransitionEvent(String externalTaskId, String eventType,
			Map<String, String> headers, ExternalTaskDetail externalTaskDetail,
			Long transitionTime) {
		activityProgressDetailRepo.findById(externalTaskId).filter(activityProgressDtl ->
				stateTransitionService.isStateTransitionEnabled(activityProgressDtl, eventType))
				.ifPresent(activityProgressDetail -> {
					/**
					 * If-else branching here as for Update event, we already have externalTaskDetail
					 * having executionId for update whereas for complete we have make Camunda API calls and fetch them.
					 * To avoid duplicate call different methods are invoked.
					 */
					if (ActivityConstants.TASK_STATUS_COMPLETE.equals(eventType)) {
						stateTransitionService.publishCompleteEvent(activityProgressDetail,
								headers, externalTaskId, transitionTime);
					} else {
						stateTransitionService.publishUpdateEvent(
								headers, externalTaskDetail, activityProgressDetail, transitionTime);
					}
				});
	}

	/**
	 * Calculates retries by invoking GET external-task id API
	 * Invokes camunda external-task/failure API with retryTimeout and retries parameter
	 * @throws WorkflowGeneralException with {@link WorkflowError#TASK_FAIL_WITH_RETRIES_EXHAUSTED} error
	 * @param event
	 * @param headers
	 */
	@Override
	public void invokeFailureWithRetry(ExternalTaskCompleted event, Map<String, String> headers) {
		Pair<String, String> taskIdAndWorkerId =
				EventConsumerUtil.getTaskIdAndWorkerId(
						headers.get(EventHeaderConstants.ENTITY_ID), getName());
		ExternalTaskDetail externalTaskDetail = camundaRest.getExtenalTaskDetails(taskIdAndWorkerId.getLeft());
		int retries;
		if (null == externalTaskDetail.getRetries()){
			retries = event.getRetries();
			WorkflowLogger.logInfo("No retry config exists. Setting retries=%s for task=%s",
					retries, taskIdAndWorkerId.getLeft());
		}
		else {
			retries = externalTaskDetail.getRetries() - 1;
			WorkflowLogger.logInfo("Retry config exists. Setting retries=%s for task=%s",
					retries, taskIdAndWorkerId.getLeft());
		}
		WorkflowLogger.logInfo("Total retries left=%s", retries);
		if (retries < 1){
			throw new WorkflowGeneralException(WorkflowError.TASK_FAIL_WITH_RETRIES_EXHAUSTED);
		}
		ExternalTaskFailure failure =
				ExternalTaskFailure.builder()
						.workerId(taskIdAndWorkerId.getRight())
						.errorDetails(event.getErrorDetails())
						.errorMessage(event.getErrorMessage())
						.retryTimeout(event.getExtendDuration())
						.retries(retries)
						.build();
		createIncident(failure, taskIdAndWorkerId.getLeft());
	}

}
