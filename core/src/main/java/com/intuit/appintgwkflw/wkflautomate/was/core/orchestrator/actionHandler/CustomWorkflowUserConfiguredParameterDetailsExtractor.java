package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class CustomWorkflowUserConfiguredParameterDetailsExtractor
        implements AppConnectParameterDetailExtractor {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final ProcessDetailsRepoService processDetailsRepoService;
  private final FilterParameterExtractorUtil filterParameterExtractorUtil;

  /**
   * As part of single definition, parameter details are filled using the user placeholder values.
   * In case of custom-workflows, all non-configurable and configurable parameters are extracted
   * using the config The values of the respective configurable parameters are filled using the user
   * placeholder values
   *
   * @param workerActionRequest
   * @return ParameterDetailsMap
   */
  @Override
  @Metric(
          name = MetricName.CUSTOM_WORKFLOW_USER_CONFIGURED_PARAMETER_DETAILS_EXTRACTOR,
          type = Type.APPLICATION_METRIC)
  public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
          WorkerActionRequest workerActionRequest) {

    Optional<DefinitionDetails> definitionDetails =
            processDetailsRepoService.findByProcessIdWithoutDefinitionData(
                    workerActionRequest.getProcessInstanceId());
    WorkflowVerfiy.verify(!definitionDetails.isPresent(), WorkflowError.DEFINITION_NOT_FOUND);
    CustomWorkflowType customWorkflowType =
            CustomWorkflowType.getCustomWorkflowForTemplateName(
                    definitionDetails.get().getTemplateDetails().getTemplateName());

    String recordType = workerActionRequest.getInputVariables().get(ENTITY_TYPE);
    String actionKey = customWorkflowType.getActionKey();
    String actionId = workerActionRequest.getActivityId();
    // Find relevant config action entry. From this entry, we will get handler details and other
    // parameters
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action recordAction =
            CustomWorkflowUtil.getRecordActionFromConfig(
                    customWorkflowConfig, recordType, actionKey, actionId);
    Map<String, HandlerDetails.ParameterDetails> actionParameters =
            recordAction.getParameters().stream()
                    .collect(
                            Collectors.toMap(
                                    Parameter::getName,
                                    CustomWorkflowUtil::getParameterDetailsFromActionParameter));
    Map<String, HandlerDetails.ParameterDetails> helpVariableParamMap =
            CustomWorkflowUtil.getHelpVariableParameterMap(recordAction);

    // Combining the action parameter details with help variable parameter details
    actionParameters.putAll(helpVariableParamMap);

    JSONObject userVariablesActionData =
            SingleDefinitionUtil.getActionPlaceholderValue(
                    definitionDetails.get(), actionKey, actionId);
    // Replacing the empty parameterDetails field values with the user placeholder values
    SingleDefinitionUtil.fillParameterDetails(actionParameters, userVariablesActionData);
    //Adding filter record type and filter condition in action parameters .
    //We require this because was need to pass these params to appconnect for duzzit execution
    actionParameters.putAll(filterParameterExtractorUtil.getFilterParameterDetails(definitionDetails.get()));

    return Optional.of(actionParameters);
  }




}