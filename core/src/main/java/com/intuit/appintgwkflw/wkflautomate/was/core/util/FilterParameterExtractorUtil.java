package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;


/**
 * <AUTHOR>
 * Util to extract filter parameters for single and custom defination.
 * i.e FilterCondition: "{\"rules\":[{\"parameterName\":\"Customer\", \"parameterType\": \"LIST\", \"$sdk_validated\":  \"true\" ,\"conditionalExpression\":\"CONTAINS 1\"},{\"parameterName\":\"StatementType\",\"parameterType\": \"LIST\", \"$sdk_validated\":  \"true\", \"conditionalExpression\":\"CONTAINS BALANCE_FORWARD\"}]}";
 * FilterRecordType: ['statement']
 * FilterCloseTaskConditions: ['txn_paid']
 */

@Component
@AllArgsConstructor
public class FilterParameterExtractorUtil {

    private final CustomWorkflowConfig customWorkflowConfig;
    private final DefinitionServiceHelper definitionServiceHelper;
    private final ReportAccessValidator reportAccessValidator;

    /**
     * This method gets the parameter details to be set as filter conditions from definition object
     *
     * @param definition: definition to extract rules and close-task conditions {@link Definition }
     * @return filled parameter details json
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterParameterDetails(
        Definition definition) {
        return getFilterParameterDetails(definition, Collections.emptyMap(), null);
    }

    /**
     * This function gets the filter parameter to be set as filter conditions from the definition
     * object, workflowStepMap and current workflow step id
     *
     * @param definition
     * @param workflowStepMap
     * @param currentWorkflowStepId
     * @return
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterParameterDetails(
        Definition definition, Map<String, WorkflowStep> workflowStepMap,
        String currentWorkflowStepId) {

        Map<String, HandlerDetails.ParameterDetails> triggers = CustomWorkflowUtil.getTriggerFromDefinition(
            definition);
        // we are converting the array to a string since appconnect is not able to handle list values.
        // bug appconnect is tracked here https://jira.intuit.com/browse/IPPC-7216
        // target state notification workflows are to triggered via domain events.
        if (triggers.containsKey(AsyncTaskConstants.ENTITY_OPERATION)) {
            convertEntityOperationInTrigger(triggers);
        }
        Map<String, HandlerDetails.ParameterDetails> filterParameterDetails = new HashMap<>();
        filterParameterDetails.putAll(triggers);
        //Adding filter conditions in filter parameter details map
        List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition,
            workflowStepMap, currentWorkflowStepId);

        // Validate report access for report-type workflows
        validateReportAccess(definition, rules);

        Map<String, List<RuleLine.Rule>> ruleMap = new HashMap<>();
        ruleMap.put(WorkflowConstants.CONDITION_RULES, rules);
        String rulesJson = ObjectConverter.toJson(ruleMap);

        filterParameterDetails.putAll(getFilterConditionParameterMap(rulesJson));
        //Adding filter record type in filter parameter details map
        filterParameterDetails.putAll(getFilterRecordTypeParameterMap(definition.getRecordType()));
        //Adding filter close task condition in filter parameter details map
        filterParameterDetails.putAll(getFilterCloseTaskConditionParameterMap(definition));

        return filterParameterDetails;
    }

    /**
     * This method gets the parameter details to be set as filter conditions and filter params from the definitions details
     *
     * @param definitionDetails: definition to extract rules and close-task conditions {@link DefinitionDetails }
     * @return filled parameter details json
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterParameterDetails(DefinitionDetails definitionDetails){
        Map<String, HandlerDetails.ParameterDetails> filterParameterDetails = new HashMap<>();
        Optional<List<DefinitionDetails>> definitions =
                definitionServiceHelper.findByParentId(definitionDetails.getDefinitionId());
        if (definitions.isPresent()) {
            // As of now each bpmn has only single dmn associated.
            DefinitionDetails dmnDefinitions = definitions.get().get(0);
            //Adding filter conditions in filter parameter details map
            JSONObject definitionPlaceholder = new JSONObject(dmnDefinitions.getPlaceholderValue());
            Map<String, Object > ruleMap = new HashMap<>();
            ruleMap.put(WorkflowConstants.CONDITION_RULES, definitionPlaceholder.toMap().get(WorkflowConstants.RULE_LINE_VARIABLES));
            String rulesJson = ObjectConverter.toJson(ruleMap);

            // Validate report access for report-type workflows
            validateReportAccessFromPlaceholder(definitionDetails, definitionPlaceholder);

            filterParameterDetails.putAll(getFilterConditionParameterMap(rulesJson));
            //Adding filter record type in filter parameter details map
            filterParameterDetails.putAll(getFilterRecordTypeParameterMap(definitionDetails.getRecordType().getRecordType()));
        }
        return filterParameterDetails;
    }

    /**
     * This method gets the filter condition parameter map
     *
     * @param rules: definition to extract rules {@link Definition }
     * @return filter condition parameter map
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterConditionParameterMap(String rules){

        Map<String, HandlerDetails.ParameterDetails> filterConditionMap = new HashMap<>();
        HandlerDetails.ParameterDetails parameterDetails = new HandlerDetails.ParameterDetails();
        parameterDetails.setFieldValue(Arrays.asList(rules));
        parameterDetails.setRequiredByHandler(true);
        filterConditionMap.put(WorkflowConstants.FILTER_CONDITION, parameterDetails);

        return filterConditionMap;
    }

    /**
     * This method gets the filter record type parameter map
     *
     * @param recordType: definition to extract record type
     * @return  filter record type parameter map
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterRecordTypeParameterMap(String recordType){

        Map<String, HandlerDetails.ParameterDetails> filterRecordTypeMap = new HashMap<>();
        HandlerDetails.ParameterDetails parameterDetails = new HandlerDetails.ParameterDetails();
        parameterDetails.setFieldValue(Arrays.asList(recordType));
        parameterDetails.setRequiredByHandler(true);
        filterRecordTypeMap.put(WorkflowConstants.FILTER_RECORD_TYPE, parameterDetails);

        return filterRecordTypeMap;
    }

    /**
     * This method gets the filter close task condition  parameter map
     *
     * @param definition: definition to extract close-task conditions {@link Definition }
     * @return  filter close task condition parameter map
     */
    public Map<String, HandlerDetails.ParameterDetails> getFilterCloseTaskConditionParameterMap(Definition definition){

        InputParameter closeTaskInputParameters = getCloseTaskCondition(definition);

        Map<String, HandlerDetails.ParameterDetails> filterCloseTaskMap = new HashMap<>();
        if (Objects.nonNull(closeTaskInputParameters)) {
            HandlerDetails.ParameterDetails parameterDetails = new HandlerDetails.ParameterDetails();
            parameterDetails.setFieldValue(closeTaskInputParameters.getFieldValues());
            filterCloseTaskMap.put(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS, parameterDetails);

        }
        return filterCloseTaskMap;
    }


    /**
     * Extracts parameter details from close-task under create-task action This is required to be
     * passed to app-connect duzzit for getting close task conditions
     *
     * @param definition : definition instance {@link Definition}
     * @return close-task parameter details
     */
    private InputParameter getCloseTaskCondition(Definition definition) {

        Predicate<WorkflowStep.ActionMapper> isCreateTaskPredicate =
                actionType ->
                        WorkflowConstants.CREATE_TASK.equalsIgnoreCase(
                                actionType.getAction().getId().getLocalId().split(WorkflowConstants.UNDERSCORE)[0]);

        WorkflowStep.ActionMapper action =
                definition.getWorkflowSteps(0).getActions().stream()
                        .filter(isCreateTaskPredicate)
                        .findFirst()
                        .orElse(null);

        // if there is no create task action as part of definition, return null
        if (Objects.isNull(action)) {
            return null;
        }

        Optional<InputParameter> closeTaskParameter =
                action.getAction().getParameters().stream()
                        .filter(actionDetail -> WorkflowConstants.CLOSE_TASK.equalsIgnoreCase(actionDetail.getParameterName()))
                        .findFirst();
        // if close task parameter is not present in definition, find the close task parameter from
        // config. And if the config close task parameter has default field values, then use the close
        // task parameter otherwise throw error
        if (!closeTaskParameter.isPresent()
                || (action.getAction().isSelected()
                && CollectionUtils.isEmpty(closeTaskParameter.get().getFieldValues()))) {
            Parameter configCloseTaskParameter =
                    getCloseTaskParameterFromConfig(action.getActionKey(), definition.getRecordType());
            closeTaskParameter = CustomWorkflowUtil.getCloseTaskInputParameter(configCloseTaskParameter);
        }

        return closeTaskParameter.orElseThrow(
                () -> new WorkflowGeneralException(WorkflowError.CLOSE_TASK_ACTION_NOT_FOUND));
    }


    /**
     * Get close task parameter from config for the given action key and record type
     *
     * @param actionKey
     * @param recordType
     * @return
     */
    private Parameter getCloseTaskParameterFromConfig(String actionKey, String recordType) {
        return customWorkflowConfig.getRecordObjForType(recordType).getActionGroups().stream()
                .filter(actionGroup -> actionKey.equalsIgnoreCase(actionGroup.getId()))
                .map(com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup::getActions)
                .flatMap(Collection::stream)
                .filter(action -> WorkflowConstants.CREATE_TASK.equalsIgnoreCase(action.getId()))
                .map(
                        com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action
                                ::getParameters)
                .flatMap(Collection::stream)
                .filter(parameter -> WorkflowConstants.CLOSE_TASK.equalsIgnoreCase(parameter.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     if parameterDetails has parameter set as ["Create","Update"]
     it gets converted to ["Create,Update"]
     */
    private void convertEntityOperationInTrigger(Map<String, HandlerDetails.ParameterDetails> triggers) {
        HandlerDetails.ParameterDetails parameterDetails = triggers.get(AsyncTaskConstants.ENTITY_OPERATION);
        String entityOperation = String.join(",",
                triggers.get(AsyncTaskConstants.ENTITY_OPERATION).getFieldValue());
        parameterDetails.setFieldValue(Arrays.asList(entityOperation));
        triggers.put(AsyncTaskConstants.ENTITY_OPERATION, parameterDetails);
    }

    /**
     * Validates report access for report-type workflows to prevent unauthorized access
     * to reports through workflow automation.
     *
     * @param definition The workflow definition
     * @param rules List of rules from the workflow definition
     * @throws WorkflowGeneralException if user doesn't have access to specified report IDs
     */
    private void validateReportAccess(Definition definition, List<RuleLine.Rule> rules) {
        // Only validate for report-type workflows
        if (!"report".equalsIgnoreCase(definition.getRecordType())) {
            return;
        }

        WorkflowLogger.logInfo("step=validateReportAccess, recordType=%s, rulesCount=%d",
            definition.getRecordType(), rules.size());

        for (RuleLine.Rule rule : rules) {
            if ("ReportId".equalsIgnoreCase(rule.getParameterName())) {
                reportAccessValidator.validateReportIdsInExpression(rule.getConditionalExpression());
            }
        }
    }

    /**
     * Validates report access for workflows created from stored definition details.
     *
     * @param definitionDetails The definition details
     * @param definitionPlaceholder The placeholder JSON containing rule variables
     */
    @SuppressWarnings("unchecked")
    private void validateReportAccessFromPlaceholder(DefinitionDetails definitionDetails, JSONObject definitionPlaceholder) {
        // Only validate for report-type workflows
        if (!"report".equalsIgnoreCase(definitionDetails.getRecordType().getRecordType())) {
            return;
        }

        WorkflowLogger.logInfo("step=validateReportAccessFromPlaceholder, recordType=%s",
            definitionDetails.getRecordType().getRecordType());

        try {
            Object ruleLineVariables = definitionPlaceholder.toMap().get(WorkflowConstants.RULE_LINE_VARIABLES);
            if (ruleLineVariables instanceof List) {
                List<Map<String, Object>> rulesList = (List<Map<String, Object>>) ruleLineVariables;

                for (Map<String, Object> rule : rulesList) {
                    String parameterName = (String) rule.get(WorkflowConstants.PARAMETER_NAME);
                    String conditionalExpression = (String) rule.get(WorkflowConstants.CONDITIONAL_EXPRESSION);

                    if ("ReportId".equalsIgnoreCase(parameterName)) {
                        reportAccessValidator.validateReportIdsInExpression(conditionalExpression);
                    }
                }
            }
        } catch (Exception ex) {
            WorkflowLogger.logError("step=validateReportAccessFromPlaceholder, error=%s", ex.getMessage());
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS,
                "Failed to validate report access from stored definition", ex);
        }
    }
}
