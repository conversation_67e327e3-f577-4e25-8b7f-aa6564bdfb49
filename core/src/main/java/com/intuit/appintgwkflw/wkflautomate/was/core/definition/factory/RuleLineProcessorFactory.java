package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiSplitRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.RuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.EmptyConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * This is a factory class that returns the type of ruleLine processor to create the rule lines
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class RuleLineProcessorFactory {

  private final MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
  private final EmptyConditionRuleLineProcessor emptyConditionRuleLineProcessor;
  @Qualifier(WorkflowBeansConstants.CREATE_MULTI_SPLIT_RULE_LINE_PROCESSOR)
  private final MultiSplitRuleLineProcessor multiSplitRuleLineProcessor;

  private final int MIN_SIBLINGS_FOR_MULTI_SPLIT = 1;
  /**
   * @param isSingleStep
   * @param siblings number of siblings for workflow steps
   * @return {@link RuleLineProcessor}
   */
  public RuleLineProcessor getHandler(boolean isSingleStep, List<WorkflowStep> siblings) {

    if (Objects.nonNull(siblings) && siblings.size() > MIN_SIBLINGS_FOR_MULTI_SPLIT ) {
      return multiSplitRuleLineProcessor;
    }
    return isSingleStep ? emptyConditionRuleLineProcessor : multiConditionRuleLineProcessor;
  }
}
