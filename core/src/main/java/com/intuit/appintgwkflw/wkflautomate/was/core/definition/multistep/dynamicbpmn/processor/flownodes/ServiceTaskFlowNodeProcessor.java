package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.ServiceTaskImpl;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for -
 * 1. adding extension elements to ServiceTask element
 * 2. adding implicit elements to ServiceTask element
 * 3. adding ServiceTask element to existing subprocess
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ServiceTaskFlowNodeProcessor implements DynamicBpmnFlowNodeProcessor {

  private final DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  @Override
  public BpmnComponentType getType() {
    return BpmnComponentType.SERVICE_TASK;
  }

  @Override
  public void addExtensionElements(
      FlowNode flowNode,
      FlowNode baseTemplateFlowNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    if (Objects.isNull(baseTemplateFlowNode) && Objects.nonNull(flowNode)
        && Objects.nonNull(baseTemplateBpmnModelInstance)) {
      baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById(flowNode.getId());
    }

    if (Objects.isNull(flowNode) && Objects.nonNull(baseTemplateFlowNode)
        && Objects.nonNull(bpmnModelInstance)){
      flowNode = bpmnModelInstance.getModelElementById(baseTemplateFlowNode.getId());
    }

    if (Objects.isNull(flowNode) || Objects.isNull(baseTemplateFlowNode)) {
      WorkflowLogger.logError(
          "Unable to add extension elements as both ServiceTask and BaseTemplateServiceTask found null.");
      return;
    }

    dynamicBpmnExtensionElementsHelper.addAllValidExtensionElements(
        flowNode, baseTemplateFlowNode, bpmnModelInstance);
  }

  @Override
  public void addEventToSubProcess(
      FlowNode sourceNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance) {
    Optional<ServiceTask> precannedServiceTask =
        baseTemplateSubprocess.getChildElementsByType(ServiceTask.class).stream().findFirst();

    if (precannedServiceTask.isPresent()) {
      subProcess
          .builder()
          .moveToNode(sourceNode.getId())
          .serviceTask(precannedServiceTask.get().getId())
          .name(precannedServiceTask.get().getName())
          .camundaTopic(precannedServiceTask.get().getCamundaTopic())
          .camundaType(precannedServiceTask.get().getCamundaType());
    }
  }

  @Override
  public void addImplicitEvents(
      FlowNode sourceFlowNode,
      FlowNode baseTemplateTargetNode,
      SequenceFlow outgoingSequenceFlow,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    sourceFlowNode
        .builder()
        .sequenceFlowId(outgoingSequenceFlow.getId())
        .condition(outgoingSequenceFlow.getName(), outgoingSequenceFlow.getTextContent())
        .serviceTask(baseTemplateTargetNode.getId())
        .name(baseTemplateTargetNode.getName())
        .camundaTopic(((ServiceTaskImpl) baseTemplateTargetNode).getCamundaTopic())
        .camundaType(((ServiceTaskImpl) baseTemplateTargetNode).getCamundaType());
  }
}
