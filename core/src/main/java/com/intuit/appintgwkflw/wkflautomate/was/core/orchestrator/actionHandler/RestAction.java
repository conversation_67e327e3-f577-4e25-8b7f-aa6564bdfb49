package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;

/** <AUTHOR> */
public interface RestAction {

  /**
   * @param <REQUEST> input request type
   * @param <RESPONSE> response type
   * @param wasHttpRequest input was http request
   * @return {@link WASHttpResponse}
   */
  <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> execute(
      WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest);
}
