package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.RestAction;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.AppconnectRecordListFetcherMock;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.RecordListFetcher;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppConnectServiceMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppconnectWasClientMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppconnectWorkflowHeaderActionMockImpl;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Created by ssingh14 on 27/04/20. This loads AppConnectServiceMockImpl in the prf environment.
 * This is done to mock the app connect network calls in the prf environment. TODO: Remove this
 * class once app connect is available in prf environment.
 */
@Configuration
@ConditionalOnProperty(name = "app.env", havingValue = "prfa")
public class AppConnectMockConfig {

  @Bean
  public AppConnectService appConnectService() {
    //TODO using to debug perf runs, will remove it post that.
    WorkflowLogger.logInfo("Initializing appconnectMockConfig.appConnectService");
    return new AppConnectServiceMockImpl();
  }

  @Primary
  @Bean
  public RestAction appConnectWASClientMock() {
    return new AppconnectWorkflowHeaderActionMockImpl();
  }

  @Primary
  @Bean
  public AppConnectWASClient wasClient() {
    return new AppconnectWasClientMockImpl();
  }

  @Primary
  @Bean
  public RecordListFetcher recordListFetcher() {
    return new AppconnectRecordListFetcherMock();
  }
}
