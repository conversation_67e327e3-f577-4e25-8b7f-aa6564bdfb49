package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for report access validation in workflow automation.
 * This configuration helps control the security feature that prevents users from
 * bypassing UI controls to access unauthorized reports.
 * 
 * <AUTHOR> Team
 */
@Configuration
@ConfigurationProperties(prefix = "workflow.report-access-validation")
@Data
public class ReportAccessValidationConfig {
    
    /**
     * Whether report access validation is enabled globally.
     * When disabled, all report access validation is bypassed.
     * Default: true (enabled for security)
     */
    private boolean enabled = true;
    
    /**
     * Feature flag name for controlling report access validation.
     * This allows runtime control via IXP feature flags.
     */
    private String featureFlag = "SBSEG-QBO-was-report-access-validation";
    
    /**
     * Whether to enforce strict cross-realm validation.
     * When enabled, users cannot access reports from other realms/companies.
     * Default: true (enabled for security)
     */
    private boolean strictCrossRealmValidation = true;
    
    /**
     * Whether to log report access validation attempts.
     * Useful for monitoring and auditing purposes.
     * Default: true
     */
    private boolean auditLogging = true;
    
    /**
     * Maximum number of report IDs allowed in a single workflow definition.
     * This prevents abuse where users try to access many reports at once.
     * Default: 10
     */
    private int maxReportIdsPerWorkflow = 10;
    
    /**
     * Whether to validate report access during workflow execution.
     * When enabled, report access is validated both at creation time and execution time.
     * Default: false (only validate at creation time for performance)
     */
    private boolean validateAtExecution = false;
    
    /**
     * Timeout in milliseconds for report access validation calls.
     * Default: 5000ms (5 seconds)
     */
    private long validationTimeoutMs = 5000;
    
    /**
     * Whether to fail fast on validation errors.
     * When true, throws exception immediately on first validation failure.
     * When false, collects all validation errors and throws at the end.
     * Default: true (fail fast for security)
     */
    private boolean failFast = true;
}
