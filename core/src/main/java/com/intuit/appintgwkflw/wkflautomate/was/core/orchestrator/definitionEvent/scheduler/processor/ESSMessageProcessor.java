package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.processor;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ClearContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SQSConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.message.ScheduleMessageExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Map;

import com.intuit.identity.authn.offline.sdk.exceptions.UnauthorizedProductImpersonationException;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.aws.messaging.listener.Acknowledgment;
import org.springframework.stereotype.Component;

/** <AUTHOR> This class process the ESS messasge and maually acknowledge it. */
@Component
@AllArgsConstructor
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class ESSMessageProcessor {

  private final ScheduleMessageExecutor scheduleMessageExecutor;

  private final SQSConfig sqsConfig;

  private final MetricLogger metricLogger;

  /**
   * Process the ESS message and acknowledge the message when successfully executed or number of
   * retries are exhausted.
   *
   * @param message
   * @param acknowledgment
   * @param headers
   */
  @Metric(name = MetricName.ESS_SQS_MESSAGE, type = Type.EVENT_METRIC)
  public void process(
      final String message,
      final Acknowledgment acknowledgment,
      final Map<String, String> headers) {
    try {
      scheduleMessageExecutor.transformAndExecute(message);
    } catch (WorkflowRetriableException | WorkflowEventException |
             OfflineTicketClientException ex) {

      // we are adding this check to avoid unauthorized companies and logging them as warning. All unauthorized requests are fatal.
      if(WasUtils.isUnauthorisedException(ex)) {
        // skipping further process as giving realm is either locked out or test company, retry will never succeed.
        EventingLoggerUtil.logWarning(
                "step=skipProcessing Unauthorized Exception occurred while processing the message=%s exception=%s",
                this.getClass().getSimpleName(), message, ex.getMessage());
        acknowledgment.acknowledge();
        return;
      }
      if (!checkRetriesExhausted(headers)) {
        EventingLoggerUtil.logWarning(
            "step=sqs_retryable_exception receiveCount=%s retryCount=%s error=%s",
            this.getClass().getSimpleName(),
            headers.get(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT),
            sqsConfig.getRetryCount(),
            ex);
        throw ex;
      }
      EventingLoggerUtil.logError(
          "step=numberOfRetriesExhausted receiveCount=%s retryCount=%s",
          this.getClass().getSimpleName(),
          headers.get(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT),
          sqsConfig.getRetryCount());
      metricLogger.logErrorMetric(MetricName.ESS_SQS_MESSAGE_RETRY_EXHAUST, Type.EVENT_METRIC, ex);
    } catch (Exception exception) {
      EventingLoggerUtil.logError(
          "Exception occurred while processing the message=%s exception=%s",
          this.getClass().getSimpleName(), message, exception.getMessage());
    }
    acknowledgment.acknowledge();
  }

  /**
   * This method checks retries are exhausted for the ess message or not.
   *
   * @param headers
   * @return
   */
  private boolean checkRetriesExhausted(final Map<String, String> headers) {
    return headers.get(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT) == null
        || Integer.parseInt(headers.get(WorkflowConstants.SQS_APPROXIMATE_RECEIVE_COUNT))
            >= sqsConfig.getRetryCount();
  }
}
