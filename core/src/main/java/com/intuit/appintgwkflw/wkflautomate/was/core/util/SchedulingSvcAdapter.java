package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.SchedulingSvcConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.SchedulingSvcException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.SchedulingSvcClient;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.SCHEDULING_SERVICE_BAD_REQUEST_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.SCHEDULING_SERVICE_CALL_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_TID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_OFFERING_HEADER;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class SchedulingSvcAdapter {

    private final SchedulingSvcConfig schedulingSvcConfig;
    private final SchedulingSvcClient schedulingSvcClient;
    private final HeaderPopulator headerPopulator;
    private final WASContextHandler wasContextHandler;
    private final OfflineTicketClient offlineTicketClient;

    /**
     * Invokes the schedule service for creating or updating schedules.
     *
     * @param eventSchedules List of event schedules to be created or updated.
     * @param isUpdate Flag indicating whether the operation is an update.
     * @return List of created or updated schedules.
     */
    public List<SchedulingSvcResponse> invokeScheduleServiceForCreateUpdate(
            List<SchedulingSvcRequest> eventSchedules, Boolean isUpdate, String realmId) {

        List<SchedulingSvcResponse> responses = new ArrayList<>();

        for (SchedulingSvcRequest eventSchedule : eventSchedules) {
            WASHttpResponse<SchedulingSvcResponse> response =
                    schedulingSvcClient.httpResponse(prepareRequestPayloadForCreateUpdate(eventSchedule, isUpdate, realmId));

            // verify the response code
            verifyAndThrowException(response);
            responses.add(response.getResponse());
        }
        return responses;
    }

    /**
     * Invokes the schedule service for retrieving schedules.
     *
     * @param scheduleIds List of schedule IDs to be retrieved.
     * @return List of retrieved schedules.
     */
    public List<SchedulingSvcResponse> invokeScheduleServiceForGet(List<String> scheduleIds, String realmId) {
        List<SchedulingSvcResponse> responses = new ArrayList<>();
        for(String id : scheduleIds) {
            WASHttpResponse<SchedulingSvcResponse> response =
                    schedulingSvcClient.httpResponse(prepareRequestPayloadForGet(id, realmId));
            // verify the response code
            verifyAndThrowException(response);
            responses.add(response.getResponse());
        }
        return responses;
    }

    /**
     * Invokes the schedule service for deleting schedules.
     *
     * @param scheduleIds List of schedule IDs to be deleted.
     * @return List of deleted schedules.
     */
    public List<SchedulingSvcResponse> invokeScheduleServiceForDelete(List<String> scheduleIds, String realmId) {
        List<SchedulingSvcResponse> responses = new ArrayList<>();
        for(String id : scheduleIds) {
            WASHttpResponse<SchedulingSvcResponse> response =
                    schedulingSvcClient.httpResponse(prepareRequestPayloadForDelete(id, realmId));

            // verify the response code
            verifyAndThrowException(response);
            responses.add(response.getResponse());
        }
        return responses;
    }

    /**
     * Prepares the request payload for creating or updating schedules.
     *
     * @param eventSchedule The event schedule request.
     * @param isUpdate Flag indicating whether the operation is an update.
     * @return The prepared WASHttpRequest object.
     */
    public WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> prepareRequestPayloadForCreateUpdate(
            SchedulingSvcRequest eventSchedule, Boolean isUpdate, String realmId) {
        WorkflowLogger.logInfo("Making a call to the scheduling service with request=%s", ObjectConverter.toJson(eventSchedule));
        return WASHttpRequest.<SchedulingSvcRequest, SchedulingSvcResponse>builder()
                .url(schedulingSvcConfig.getBaseUrl())
                .httpMethod(isUpdate ? HttpMethod.PUT : HttpMethod.POST)
                .requestHeaders(prepareHttpHeaders(realmId))
                .request(eventSchedule)
                .responseType(new ParameterizedTypeReference<SchedulingSvcResponse>() {})
                .build();
    }

    /**
     * Prepares the request payload for retrieving schedules.
     *
     * @param id The schedule ID to be retrieved.
     * @return The prepared WASHttpRequest object.
     */
    public WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> prepareRequestPayloadForGet(
            String id, String realmId) {
        String url = schedulingSvcConfig.getBaseUrl() + String.format("/%s", id);
        return WASHttpRequest.<SchedulingSvcRequest, SchedulingSvcResponse>builder()
                .url(url)
                .httpMethod(HttpMethod.GET)
                .requestHeaders(prepareHttpHeaders(realmId))
                .responseType(new ParameterizedTypeReference<SchedulingSvcResponse>() {})
                .build();
    }

    /**
     * Prepares the request payload for deleting schedules.
     *
     * @param id The schedule ID to be deleted.
     * @return The prepared WASHttpRequest object.
     */
    public WASHttpRequest<SchedulingSvcRequest, SchedulingSvcResponse> prepareRequestPayloadForDelete(
            String id, String realmId) {
        String url = schedulingSvcConfig.getBaseUrl() + String.format("/%s", id);
        return WASHttpRequest.<SchedulingSvcRequest, SchedulingSvcResponse>builder()
                .url(url)
                .httpMethod(HttpMethod.DELETE)
                .requestHeaders(prepareHttpHeaders(realmId))
                .responseType(new ParameterizedTypeReference<SchedulingSvcResponse>() {})
                .build();
    }

    /**
     * Prepares the HTTP headers for the request for online user.
     *
     * @return The prepared HttpHeaders object.
     */
    public HttpHeaders prepareHttpHeaders(String realmId) {
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.setContentType(MediaType.APPLICATION_JSON);
        requestHeaders.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
        requestHeaders.add(INTUIT_TID,wasContextHandler.get(WASContextEnums.INTUIT_TID));
        // set authHeader
        requestHeaders.set(
                HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(realmId));
        requestHeaders.set(INTUIT_OFFERING_HEADER, WorkflowConstants.WORKFLOW);
        return requestHeaders;
    }

    /**
     * Verifies the response from the scheduling service and throws an exception if the response is not successful.
     *
     * @param response The response from the scheduling service.
     * @throws SchedulingSvcException if the response is not successful or the response body is null.
     */
     void verifyAndThrowException(WASHttpResponse<SchedulingSvcResponse> response) {
        WorkflowVerfiy.verify(
                !response.isSuccess2xx() || response.getResponse() == null,
                () -> {
                    SchedulingError schedulingError = SchedulingServiceUtil.getSchedulingError(response.getError());
                    if (HttpStatus.BAD_REQUEST == response.getStatus()){
                        throw new SchedulingSvcException(SCHEDULING_SERVICE_BAD_REQUEST_FAILURE,
                                String.format(SCHEDULING_SERVICE_BAD_REQUEST_FAILURE.getErrorCode(), ObjectUtils.isNotEmpty(schedulingError) ? schedulingError.getErrorCode() : WorkflowError.UNKNOWN_ERROR.getErrorCode()),
                                String.format(SCHEDULING_SERVICE_BAD_REQUEST_FAILURE.getErrorMessage(), ObjectUtils.isNotEmpty(schedulingError) ? schedulingError.getMessage() : WorkflowError.UNKNOWN_ERROR.getErrorMessage()));
                    }
                    throw new SchedulingSvcException(SCHEDULING_SERVICE_CALL_FAILURE,
                            ObjectUtils.isNotEmpty(schedulingError) ? schedulingError.getErrorCode() : WorkflowError.UNKNOWN_ERROR.getErrorCode(),
                            ObjectUtils.isNotEmpty(schedulingError) ? schedulingError.getMessage() : WorkflowError.UNKNOWN_ERROR.getErrorMessage());
                }
        );
    }
}
