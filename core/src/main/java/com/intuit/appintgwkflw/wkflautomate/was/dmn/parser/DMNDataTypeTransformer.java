package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;

/** <AUTHOR> */
public interface DMNDataTypeTransformer {

  /**
   * transform input rule to DMN friendly rule.
   *
   * @param userFriendlyExpr input expression
   * @param parameterName parameter name in DMN
   * @return DMN friendly rule
   */
  String transformToDmnFriendlyExpression(String userFriendlyExpr, String parameterName,
      String parameterType, boolean useFeelExpr);

  /**
   * transform DMN rule to UI friendly rule.
   *
   * @param dmnFriendlyExpr input rule saved in DMN
   * @return UI friendly rule
   */
  String transformToUserFriendlyExpression(String dmnFriendlyExpr, String parameterName);

  /**
   * @param defaultValue default value from DMN
   * @return UI friendly default value
   */
  String defaultRule(String parameterName, String defaultValue);

  /** @return original data type */
  String getDataType();

  /** @return return DMN supported operator name */
  DMNSupportedOperator getName();
}