package com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn;

import com.google.gson.Gson;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collection;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Utility class for handling start elements in BPMN (Business Process Model and Notation).
 * This class provides various utility methods specifically for start elements in BPMN workflows.
 *
 * <p>Note: This class should be used only for start elements in BPMN.</p>
 *
 * <p>Example usage:</p>
 * <pre>
 * {@code
 * StartEvent startEvent = ...;
 * BpmnStartElementUtil.someUtilityMethod(startEvent);
 * }
 * </pre>
 *
 */
@UtilityClass
public class BpmnStartElementUtil {

    /**
     * Fetches the initial start event `ActivityDetail` from a list of start event activity details.
     *
     * <p>This method iterates over the provided list of `ActivityDetail` objects and attempts to find
     * the first one that contains startable event details within its extension properties. The extension
     * properties are expected to be stored in JSON format within the `attributes` field of the `ActivityDetail`.
     *
     *
     * @param startEventsActivityDetails a list of `ActivityDetail` objects representing start event activity details
     * @return the first `ActivityDetail` containing startable event details, or a new empty `ActivityDetail` if none are found
     */
    public ActivityDetail fetchInitialStartEventActivityDetail(List<ActivityDetail> startEventsActivityDetails) {
        return startEventsActivityDetails.stream()
                .filter(activityDetail -> {
                    Map<String, String> extensionProperties = ObjectConverter.fromJson(
                            activityDetail.getAttributes(),
                            new TypeReference<Map<String, Object>>() {
                            }
                    );
                    if (MapUtils.isNotEmpty(extensionProperties)) {
                        Map<String, Object> startEventExtensionPropertiesMap = ObjectConverter.fromJson(
                                ObjectConverter.toJson(extensionProperties.get("modelAttributes")),
                                new TypeReference<Map<String, Object>>() {
                                }
                        );
                        if (MapUtils.isNotEmpty(startEventExtensionPropertiesMap)) {
                            Object startableEventsDetailsData = startEventExtensionPropertiesMap
                                    .getOrDefault(WorkFlowVariables.PROCESS_STARTABLE_EVENTS_KEY.getName(), null);
                            return ObjectUtils.isNotEmpty(startableEventsDetailsData);
                        }
                    }
                    return false;
                })
                .findFirst().orElseGet(() -> {
                            WorkflowLogger.info(() ->
                                    WorkflowLoggerRequest.builder()
                                            .message("No startable events found in the extension properties " +
                                                    "for the start event activity details: " + new Gson().toJson(startEventsActivityDetails))
                            );
                            return new ActivityDetail();
                        }
                );
    }


    /**
     * Fetches the initial start event for a process from a collection of start events.
     * This method uses the startableEvents as an identifier to find the initial start event.
     * It iterates through the provided start events and checks their extension elements for
     * properties that contain the key defined by PROCESS_STARTABLE_EVENTS_KEY. If such a property
     * is found, and it is not empty, that start event is considered the desired initial start event.
     *
     * @param startEvents a collection of StartEvent objects to search through
     * @return the initial StartEvent for the process
     */
    public StartEvent fetchInitialStartEventForAProcess(Collection<StartEvent> startEvents) {

        StartEvent desiredInitialStartEvent = null;
        for (StartEvent startEvent : startEvents) {
            Map<String, String> properties = BpmnProcessorUtil
                    .getMapOfCamundaProperties(startEvent.getExtensionElements());
            if (MapUtils.isNotEmpty(properties)) {
                String startableEventsDetailsData = properties
                        .getOrDefault(WorkFlowVariables.PROCESS_STARTABLE_EVENTS_KEY.getName(), null);
                if (StringUtils.isNotEmpty(startableEventsDetailsData)) {
                    desiredInitialStartEvent = startEvent;
                    break;
                }
            }
        }

        return desiredInitialStartEvent;
    }

    public List<String> getStartableEventTypes(Map<String, Object> initialStartEventExtensionProperties) {
        Object startableEventsDetailsData = initialStartEventExtensionProperties
                .getOrDefault(WorkFlowVariables.PROCESS_STARTABLE_EVENTS_KEY.getName(), null);

        List<String> startableEventTypes = Optional.ofNullable(startableEventsDetailsData)
                .filter(startableEvents -> startableEvents instanceof List || startableEvents instanceof String)
                .map(startableEvents -> startableEvents instanceof List ? (List<String>) startableEvents :
                        ObjectConverter.fromJson((String) startableEvents, new TypeReference<List<String>>() {}))
                .orElseGet(ArrayList::new);

        WorkflowVerfiy
                .verify(ObjectUtils.isEmpty(startableEventTypes), WorkflowError.TRIGGER_STARTABLE_EVENTS_DETAILS_NOT_FOUND);
        return startableEventTypes;
    }

}
