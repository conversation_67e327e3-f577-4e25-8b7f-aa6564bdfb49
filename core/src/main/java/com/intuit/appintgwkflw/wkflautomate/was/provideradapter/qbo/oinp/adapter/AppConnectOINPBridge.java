package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.adapter;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.BRIDGE_INPUTS_MAP;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.CONSOLIDATE_NOTIFICATIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.IS_EMAIL;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.IS_MOBILE;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.NOTIFICATION_TASK_STATUS;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.SEND_ATTACHMENT;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.SEND_NOTIFICATION_API_HANDLER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.SEND_NOTIFICATION_HANDLER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.TEMPLATE_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.WORKER_ACTION_REQUEST;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowBatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPNotificationConfigMap;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.config.OINPSendNotificationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks.CustomHandlingTask;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks.OINPEventMappingTask;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks.OINPNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks.ReplacePlaceholdersTask;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> skumar103
 * 1. evaluates if the bridge has to be invoked for a workflow
 * 2. Stitches OINP notification with workflow
 */
@Component
@AllArgsConstructor
public class AppConnectOINPBridge {

  private static final String STEP_LOGGER = "Sending notification using OINP Bridge step=%s notificationType=%s externalTaskId=%s";

  private WorkflowNotificationTask workflowNotificationTask;
  private WorkflowBatchNotificationTask workflowBatchNotificationTask;
  private IdentityService identityService;
  private OINPNotificationConfigMap oinpNotificationConfigMap;
  private OINPSendNotificationConfig oinpSendNotificationConfig;

  /**
   * evaluates if OINP bridge should be invoked
   * @return boolean
   */
  public boolean initiateBridge(
      WorkerActionRequest workerActionRequest, Map<String, String> bridgeInputMap) {
    Long ownerId = workerActionRequest.getOwnerId();
    String workflowName = workerActionRequest.getInputVariables().get(TEMPLATE_NAME);
    String notificationType = getNotificationType(bridgeInputMap);
    return evaluateHardcodedConditions(workerActionRequest)
        && Objects.nonNull(workflowName)
        && Objects.nonNull(notificationType)
        && oinpSendNotificationConfig.isNotificationEnabled(workflowName, notificationType, ownerId);
  }

  /**
   * evaluate hardcoded conditions, like handlerId
   */
  private boolean evaluateHardcodedConditions(WorkerActionRequest workerActionRequest){
    return SEND_NOTIFICATION_HANDLER_ID.equals(workerActionRequest.getHandlerId()) ||
        SEND_NOTIFICATION_API_HANDLER_ID.equals(workerActionRequest.getHandlerId());
  }

  /**
   * evaluate notificationType, email/mobile
   */
  private String getNotificationType(Map<String, String> bridgeInputMap){
    if (Boolean.parseBoolean(bridgeInputMap.get(IS_MOBILE))) {
      return IS_MOBILE;
    } else if (Boolean.parseBoolean(bridgeInputMap.get(SEND_ATTACHMENT))){
      return SEND_ATTACHMENT;
    } else if (Boolean.parseBoolean(bridgeInputMap.get(CONSOLIDATE_NOTIFICATIONS))) {
      return CONSOLIDATE_NOTIFICATIONS;
    } else if (Boolean.parseBoolean(bridgeInputMap.get(IS_EMAIL))) {
      return IS_EMAIL;
    }
    return null;
  }

  /**
   * Executes notification action for email/mobile
   * @param workerActionRequest input request
   * @param inputsMap input key/value map to be processed
   * @return response
   */
  public Map<String, Object> executeNotificationAction(
      WorkerActionRequest workerActionRequest, Map<String, String> inputsMap) {

    String notificationType = getNotificationType(inputsMap);
    WorkflowLogger.logInfo(STEP_LOGGER, "oinpBridgeStarted", notificationType, workerActionRequest.getTaskId());
    State state = populateStateVariables(workerActionRequest, inputsMap);

    try {
      state = new RxExecutionChain(state)
          .next(new ReplacePlaceholdersTask())
          .next(new CustomHandlingTask(identityService))
          .next(new OINPEventMappingTask(oinpNotificationConfigMap))
          .next(new OINPNotificationTask(workflowBatchNotificationTask, workflowNotificationTask))
          .execute();

      return prepareResponse(workerActionRequest, notificationType, state);
    }
    catch (Exception e){
      WorkflowLogger.logInfo(STEP_LOGGER, "oinpBridgeFailed", notificationType, workerActionRequest.getTaskId());
      throw e;
    }
  }

  /**
   * Prepare response to be updated in camunda task complete call
   * @param workerActionRequest input request
   * @param state contains variable map
   * @param notificationType email/mobile/attachment/consolidation
   * @return (k,V) response
   */
  private Map<String, Object> prepareResponse(WorkerActionRequest workerActionRequest, String notificationType, State state){

    String status = state.getValue(NOTIFICATION_TASK_STATUS);

    if (ActivityConstants.NO_ACTION.equals(status)){
      WorkflowLogger.logInfo(STEP_LOGGER, "oinpBridgeNoAction", notificationType, workerActionRequest.getTaskId());
    }
    else{
      WorkflowLogger.logInfo(STEP_LOGGER, "oinpBridgeCompleted", notificationType, workerActionRequest.getTaskId());
    }
    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put(workerActionRequest.getActivityId().concat(UNDERSCORE).concat(RESPONSE.getName()),
        Boolean.TRUE);
    return responseMap;
  }

  /**
   * populate required state variables
   */
  private State populateStateVariables(WorkerActionRequest workerActionRequest,
      Map<String, String> bridgeInputMap){

    var state = new State();
    state.addValue(WORKER_ACTION_REQUEST, workerActionRequest);
    state.addValue(BRIDGE_INPUTS_MAP, bridgeInputMap);
    state.addValue(IS_MOBILE,
        Boolean.parseBoolean(bridgeInputMap.get(IS_MOBILE)));
    state.addValue(IS_EMAIL,
        Boolean.parseBoolean(bridgeInputMap.get(IS_EMAIL)));
    state.addValue(CONSOLIDATE_NOTIFICATIONS,
        Boolean.parseBoolean(bridgeInputMap.get(CONSOLIDATE_NOTIFICATIONS)));
    state.addValue(TEMPLATE_NAME,
        Optional.ofNullable(workerActionRequest.getInputVariables().get(TEMPLATE_NAME)).orElse(""));

    return state;
  }
}
