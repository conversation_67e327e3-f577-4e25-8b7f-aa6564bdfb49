package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.DuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.RecordDuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.RecordListFetcher;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR> @yagarwal1

 * This class is responsible of fetching all records of all types of customReminder workflow
 * recordTypes(Invoice, Bill, Estimate etc) from the external service(e.g Appconnect) and publishes
 * to the trigger topic.
 */
@Component
@AllArgsConstructor
public class CustomReminderScheduleActionProcessor implements WorkflowScheduleActionProcessor {

  private final RecordListFetcher recordListFetcher;

  private final WASContextHandler wasContextHandler;

  private final TriggerEventPublisher triggerEventPublisher;

  private final EventScheduleHelper eventScheduleHelper;
  
  private final IXPManager ixpManager;
  
  private static final String REMINDER_250_NEW_DUZZIT_FF = "qbo-adv-reminder-250-limit";
  
  private final DuzzitPaginationConfig paginationConfig;

  private static final String INPUT_PARAMETER_REQUEST_FLOW = "requestFlow";

  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  /**
   * This method returns the custom reminder workflow name
   *
   * @return
   */
  @Override
  public WorkflowNameEnum getWorkflowName() {
    return WorkflowNameEnum.CUSTOM_REMINDER;
  }

  /**
   * This method fetches the records from the connector and publishes to the trigger topic.
   *
   * @param schedulerDetails
   * @param eventScheduleMessageData
 * @return
   */
  @Override
  public Map<String, String> process(
      final SchedulerDetails schedulerDetails,
      final EventScheduleMessageData eventScheduleMessageData) {
    if (!eventScheduleHelper.isEventSchedulingEnabledForWorkflow(
        schedulerDetails.getDefinitionDetails().getTemplateDetails().getTemplateName(),
        String.valueOf(schedulerDetails.getOwnerId()))) {
      EventingLoggerUtil.logInfo(
          "Processing CustomReminder Action. step=skip_schedule_event, definitionId=%s, action=%s",
          this.getClass().getSimpleName(),
          schedulerDetails.getDefinitionDetails().getDefinitionId(),
          schedulerDetails.getSchedulerAction());
      return Collections.emptyMap();
    }
    // add all the keys to the context for calling downstream services
    addKeysToContextHandler(schedulerDetails, eventScheduleMessageData);

    // get connectorId for the given schedulerAction and recordType
    String connectorId = getConnectorId(
            schedulerDetails.getSchedulerAction(),
            schedulerDetails.getDefinitionDetails().getRecordType());

    // process differently for multiCondition workflows
    if (MultiStepUtil.isMultiConditionWorkflow(schedulerDetails.getDefinitionDetails())) {
      return processForMultiConditionWorkflows(schedulerDetails, eventScheduleMessageData);
    }

    // resume the flow for single step custom workflows
    Map<String, String> requestParameters = getRequestParameters(schedulerDetails, null);
    EventingLoggerUtil.logInfo(
        "Processing CustomReminder Action. step=start_record_fetch, definitionId=%s, action=%s",
        this.getClass().getSimpleName(),
        schedulerDetails.getDefinitionDetails().getDefinitionId(),
        schedulerDetails.getSchedulerAction());

    return fetchRecordsAndPublishTriggerEvent(schedulerDetails, connectorId, requestParameters,
        eventScheduleMessageData, null);

  }

  /**
   * This method processes the multiCondition workflows
   *
   * @param schedulerDetails
   * @param eventScheduleMessageData
   * @return
   */
  @Metric(name = MetricName.FETCH_APPCONNECT_RECORDS_MULTI_STEP_DEFINITION, type = Type.APPLICATION_METRIC)
  private Map<String, String> processForMultiConditionWorkflows(
      SchedulerDetails schedulerDetails,
      EventScheduleMessageData eventScheduleMessageData) {

    // get all the callActivities for the given definitionId
    Optional<List<DefinitionActivityDetail>> optionalDefinitionActivityDetailList =
        definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(
            schedulerDetails.getDefinitionDetails().getDefinitionId());
    if (optionalDefinitionActivityDetailList.isEmpty()) {
      WorkflowLogger.logError("No call activities found for definitionId=%s action=%s",
          this.getClass().getSimpleName(),
          schedulerDetails.getDefinitionDetails().getDefinitionId(),
          schedulerDetails.getSchedulerAction());
      return Collections.emptyMap();
    }

    List<DefinitionActivityDetail> calledActivitiesList = optionalDefinitionActivityDetailList.get();
    Map<String,String> txnTriggeredDetails = new HashMap<>();

    // enrich conditions for all call activities and fetch the records from appConnect

    calledActivitiesList.stream()
        .filter(callActivity -> Objects.nonNull(callActivity.getUserAttributes()))
        .forEach(
            callActivity -> {

              // get the request parameters for the given activity
              Map<String, String> requestParameters = getRequestParameters(schedulerDetails,
                  callActivity.getUserAttributes());

              // fetch records from customStart and customWait duzzits
              Map<String,String> triggerDetails = fetchRecordsAndPublishTriggerEvent(
                  schedulerDetails,
                  getConnectorId(schedulerDetails.getSchedulerAction(),
                      schedulerDetails.getDefinitionDetails().getRecordType()),
                  requestParameters,
                  eventScheduleMessageData,
                  callActivity.getActivityId());

              txnTriggeredDetails.putAll(triggerDetails);

              // if the action is customReminder_customWait and recurrence is applicable, then
              // fetch records from CustomRecur duzzit
              if (SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT.equals(
                  schedulerDetails.getSchedulerAction()) &&
                  requestParameters.containsKey(WorkflowConstants.IS_RECURRING_ENABLED) &&
                  Boolean.TRUE.toString()
                      .equals(requestParameters.get(WorkflowConstants.IS_RECURRING_ENABLED))) {

                /**
                 * Recur action is associated with Wait and is instantiated On Demand.
                 * Making a copy of schedulerDetails for recur from Wait Action.
                 **/
                SchedulerDetails schedulerDetailsForRecurEvent = schedulerDetails.toBuilder()
                    .schedulerAction(SchedulerAction.CUSTOM_REMINDER_CUSTOM_RECUR)
                    .build();

                Map<String,String> recurrenceTriggerDetails = fetchRecordsAndPublishTriggerEvent(
                    schedulerDetailsForRecurEvent,
                    getConnectorId(schedulerDetailsForRecurEvent.getSchedulerAction(),
                        schedulerDetailsForRecurEvent.getDefinitionDetails().getRecordType()),
                    requestParameters,
                    eventScheduleMessageData,
                    callActivity.getActivityId());
                txnTriggeredDetails.putAll(recurrenceTriggerDetails);
              }
        });
    return txnTriggeredDetails;
  }

  /**
   * Method to fetch eligible records from AppConnect and publish trigger events to kafka
   * @param schedulerDetails
   * @param connectorId
   * @param requestParameters
 * @return
   * @return
   */
  private Map<String, String> fetchRecordsAndPublishTriggerEvent(
      SchedulerDetails schedulerDetails,
      String connectorId,
      Map<String, String> requestParameters,
      EventScheduleMessageData eventScheduleMessageData,
      String activityId) {

    StringBuilder idempotencyKey = new StringBuilder(eventScheduleMessageData.getMessageId());

    // Making one unique call to AC per event per activityId per scheduleAction in case of Multi-Condition
    if (StringUtils.isNotBlank(activityId)) {
      idempotencyKey.append(UNDERSCORE)
          .append(activityId)
          .append(UNDERSCORE)
          .append(schedulerDetails.getSchedulerAction());
    }

    wasContextHandler.addKey(WASContextEnums.IDEMPOTENCY_KEY, idempotencyKey.toString());

    RecordQueryConnectorResponse recordQueryConnectorResponse =
        recordListFetcher.fetchRecords(
            new RecordQueryConnectorRequest(connectorId, requestParameters));
    if (CollectionUtils.isEmpty(recordQueryConnectorResponse.getRecordList())) {
      EventingLoggerUtil.logInfo(
          "Records not found for definitionId=%s, action=%s",
          this.getClass().getSimpleName(),
          schedulerDetails.getDefinitionDetails().getDefinitionId(),
          schedulerDetails.getSchedulerAction());
      return Collections.emptyMap();
    }
    EventingLoggerUtil.logInfo(
        "Processing CustomReminder Action. step=complete_record_fetch , definitionId=%s, action=%s, recordCount=%s",
        this.getClass().getSimpleName(),
        schedulerDetails.getDefinitionDetails().getDefinitionId(),
        schedulerDetails.getSchedulerAction(),
        recordQueryConnectorResponse.getRecordList().size());

    Map<String,String> responseMap = new HashMap<>();

    recordQueryConnectorResponse
        .getRecordList()
        .forEach(
            record -> {
              addTriggerKeyToContextHandler(eventScheduleMessageData, record.get(ID_KEY), activityId);
              triggerEventPublisher.publishTriggerEvent(
                  prepareTriggerEvent(schedulerDetails, record, eventScheduleMessageData.getScope()));
              responseMap.put(record.get(ID_KEY), schedulerDetails.getSchedulerAction().getEntityChangeType());
            });

    return responseMap;
  }

  /**
   * This method adds all keys to the context that the downstream services require.
   *
   * @param schedulerDetails
   * @param eventScheduleMessageData
   */
  private void addKeysToContextHandler(
      SchedulerDetails schedulerDetails, EventScheduleMessageData eventScheduleMessageData) {
    wasContextHandler.addKey(
        WASContextEnums.OWNER_ID, String.valueOf(schedulerDetails.getOwnerId()));
    wasContextHandler.addKey(WASContextEnums.INTUIT_TID, eventScheduleMessageData.getMessageId());
    // any other suggestions from where we can find offering id??
    wasContextHandler.addKey(
        WASContextEnums.OFFERING_ID,
        schedulerDetails.getDefinitionDetails().getTemplateDetails().getOfferingId());
    /**
     * setting intuit_userid using LastModifiedByUserId from definition. this will be used for
     * populating intuit_userid in process variables. Previously the value is taken from the
     * authorization but new processes will be triggered using events and authorization will not
     * have the required values
     */
    wasContextHandler.addKey(
        WASContextEnums.INTUIT_USERID,
        String.valueOf(schedulerDetails.getDefinitionDetails().getModifiedByUserId()));
  }

  /**
   * This method add all the keys required for publishing an event to trigger topic
   *
   * @param eventScheduleMessageData
   * @param recordId
   */
  private void addTriggerKeyToContextHandler(
      EventScheduleMessageData eventScheduleMessageData, String recordId, String activityId) {
    String entityId =
        getEntityId(
            eventScheduleMessageData.getMessageId(),
            eventScheduleMessageData.getScheduleId(),
            recordId);
    wasContextHandler.addKey(WASContextEnums.ENTITY_ID, entityId);

    // Making one unique trigger call per entity per activityId in case of Multi-Condition
    String idempotencyKey =
        StringUtils.isEmpty(activityId) ? entityId : entityId + UNDERSCORE + activityId;
    wasContextHandler.addKey(WASContextEnums.IDEMPOTENCY_KEY, idempotencyKey);
  }

  /**
   * This method returns the connectorId for the given scheduleActions and recordType
   *
   * @param schedulerAction
   * @param recordType
   * @return
   */
  private String getConnectorId(SchedulerAction schedulerAction, RecordType recordType) {
    // ##TODO read from config
    return schedulerAction.getConnectorId(recordType);
  }

  /**
   * This method prepares all the request parameters required for fetching the records.
   *
   * @param schedulerDetails
   * @return
   */
    private Map<String, String> getRequestParameters(SchedulerDetails schedulerDetails, String callActivityAttributes) {
	  DefinitionDetails definitionDetails = schedulerDetails.getDefinitionDetails();

    Map<String, ParameterDetails> parameterDetailsMap =
        MultiStepUtil.isMultiConditionWorkflow(schedulerDetails.getDefinitionDetails()) ?
            getParameterDetailsMapForMultiConditionWorkflows(callActivityAttributes) :
            getParameterDetailsMap(definitionDetails);

    Map<String, String> inputs = new HashMap<>();
    addInputParameter(WorkflowConstants.FILTER_RECORD_TYPE, inputs, parameterDetailsMap);
    addInputParameter(WorkflowConstants.FILTER_CONDITION, inputs, parameterDetailsMap);
    addInputParameter(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS, inputs, parameterDetailsMap);
    // To stop appconnect scheduler, we are passing headers source so that only ess call serve by duzzits.
    inputs.put(WorkflowConstants.DUZZIT_SOURCE_KEY, WorkflowConstants.DUZZIT_SOURCE_VALUE);
    inputs.put(WorkflowConstants.WORKFLOW_NAME, definitionDetails.getTemplateDetails().getTemplateName());

    inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_PAGE_SIZE,
    		String.valueOf(paginationConfig.getRecordsPerPage()));
    inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_MAXIMUM_RESULTS, 
    		String.valueOf(paginationConfig.getMaxResult()));
    inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_TOTAL_PAGES, 
    		String.valueOf(paginationConfig.getTotalPages()));
    /**
     * This helps to fetch invoices for companies which get filtered out due to PageLimit = 2, MaxRecords from QBO=250.
     * Override default config with recordBased config.   
     */
    if(isOverrideRecordBasedConfig(schedulerDetails, definitionDetails)){
    	RecordDuzzitPaginationConfig recordDuzzitPaginationConfig = paginationConfig.getRecordConfig()
				.get(definitionDetails.getRecordType());
		inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_PAGE_SIZE, 
        		String.valueOf(recordDuzzitPaginationConfig.getRecordsPerPage()));
        inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_MAXIMUM_RESULTS, 
        		String.valueOf(recordDuzzitPaginationConfig.getMaxResult()));
    	inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_TOTAL_PAGES, 
        		String.valueOf(recordDuzzitPaginationConfig.getTotalPages()));
    }
    /**
     * IXP Based input parameter for pagination flow.
     */
    if(ixpManager.getBoolean(REMINDER_250_NEW_DUZZIT_FF,  
    		String.valueOf(schedulerDetails.getOwnerId()))){
    	inputs.put(INPUT_PARAMETER_REQUEST_FLOW, REMINDER_250_NEW_DUZZIT_FF);
    }

    // input fields for custom-recur duzzit
    addInputParameter(WorkflowConstants.IS_RECURRING_ENABLED, inputs, parameterDetailsMap);
    addInputParameter(WorkflowConstants.RECUR_FREQUENCY, inputs, parameterDetailsMap);
    addInputParameter(WorkflowConstants.MAX_SCHEDULE_COUNT, inputs, parameterDetailsMap);
    return inputs;
  }
  
  /**
   * Checks if Record Based Config Provided and IXP is enabled.
   * @param schedulerDetails
   * @param definitionDetails
   * @return
   */
  private boolean isOverrideRecordBasedConfig(SchedulerDetails schedulerDetails, DefinitionDetails definitionDetails) {
	return MapUtils.isNotEmpty(paginationConfig.getRecordConfig()) &&
    		paginationConfig.getRecordConfig()
    		.containsKey(definitionDetails.getRecordType());
  }

  /**
   * This method retrieves all parameters details defined as startEventElement properties in BPMN.
   * These parameter details contain the conditions that the user specified when creating a
   * definition.
   *
   * @param definitionDetails
   * @return
   */
  private Map<String, ParameterDetails> getParameterDetailsMap(
      DefinitionDetails definitionDetails) {
    // get the start element from the definition BPMN.
    // #TODO will start reading from placeholder value once all the customReminder filter values
    // added to placeholder values
    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(
            BpmnProcessorUtil.readBPMN(definitionDetails.getDefinitionData()));
    Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();
    // This map contains all the parameterDetails parameter key value in the extension variables for
    // startElement of BPMN.
    Optional<CamundaProperty> propertyMap =
        BpmnProcessorUtil.getCamundaProperty(
            startEventElement, WorkFlowVariables.PARAMETERS_KEY.getName());
    if (propertyMap.isPresent()) {
      parameterDetailsMap.putAll(
          ObjectConverter.fromJson(
              propertyMap.get().getCamundaValue(),
              new TypeReference<Map<String, ParameterDetails>>() {}));
    }
    return parameterDetailsMap;
  }


  /**
   * This method extracts all parameters details from the userAttributes field of the callActivity
   * @param userAttributes String
   * @return
   */
  private Map<String, ParameterDetails> getParameterDetailsMapForMultiConditionWorkflows(
      String userAttributes) {

    Map<String, ParameterDetails> parameterDetailsMap = new HashMap<>();

    Map<String, Object> userAttributesMap = ObjectConverter.fromJson(userAttributes,
        new TypeReference<Map<String, Object>>() {});

    if (MapUtils.isEmpty(userAttributesMap) || !userAttributesMap.containsKey(WorkflowConstants.PARAMETERS)) {
      return parameterDetailsMap;
    }

    Object parametersMap = userAttributesMap.get(WorkflowConstants.PARAMETERS);

    parameterDetailsMap = ObjectConverter.fromJson(ObjectConverter.toJson(parametersMap),
        new TypeReference<Map<String, ParameterDetails>>() {});

    return parameterDetailsMap;
  }


  /**
   * Adds input parameter to MultiValueMap
   *
   * @param inputParameterKey
   * @param inputs
   * @param parameterDetailsMap
   */
  private void addInputParameter(
      String inputParameterKey,
      Map<String, String> inputs,
      Map<String, ParameterDetails> parameterDetailsMap) {
    Optional.ofNullable(parameterDetailsMap.get(inputParameterKey))
        .ifPresent(
            parameterDetails -> {
              if (CollectionUtils.isNotEmpty(parameterDetails.getFieldValue())) {
                inputs.put(inputParameterKey, parameterDetails.getFieldValue().get(0));
              }
            });
  }

  /**
   * This method returns the entityId as a combination of messageId, schedulerId, recordId
   *
   * @param messageId
   * @param schedulerId
   * @param recordId
   * @return
   */
  private String getEntityId(String messageId, String schedulerId, String recordId) {
    // this would be helpful in debugging the recordId for a messageId of schedulerId when trigger
    // event consumed by trigger
    // consumer.
    return new StringBuilder()
        .append(messageId)
        .append(WorkflowConstants.COLON)
        .append(schedulerId)
        .append(WorkflowConstants.COLON)
        .append(recordId)
        .toString();
  }

  /**
   * This method prepares the workflowTrigger Event
   *
   * @param schedulerDetails
   * @param data
   * @return {@link Trigger}
   */
  private Trigger prepareTriggerEvent(SchedulerDetails schedulerDetails, Map<String, String> data, String scope) {
    DefinitionDetails definitionDetails = schedulerDetails.getDefinitionDetails();
    MetaData metaData =
        MetaData.builder()
            .workflow(CustomWorkflowType.getActionKey(schedulerDetails.getDefinitionDetails().getTemplateDetails().getTemplateName()))
            .entityType(definitionDetails.getRecordType().getRecordType())
            .entityChangeIdentifier(
                new EntityChangeIdentifier(
                    schedulerDetails.getSchedulerAction().getEntityChangeType()))
            .entityId(data.get(ID_KEY))
            .providerWorkflowId(definitionDetails.getWorkflowId())
            .blockProcessOnSignalFailure(false)
            .targetApi(schedulerDetails.getSchedulerAction().getTriggerTargetAPI())
            .definitionKey(schedulerDetails.getDefinitionDetails().getDefinitionKey())
            .scope(scope)
            .build();
    return new Trigger(
        metaData, null, Map.of(definitionDetails.getRecordType().getRecordType(), data));
  }
}
