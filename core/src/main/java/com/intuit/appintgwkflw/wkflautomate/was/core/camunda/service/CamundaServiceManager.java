package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@AllArgsConstructor
public class CamundaServiceManager {

  private BPMNEngineHistoryServiceRest camundaHistoryRest;

  private WorkflowTaskConfig workflowTaskConfig;

  /**
   * Get the variables for the external Task from camunda history
   * @param externalTaskId externalTaskId
   * @param processInstanceId processInstanceId
   * @return map of variables
   */
  public Map<String, Object> getExternalTaskVariables(String processInstanceId, String externalTaskId) {
    WASHttpResponse<List<ExternalTaskLog>> wasResponse = camundaHistoryRest
        .getExternalTaskLogs(ExternalTaskLogRequest.builder()
            .maxResults(1).externalTaskId(externalTaskId).processInstanceId(processInstanceId).build());
    if (CollectionUtils.isEmpty(wasResponse.getResponse())){
      return Collections.emptyMap();
    }
    String executionId = wasResponse.getResponse().get(0).getExecutionId();
    ProcessVariableDetailsRequest processVariableDetailsRequest =
        ProcessVariableDetailsRequest.builder().executionIdIn(new String[]{executionId})
            .processInstanceId(processInstanceId)
            .maxResults(workflowTaskConfig.getStateTransitionConfig().getMaxResult())
            .deserializeValues(true).build();

    WASHttpResponse<List<ProcessVariableDetailsResponse>> response =
        camundaHistoryRest.getProcessVariableDetails(processVariableDetailsRequest);
    /*
    The intention of the API is to fetch the local variables for the external task.
    Ideally, Camunda should return unique variable names in the response,
    as we cannot have duplicate variables for a task.
    But in overwatch runs, it is returning duplicate variables sometimes, leading to intermittent automation failure.
    Hence, we have added check for duplicate (value1, value2) -> value1
     */
    return Optional.ofNullable(response.getResponse())
        .map(list -> list.stream()
            .collect(Collectors
                .toMap(ProcessVariableDetailsResponse::getName,
                    ProcessVariableDetailsResponse::getValue,
                    (value1, value2) -> {
                      WorkflowLogger.logWarn("Received duplicate key in task variables value1=%s value2=%s apiResponse=%s",
                          value1, value2, response.getResponse());
                      return value1;
                    })))
        .orElse(Collections.emptyMap());
  }
}
