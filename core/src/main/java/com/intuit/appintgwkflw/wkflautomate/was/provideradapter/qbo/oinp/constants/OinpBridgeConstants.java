package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * OINP AppConnect Bridge constants
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OinpBridgeConstants {

  public static final String SEND_NOTIFICATION_HANDLER_ID = "intuit-workflows/was-send-notification";
  public static final String SEND_NOTIFICATION_API_HANDLER_ID = "/intuit-workflows/api/was-send-notification.json";
  public static final String TEMPLATE_NAME = "templateName";
  public static final String WORKER_ACTION_REQUEST = "workerActionRequest";
  public static final String BRIDGE_INPUTS_MAP = "inputsMap";
  public static final String BRIDGE_OUTPUT_MAP = "outputMap";
  public static final String IS_MOBILE = "IsMobile";
  public static final String IS_EMAIL = "IsEmail";
  public static final String SEND_ATTACHMENT = "SendAttachment";
  public static final String CONSOLIDATE_NOTIFICATIONS = "consolidateNotifications";
  public static final String TO_FIELD = "To";
  public static final String TO_FIELD_DELIMITER = ",";
  public static final String OINP_SERVICE_NAME = "Workflow";
  public static final String IDEMPOTENCY_KEY_CONCAT = "-";
  public static final String NOTIFICATION_TASK_STATUS = "notificationTaskStatus";
  public static final String NOTIFICATION_NAME = "notificationName";
  public static final String NOTIFICATION_DATA_TYPE = "notificationDataType";
  public static final String NOTIFICATION_DATA = "notificationData";
  public static final String NOTIFICATION_METADATA = "notificationMetaData";
  public static final String NOTIFICATION_TYPE = "NotificationType";
  public static final String AUTH_IDS = "authIds";
  public static final String DEFAULT_AUTH_ID = "-1";
  public static final String EMAIL_AT = "@";
  public static final String IUS_AUTHID_PERSONA_MAP = "iusAuthIdPersonaMap";
  public static final String SERVICE_NAME = "serviceName";

}
