package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ActivityDetailsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 *     <p>Base class for Trigger handler.Contains logic common across triggers.
 */
public abstract class BaseTriggerHandler {

  @Autowired
  protected TemplateService templateService;

  @Autowired
  protected V3RunTimeHelper runtimeHelper;


  /**
   * @param transactionEntity input transaction entity details
   * @return {@link StartEvent} details
   * @throws TEMPLATE_DOES_NOT_EXIST,TEMPLATE_READ_EXCEPTION,PROCESSING_ERROR
   */
  public Collection<StartEvent> getStartEventOfTheTemplate(BpmnModelInstance bpmnModelInstance) {
    // todo: bugfix for same templateId for bpmn and dmn in the defn table

      WorkflowVerfiy.verifyNull(bpmnModelInstance, WorkflowError.TEMPLATE_READ_EXCEPTION);
      Collection<StartEvent> startEvents =
          bpmnModelInstance.getModelElementsByType(StartEvent.class);
      WorkflowVerfiy.verify(CollectionUtils.isEmpty(startEvents), WorkflowError.PROCESSING_ERROR);
      return startEvents;
  }

  /**
   * Check if businessKey is present in {@link com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders}
   * @param transactionEntity contains eventHeader info
   * BusinessKey allowed only for {@link SystemDefinitionTriggerHandler}
   */
  protected void verifyBusinessKeyNotUsed(TransactionEntity transactionEntity){
    WorkflowVerfiy.verify(
        StringUtils.isNotBlank(transactionEntity.getEventHeaders().getBusinessKey()),
        WorkflowError.BUSINESS_KEY_NOT_ALLOWED);
  }

  /**
   * Method to verify the entity type from request matches the one in the database.
   * @param transactionEntity
   * @param enabledDefinitionList
   */
  protected List<DefinitionDetails> pruneIneligibleDefinitions(TransactionEntity transactionEntity, List<DefinitionDetails> enabledDefinitionList) {
    if (!CollectionUtils.isEmpty(enabledDefinitionList)) {
      return enabledDefinitionList.stream().filter(definitionDetail -> definitionDetail.getRecordType().equals(
              transactionEntity.getEventHeaders().getEntityType())).collect(Collectors.toList());
    }
    return Collections.emptyList();
  }

  protected List<DefinitionDetails> filterDefsOnChangeType(ActivityDetail startEventActivityDetail, List<DefinitionDetails> definitionDetailsList, TransactionEntity transactionEntity) {
    if (!CollectionUtils.isEmpty(definitionDetailsList)) {
      return definitionDetailsList.stream().filter(definitionDetails -> {
                List<String> entityOperations = SingleDefinitionUtil.getEntityOperations(startEventActivityDetail,
                        definitionDetails.getPlaceholderValue());
                return entityOperations.isEmpty() || entityOperations.toString().contains(transactionEntity.getEventHeaders().getEntityChangeType());
              }
      ).collect(Collectors.toList());
    }
    return definitionDetailsList;
  }

  protected BpmnModelInstance getBpmnModelInstance(TransactionEntity transactionEntity) {
    byte[] templateData = runtimeHelper.getTemplateData(transactionEntity);
    WorkflowVerfiy.verifyNull(templateData, WorkflowError.TEMPLATE_DOES_NOT_EXIST);
    return  BpmnProcessorUtil.readBPMN(templateData);
  }

  protected Map<String, Object> getInitialStartEventExtensionPropertiesFromActivityDetail(TransactionEntity transactionEntity) {
    return Optional.ofNullable(runtimeHelper.fetchInitialStartEventActivityDetail(transactionEntity))
            .filter(event -> event.getActivityId() != null && !event.getActivityId().isEmpty())
            .map(ActivityDetailsUtil::getExtensionProperties)
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.INVALID_INPUT, "Initial start event/extension properties cannot be null or empty."));
  }

}
