package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.EventScheduleServiceMockImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/** <AUTHOR> This class used for mocking the ESS for perf env */
@Configuration
@ConditionalOnProperty(name = "app.env", havingValue = "prfa")
public class EventScheduleMockConfig {

  @Primary
  @Bean
  public EventScheduleService eventScheduleServiceMock() {
    return new EventScheduleServiceMockImpl();
  }
}
