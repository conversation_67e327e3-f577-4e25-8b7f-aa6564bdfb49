package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import java.util.Deque;
import java.util.LinkedList;
import java.util.Optional;

/**
 * <AUTHOR>
 * Basic implementation of count based sliding window.
 *
 * Caller is expected to record the failures/success manually
 * and calculate the next steps based on the total failure ratio in the configured window.
 *
 * Currently it is used to calculate backoff interval in ExternalTask worker client.
 */
public class CountSlidingWindow {

  private static final int POSITIVE_TOKEN = 0;
  private static final int NEGATIVE_TOKEN = 1;

  private final Deque<Integer> slidingWindowDeque = new LinkedList<>();
  private final int windowSize;
  private final int maxErrorScore;
  private int negatives = 0;

  /**
   *
   * @param windowSize total number of samples to record success/failures
   * @param maxErrorScore max score to calculate error rate in {@link #windowSize}
   * @return instance of {@link CountSlidingWindow}
   */
  public static CountSlidingWindow of(int windowSize, int maxErrorScore){
    return new CountSlidingWindow(windowSize, maxErrorScore);
  }

  private CountSlidingWindow(int windowSize, int maxErrorScore){
    WorkflowVerfiy.verify(windowSize == 0 || maxErrorScore == 0, WorkflowError.INVALID_INPUT);
    this.windowSize = windowSize;
    this.maxErrorScore = maxErrorScore;
    populateList();
  }

  /**
   * populate the Deque with all {@link #POSITIVE_TOKEN}
   */
  private void populateList() {
    for (int i = 0; i < windowSize; i++){
      slidingWindowDeque.add(POSITIVE_TOKEN);
    }
  }

  /**
   * Adds negative sample to the window
   */
  public void recordFailure(){
    addToken(NEGATIVE_TOKEN);
  }

  /**
   * Adds positive sample to the window
   */
  public void recordSuccess(){
    addToken(POSITIVE_TOKEN);
  }

  /**
   * Adds sample to the window
   */
  private void addToken(int token){
    negatives += token
        - Optional.ofNullable(slidingWindowDeque.pollLast()).orElse(0);
    slidingWindowDeque.addFirst(token);
  }

  /**
   * @return count of error samples
   */
  public int getFailureCount() {
    return negatives;
  }

  /**
   * @return the failure score based on total error samples and maxErrorScore
   */
  public int failureScore() {
    return  (int)(((double)(negatives)/(double)windowSize) * maxErrorScore);
  }
}
