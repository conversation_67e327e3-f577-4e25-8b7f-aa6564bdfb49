package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.ACTIVATE_ACTION_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.SUBSCRIPTION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.WORKFLOW_ID_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

/**
 * Task for disable appconnect workflow (used during trigger call) Checks if there is any definition
 * marked for delete/disable, and do the same in appconnect Then updates the status in DB, Set
 * Status as DISABLED in case of disable and set Internal status as Deleted
 * TODO One race condition where tasks are running and trigger call comes then the tasks will fail
 * TODO since we have disabled the workflow from appconnect at the time of trigger
 */
@AllArgsConstructor
public class DisableDeleteWorkflowTask implements Task {

  private DefinitionDetailsRepository definitionDetailsRepository;

  private AppConnectService appConnectService;

  private AuthDetailsService authDetailsService;

  private EventScheduleHelper eventScheduleHelper;

  private SchedulingService schedulingService;


  @Override
  public State execute(State state) {

    // Get definition details which are marked_for_delete or marked_for_disable
    final List<DefinitionDetails> definitionDetailsList = state.getValue(
        AsyncTaskConstants.DEFINITION_DETAILS);
    final String realmId = state.getValue(REALM_ID_KEY);
    if (definitionDetailsList.isEmpty()) {
      return state;
    }

    try {
      WorkflowVerfiy.verifyNull(realmId, WorkflowError.INVALID_INPUT, REALM_ID_KEY);
      // Get definitions which are to be deleted or to be disabled
      definitionDetailsList.stream()
          .filter(
              definitionDetails ->
                  Objects.equals(InternalStatus.MARKED_FOR_DISABLE,
                      definitionDetails.getInternalStatus()))
          .forEach(
              definitionDetails -> disableDefinitions(definitionDetails, realmId));

      definitionDetailsList.stream()
          .filter(
              definitionDetails ->
                  Objects.equals(
                      InternalStatus.MARKED_FOR_DELETE, definitionDetails.getInternalStatus()))
          .forEach(
              definitionDetails -> deleteDefinitions(definitionDetails, realmId));
    } catch (Exception exception) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .downstreamServiceName(DownstreamServiceName.DISABLE_DELETE_WORKFLOW)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(this.getClass().getSimpleName())
                  .stackTrace(exception));
      throw new WorkflowGeneralException(WorkflowError.DISABLE_DELETE_WORKFLOW_ERROR, exception);
    }

    return state;
  }

  /**
   * This method deletes the definition from :Appconnect, ESS and DB.
   * @param definitionDetails
   * @param realmId
   */
  private void deleteDefinitions(
      final DefinitionDetails definitionDetails, final String realmId) {

    logInfoForAppConnect("Delete");
    final State inputReq = new State();
    inputReq.addValue(
        SUBSCRIPTION_ID_KEY,
        authDetailsService.getAuthDetailsFromRealmId(realmId).getSubscriptionId());
    inputReq.addValue(WORKFLOW_ID_KEY, definitionDetails.getWorkflowId());
    inputReq.addValue(REALM_ID_KEY, realmId);
    // If UpdateEventScheduleTask is null, it means config is not enable for the workflow and
    // rx chain  will not include the task
    RxExecutionChain rxExecutionChain =
        new RxExecutionChain(
            inputReq, new AppConnectDeleteWorkflowTask(appConnectService, authDetailsService));
    //Todo: Remove this check after complete migration to Scheduling Service
    if(schedulingService.isEnabled(definitionDetails, definitionDetails.getOwnerId().toString())){
      Optional.ofNullable(
                      eventScheduleHelper.prepareSchedulingDeleteTask(
                              inputReq,
                              definitionDetails.getDefinitionKey()))
              .ifPresent(task -> rxExecutionChain.next(task));
    } else{
      Optional.ofNullable(
                      eventScheduleHelper.prepareScheduleStatusUpdateTask(
                              inputReq,
                              EventScheduleServiceUtil.getScheduleMetaData(
                                      definitionDetails, ScheduleStatus.DELETED)))
              .ifPresent(task -> rxExecutionChain.next(task));
    }
    rxExecutionChain.execute();
    logInfoForDatabase(InternalStatus.DELETED.name());
    definitionDetailsRepository.updateInternalStatus(
        InternalStatus.DELETED, Collections.singletonList(definitionDetails.getDefinitionId()));
  }

  /**
   * This method disable definitions from : Appconnect, ESS and DB.
   * @param definitionDetails
   * @param realmId
   */
  public void disableDefinitions(final DefinitionDetails definitionDetails, final String realmId) {
      logInfoForAppConnect("Disable");
      State inputRequest = new State();
      inputRequest.addValue(DEFINITION_ID_KEY, definitionDetails.getDefinitionId());
      inputRequest.addValue(WORKFLOW_ID_KEY, definitionDetails.getWorkflowId());
      inputRequest.addValue(
          SUBSCRIPTION_ID_KEY,
          authDetailsService.getAuthDetailsFromRealmId(realmId).getSubscriptionId());
      inputRequest.addValue(ACTIVATE_ACTION_KEY, false);
      inputRequest.addValue(REALM_ID_KEY, realmId);
    RxExecutionChain rxExecutionChain =
        new RxExecutionChain(
            inputRequest,
            new ActivateDeActivateAppConnectWorkflowTask(authDetailsService, appConnectService));
    //Todo: Remove this check after complete migration to Scheduling Service
    if(schedulingService.isEnabled(definitionDetails, definitionDetails.getOwnerId().toString())){
      SchedulingMetaData schedulingMetaData = SchedulingServiceUtil.getSchedulingMetaData(definitionDetails, com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.INACTIVE);
      Optional.ofNullable(
                      eventScheduleHelper.prepareSchedulingUpdateTask(
                              inputRequest,
                              schedulingMetaData, false))
              .ifPresent(task -> rxExecutionChain.next(task));
    }else{
      // If UpdateEventScheduleTask is null, it means config is not enable for the workflow and
      // rx chain  will not include the task
      Optional.ofNullable(
                      eventScheduleHelper.prepareScheduleStatusUpdateTask(
                              inputRequest,
                              EventScheduleServiceUtil.getScheduleMetaData(
                                      definitionDetails, ScheduleStatus.INACTIVE)))
              .ifPresent(task -> rxExecutionChain.next(task));
    }

    // execute here
    rxExecutionChain.execute();
      logInfoForDatabase(Status.DISABLED.name());
      definitionDetailsRepository.updateInternalStatus(
          null, Collections.singletonList(definitionDetails.getDefinitionId()));
  }

  private void logInfoForAppConnect(String operation) {

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Calling appconnect for ", operation)
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.DISABLE_DELETE_WORKFLOW));
  }

  private void logInfoForDatabase(String operation) {

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Setting database status: ", operation)
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.DISABLE_DELETE_WORKFLOW));
  }

}
