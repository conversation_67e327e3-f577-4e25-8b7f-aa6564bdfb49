package com.intuit.appintgwkflw.wkflautomate.was.core.jira.service;


import com.intuit.appintgwkflw.wkflautomate.was.common.config.JiraConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.jira.helper.JiraServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.JiraRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.response.JiraResponse;
import lombok.AllArgsConstructor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> his class is responsible for perfrominng operations related to jira service.
 */
@Component
@AllArgsConstructor
public class JiraServiceManager {

  private final JiraConfig vocJiraConfig;
  private final WASHttpClient jiraHttpClient;

  /**
   * This method creates a jira
   *
   * @param jiraRequest
   * @return
   */
  public JiraResponse  createJira(JiraRequest jiraRequest) {
    WASHttpResponse<JiraResponse> response = jiraHttpClient.httpResponse(
        WASHttpRequest.<String, JiraResponse>builder()
            .url(vocJiraConfig.getJiraEndPoint()) // to be read from config
            .httpMethod(HttpMethod.POST)
            .requestHeaders(JiraServiceHelper.prepareRequestHeader(vocJiraConfig.getUsername(),
                vocJiraConfig.getPassword()))
            .request(JiraServiceHelper.createJiraPayload(jiraRequest))
            .responseType(new ParameterizedTypeReference<JiraResponse>() {
            })
            .build());
    verifyResponse(response);
    return response.getResponse();
  }

  private void verifyResponse(
      WASHttpResponse<JiraResponse> response) {
    WorkflowLogger.logInfo("step=verifyJiraResponse statusCode=%s", response.statusCode());
    // non-retryable exception.
    WorkflowVerfiy.verify(
        (!response.isSuccess2xx()
            || null == response.getResponse()),
        WorkflowError.JIRA_API_CALL_FAILURE,
        response.getError());
  }
}
