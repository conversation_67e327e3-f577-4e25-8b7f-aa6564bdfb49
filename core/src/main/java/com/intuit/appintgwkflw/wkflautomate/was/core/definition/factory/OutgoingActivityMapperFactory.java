package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.BusinessRuleTaskOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.CallActivityOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.OutgoingActivityMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * Factory that returns the type of Activity Mapper to fetch the activityIds for any bpmn element
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class OutgoingActivityMapperFactory {

  private final BusinessRuleTaskOutgoingActivityMapper businessRuleTaskOutgoingActivityMapper;

  private final CallActivityOutgoingActivityMapper callActivityOutgoingActivityMapper;

  /**
   * @param element which decides which handler to return
   * @return {@link OutgoingActivityMapper}
   */
  public OutgoingActivityMapper getHandler(BaseElement element) {
    if (element instanceof BusinessRuleTask) {
      return businessRuleTaskOutgoingActivityMapper;
    } else if (element instanceof CallActivity) {
      return callActivityOutgoingActivityMapper;
    }
    throw new WorkflowGeneralException(WorkflowError.INVALID_ACTIVITY_ID);
  }

  public List<Pair<String, String>> fetchOutgoingActivityIds(String bpmnElementId,
      DefinitionInstance definitionInstance) {
    BaseElement element = definitionInstance.getBpmnModelInstance()
        .getModelElementById(bpmnElementId);
    OutgoingActivityMapper outgoingActivityMapper = getHandler(element);
    return outgoingActivityMapper.fetchOutgoingActivityIds(bpmnElementId, definitionInstance);
  }
}
