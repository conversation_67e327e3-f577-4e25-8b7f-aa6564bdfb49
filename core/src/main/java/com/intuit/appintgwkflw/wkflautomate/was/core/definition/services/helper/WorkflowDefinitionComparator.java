package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import org.apache.commons.lang3.StringUtils;
import org.skyscreamer.jsonassert.FieldComparisonFailure;
import org.skyscreamer.jsonassert.JSONCompare;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.skyscreamer.jsonassert.JSONCompareResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class WorkflowDefinitionComparator {

  public boolean hasDifferenceInWorkflowDefinitions(String expectedDefinition, String actualDefinition){
    try {
      JSONCompareResult definitionCompareResult = JSONCompare.compareJSON(expectedDefinition, actualDefinition, JSONCompareMode.STRICT);
      boolean foundDifference = false;
      if (definitionCompareResult.failed()) {
        if (definitionCompareResult.isFailureOnField()) {
          foundDifference |= checkComparisonFailures(definitionCompareResult.getFieldFailures());
        }
        if (definitionCompareResult.isMissingOnField()) {
          foundDifference |= checkComparisonFailures(definitionCompareResult.getFieldMissing());
        }
        if (definitionCompareResult.isUnexpectedOnField()) {
          foundDifference |= checkComparisonFailures(definitionCompareResult.getFieldUnexpected());
        }
      }
      return foundDifference;
    }catch(Exception e){
      WorkflowLogger.logError("step=CompareDefinitionFailed Exception occurred while trying to compare definitions", e);
      return true;
    }
  }


  private boolean checkComparisonFailures(List<FieldComparisonFailure> failures){

    boolean nonIgnorableDiffFound = false;
    for (FieldComparisonFailure ff : failures) {
      String fieldName = ff.getField();
      String expectedFieldValue = Objects.isNull(ff.getExpected()) ? "" : ff.getExpected().toString();
      String actualFieldValue = Objects.isNull(ff.getActual()) ? "" : ff.getActual().toString();

      //Ignoring when actual value is a substring of expected value (example: definition name/id)
      if (StringUtils.isNotBlank(expectedFieldValue) && StringUtils.isNotBlank(actualFieldValue) &&
          expectedFieldValue.contains(actualFieldValue)) {
        continue;
      }

//    In cases of Pre-canned definitions, actionKey is null in UserDefinition, but in single definition we are passing the value.
//    So it's okay, to ignore such errors
      if (("null").equals(expectedFieldValue) && fieldName.contains(WorkflowConstants.ACTION_KEY)) {
        continue;
      }

      //Log error if any other difference is detected
      WorkflowLogger.logError("step=CompareDefinitionDiffDetected Difference detected in field=%s expectedValue=%s actualValue=%s",
          fieldName, expectedFieldValue, actualFieldValue);
      nonIgnorableDiffFound = true;
    }
    return nonIgnorableDiffFound;
  }
}
