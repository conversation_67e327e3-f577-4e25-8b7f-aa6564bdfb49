package com.intuit.appintgwkflw.wkflautomate.was.core.processor;

import static java.util.Objects.nonNull;

import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.instance.Rule;

@UtilityClass
public class BpmnProcessingHelper {

    /**
     * This function converts the dmn rule expression to UI readable rule
     * expression format. Prepare rule expression compatible with UI.
     * <p>
     * 1. Convert String rules like Customer.equals("1") || Customer.equals("2") to Customer CONTAINS 1,2
     * 2. !Customer.equals("1") && !Customer.equals("2") to Customer NOT_CONTAINS 1,2
     * 3. Non-String rules like Amount > 100 && Amount < 200 to Amount > 100 && < 200
     *
     * @param inputLabel                  input column label
     * @param inputTypeRef                input column data type ref
     * @param paramsWithSelectAllRule         set of selectAll type parameters
     * @param dmnFriendlyExpr            input text content
     * @return rule expression
     */
    public String getUIFriendlyRuleExpression(
        String inputLabel,
        String inputTypeRef,
        Set<String> paramsWithSelectAllRule,
        String dmnFriendlyExpr) {
        // get the transformer based on the data type
        DMNDataTypeTransformer dataTypeTransformer =
                DMNDataTypeTransformers.getTransformer(
                        DMNSupportedOperator.value(inputTypeRef.toUpperCase()));

        if (StringUtils.isEmpty(dmnFriendlyExpr) && paramsWithSelectAllRule.contains(inputLabel)) {
            // If expression is null and it is part of selectAllParameters then prepare
            // rules for UI, like CONTAINS ALL_CUSTOMER for Customer
            return MessageFormat.format(
                "{0} {1}_{2}",
                WorkflowConstants.CONTAINS_OPERATOR, WorkflowConstants.KEYWORD_ALL, inputLabel.toUpperCase());
        } else if (nonNull(dataTypeTransformer)) {
            return dataTypeTransformer.transformToUserFriendlyExpression(dmnFriendlyExpr, inputLabel);
        }
        //TODO: throw exception in else
        return null;
    }

    /**
     * This function generates the all rule expression which is used
     * while preparing ruleLines from dmn rules
     *
     * @param rule dmn rule object
     * @return set of select all parameters
     */
    public Set<String> getSelectAllParametersForRule(Rule rule) {
        return Arrays.stream(rule.getDescription().getTextContent().split(WorkflowConstants.COMMA))
                .map(selectAllIdentifiers ->
                        StringUtils.isEmpty(selectAllIdentifiers)
                                ? null
                                : selectAllIdentifiers.split(WorkflowConstants.COLON)[0])
                .collect(Collectors.toSet());
    }
}
