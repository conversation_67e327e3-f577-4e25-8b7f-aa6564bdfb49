package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_DETAILS;

/**
 * This class is an implementation to handle service task event from camunda.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ServiceTaskEventHandler implements WorkflowEventHandler<ExternalTaskAssigned> {
  private final ProcessDetailsRepository processDetailsRepository;
  private final EventPublisherCapability eventPublisherCapability;
  private MetricLogger metricLogger;

  @Override
  public ExternalTaskAssigned transform(String event) {
    ExternalTaskAssigned serviceTaskEvent =
            ObjectConverter.fromJson(event, ExternalTaskAssigned.class);

    WorkflowVerfiy.verify(
            serviceTaskEvent == null || serviceTaskEvent.getWorkflowMetadata() == null,
            WorkflowError.INCORRECT_EVENT_PAYLOAD,
            "Unable to parse Or missing mandatory fields in event. payload=%s",
            event);

    return serviceTaskEvent;
  }

  @Override
  public ExternalTaskAssigned transformAndValidate(String event, Map<String, String> headers) {
    try{
      WorkflowVerfiy.verify(!headers.containsKey(EventHeaderConstants.ENTITY_ID), WorkflowError.MISSING_EVENT_HEADERS,
              "Unable to parse or missing Mandatory field entity_id in event header. headers=%s", headers);
      return transform(event);
    } catch (Exception e) {
      handleFailure(headers, e);
      throw e;
    }
  }

  @Override
  public void execute(ExternalTaskAssigned serviceTaskEvent, Map<String, String> headers) {
    try {
      EventingLoggerUtil.logInfo(
              "step=serviceTaskEventConsumed processInstanceId=%s; taskId=%s",
              this.getClass().getSimpleName(), serviceTaskEvent.getWorkflowMetadata().getProcessInstanceId(),
              headers.get(EventHeaderConstants.ENTITY_ID));

      enrichServiceTaskEntity(serviceTaskEvent);

      eventPublisherCapability.publish(buildEventHeader(headers, getHandlerDetails(serviceTaskEvent), PublishEventType.SERVICE_TASK, getName()), serviceTaskEvent);
      EventingLoggerUtil.logInfo(
              "step=serviceTaskPublished processInstanceId=%s; taskId=%s",
              this.getClass().getSimpleName(), serviceTaskEvent.getWorkflowMetadata().getProcessInstanceId(),
              headers.get(EventHeaderConstants.ENTITY_ID));
    } catch (Exception e) {
      handleFailure(headers, e);
      throw e;
    }
  }

  private void enrichServiceTaskEntity(ExternalTaskAssigned serviceTaskEvent) {
    Optional<ProcessDetails> processDetailsOpt = processDetailsRepository
            .findById(serviceTaskEvent.getWorkflowMetadata().getProcessInstanceId());

    ProcessDetails processDetails = processDetailsOpt
            .orElseThrow(() -> new WorkflowGeneralException(
                    PROCESS_DETAILS_NOT_FOUND_ERROR));

    serviceTaskEvent.getWorkflowMetadata().setWorkflowName(
            processDetails.getDefinitionDetails().getTemplateDetails().getTemplateName());
    serviceTaskEvent.getWorkflowMetadata()
            .setWorkflowOwnerId(Long.toString(processDetails.getOwnerId()));
    serviceTaskEvent.setBusinessEntityId(processDetails.getRecordId());
    serviceTaskEvent.setBusinessEntityType(
            processDetails.getDefinitionDetails().getRecordType().getRecordType());
  }

  /**
   * Build Event Header Entity for handler Details
   * @param headers
   * @param handlerDetails
   * @param eventType
   * @param eventEntityType
   * @return EventHeaderEntity
   */
  public EventHeaderEntity buildEventHeader(Map<String, String> headers,
                                            HandlerDetails handlerDetails,
                                            PublishEventType eventType,
                                            EventEntityType eventEntityType) {

    headers.put(EventHeaderConstants.TARGET_ASSET_ALIAS, handlerDetails.getHandlerId());

    return InternalEventsUtil.buildEventHeader(headers,
            PublishEventType.getPublishEventType(eventType, handlerDetails.getHandlerScope()),
            eventEntityType);
  }

  /**
   * Get handler Details from ExternalTaskAssigned
   * @param serviceTaskEvent
   * @return HandlerDetails
   */
  private HandlerDetails getHandlerDetails(ExternalTaskAssigned serviceTaskEvent) {
    Map<String, String> inputVariablesMap =
            serviceTaskEvent.getVariables().entrySet().stream()
                    .filter(entry -> ObjectUtils.isNotEmpty(entry.getValue()))
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().toString()));

    return SchemaDecoder.getHandlerDetails(inputVariablesMap)
            .orElseThrow(() -> new WorkflowGeneralException(UNSUPPORTED_HANDLER_DETAILS));
  }

  @Override
  public EventEntityType getName() {
    return EventEntityType.SERVICE_TASK;
  }

  @Override
  public void handleFailure(String event, Map<String, String> headers, Exception e) {
    //Need to raise camunda incident for non retryable errors.
  }

  private void handleFailure(Map<String, String> headers, Exception e) {
    metricLogger.logErrorMetric(MetricName.INTERNAL_CAMUNDA_SERVICE_TASK_EVENT, Type.EVENT_METRIC, e);
    TaskCompletionHandlers.getHandler(getName()).handleFailure(headers, e);
  }
}
