package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import com.amazonaws.util.CollectionUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;

import java.util.Collection;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for -
 * 1. adding extension elements to BusinessRuleTask element
 * 2. adding implicit elements to BusinessRuleTask element
 * 3. adding BusinessRuleTask element to existing subprocess
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class BusinessRuleTaskFlowNodeProcessor implements DynamicBpmnFlowNodeProcessor {

  private final DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  @Override
  public BpmnComponentType getType() {
    return BpmnComponentType.BUSINESS_RULE_TASK;
  }

  @Override
  public void addExtensionElements(
      FlowNode flowNode,
      FlowNode baseTemplateFlowNode,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    if (Objects.isNull(baseTemplateFlowNode) && Objects.nonNull(flowNode)
        && Objects.nonNull(baseTemplateBpmnModelInstance)) {
      baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById(flowNode.getId());

      if (Objects.isNull(baseTemplateFlowNode)) {
        baseTemplateFlowNode = getFirstBaseTemplateFlowNodeViaModelType(baseTemplateBpmnModelInstance);
      }
    }

    if (Objects.isNull(flowNode) && Objects.nonNull(baseTemplateFlowNode)
        && Objects.nonNull(bpmnModelInstance)) {
      flowNode = bpmnModelInstance.getModelElementById(baseTemplateFlowNode.getId());
    }

    if (Objects.isNull(flowNode) || Objects.isNull(baseTemplateFlowNode)) {
      WorkflowLogger.logError(
          "Unable to add extension elements as both BusinessRuleTask and BaseTemplateBusinessRuleTask found null.");
      return;
    }

    dynamicBpmnExtensionElementsHelper.addAllValidExtensionElements(
        flowNode, baseTemplateFlowNode, bpmnModelInstance);
  }

  @Override
  public void addEventToSubProcess(
      FlowNode sourceNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance) {
    // do nothing
  }

  @Override
  public void addImplicitEvents(
      FlowNode flowNode,
      FlowNode targetFlowNode,
      SequenceFlow outgoingSequenceFlow,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {
    // do nothing
  }

  private FlowNode getFirstBaseTemplateFlowNodeViaModelType(BpmnModelInstance baseTemplateBpmnModelInstance) {
    Collection<BusinessRuleTask> flowNodes =
            baseTemplateBpmnModelInstance.getModelElementsByType(BusinessRuleTask.class);
    if (CollectionUtils.isNullOrEmpty(flowNodes)) return null;
    return flowNodes.stream().findFirst().get();
  }
}
