package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import java.util.EnumMap;
import java.util.Map;
import lombok.experimental.UtilityClass;

/**
 * This class contains a static variable which keeps a mapping of command and its related handler.
 * The map filled using Spring refresh context handler, refer {ApplicationEventListener.class}
 * 
 * <AUTHOR>
 *
 */
@UtilityClass
public class WorkflowTaskCommands {

	private final Map<TaskCommand, WorkflowTaskCommand> WORKFLOW_TASK_COMMAND_MAP = new EnumMap<>(
			TaskCommand.class);

	/**
	 * Adds corresponding command handler to the command.
	 * 
	 * @param command     - TaskCommand to which handler needs to be mapped.
	 * @param taskCommand - Command Handler implementation of WorkflowTaskCommand.
	 */
	public void addCommand(TaskCommand command, WorkflowTaskCommand taskCommand) {
		WORKFLOW_TASK_COMMAND_MAP.put(command, taskCommand);
	}

	/**
	 * fetched relevant commandHandler for the command provided.
	 * 
	 * @param command
	 * @return
	 */
	public WorkflowTaskCommand getCommand(TaskCommand command) {

		return WORKFLOW_TASK_COMMAND_MAP.get(command);
	}

	/**
	 * Check whether the given command is registered or not.
	 * 
	 * @param command
	 * @return boolean.
	 */
	public boolean contains(TaskCommand command) {
		return WORKFLOW_TASK_COMMAND_MAP.containsKey(command);
	}

}
