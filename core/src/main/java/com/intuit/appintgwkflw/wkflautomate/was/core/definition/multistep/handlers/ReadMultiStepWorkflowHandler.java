package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;

/**
 * Interface for processing action/condition workflowSteps
 *
 * <AUTHOR>
 */
public interface ReadMultiStepWorkflowHandler {

    /**
     * This function is used to process the workflow step
     *
     * @param multiStepWorkflowEntity multiStepWorkflowEntity
     */
    void buildWorkflowStep(MultiStepWorkflowEntity multiStepWorkflowEntity);
}
