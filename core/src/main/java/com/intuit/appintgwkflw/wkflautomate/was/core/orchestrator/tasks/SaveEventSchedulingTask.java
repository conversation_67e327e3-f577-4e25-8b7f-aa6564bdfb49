package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.Definition;
import liquibase.repackaged.org.apache.commons.collections4.CollectionUtils;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.*;

/**
 * Task to save event schedules in the scheduling service.
 * <AUTHOR>
 */
@AllArgsConstructor
public class SaveEventSchedulingTask implements Task  {
    private final SchedulingService schedulingService;
    private final ActionModelToScheduleRequestMapper actionModelToCreateScheduleRequestMapper;

    /**
     * Executes the task to save event schedules in the scheduling service.
     *
     * @param state The current state of the task execution.
     * @return The updated state after execution.
     */
    @Override
    public State execute(State state) {
        Optional<List<EventScheduleWorkflowActionModel>> optionalEventScheduleWorkflowActionModels =
                state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
        if (optionalEventScheduleWorkflowActionModels.isEmpty()) {
            WorkflowLogger.logInfo("Schedule Actions Models are empty");
            return state;
        }
        List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
                optionalEventScheduleWorkflowActionModels.orElse(Collections.emptyList());
        String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
        try {
            SchedulingMetaData schedulingMetaData = state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA);
            if(ObjectUtils.isEmpty(schedulingMetaData.getDefinitionKey()))
                schedulingMetaData.setDefinitionKey(state.getValue(DEFINITION_KEY));
            //make call to scheduling service
            List<SchedulingSvcResponse> eventScheduleResponses =
                    schedulingService.createSchedules(
                            SchedulingServiceUtil.getSchedulingSvcRequestsPayload(
                                    eventScheduleWorkflowActionModels, actionModelToCreateScheduleRequestMapper, schedulingMetaData, state),
                            realmId);
            WorkflowVerfiy.verify(
                    CollectionUtils.isEmpty(eventScheduleResponses),
                    WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                    "No schedules created");
            WorkflowVerfiy.verify(
                    eventScheduleResponses.size() != eventScheduleWorkflowActionModels.size(),
                    WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                    "Schedules Not created for all actions");
        } catch (Exception e){
            state.addValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE, true);
            state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_EXCEPTION, e);
            state.addValue(AsyncTaskConstants.SAVE_SCHEDULE_ERROR_MESSAGE, WorkflowError.EVENT_SCHEDULING_CALL_FAILURE);
            // If the exception is due to migration from ESS to Scheduling, then throw the exception as we don't want the later tasks to get executed.
            if(BooleanUtils.isTrue(state.getValue(IS_ESS_TO_SCHEDULING_MIGRATION))){
                throw e;
            }
        }
        return state;
    }

    /**
     * Handles errors that occur during the execution of the task.
     * Sets the EVENT_SCHEDULING_TASK_FAILURE flag to true in the state.
     *
     * @param state The current state of the task execution.
     * @return The updated state after handling the error.
     */
    @Override
    public State onError(State state){
        state.addValue(AsyncTaskConstants.EVENT_SCHEDULING_TASK_FAILURE, true);
        return state;
    }
}
