package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4QueryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;

/**
 * Class to read and process a multi-condition/multi-step DMN
 * and generate a map of rules keyed by index values
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class MultiConditionRuleLineParser implements MultiStepBusinessRuleProcessor{
    /**
     * This function reads the dmn and converts it to a readable map
     * Ex -
     * {
     *      "0" - {
     *          "yes" - List<List<Rule>> yes rules
     *          "no" - List<List<Rule>> no rules
     *      },
     *      "1" - {
     *          "yes" - List<List<Rule>> yes rules
     *          "no" - List<List<Rule>> no rules
     *      }
     * }
     *
     * @param decisionTable        decision table instance
     * @param attributeToHeaderMap dmn headers map
     * @return dmn rules map keyed by respective index values
     */
    @Override
    public Map<String, Map<String, List<List<Rule>>>> parseDecisionTable(
            DecisionTable decisionTable,
            Map<String, Map<String, DmnHeader>> attributeToHeaderMap) {
        Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoRulesMap = new HashMap<>();
        List<Rule> rules = new LinkedList<>(decisionTable.getRules());
        // If the dmn's input columns include an Index column, then we will consider
        // it to be a multi-step DMN. Legacy dmn's will not have an Index input column
        boolean isMultiStepDmn = attributeToHeaderMap.get(WorkflowConstants.INPUT)
                .containsKey(WorkflowConstants.INDEX_COLUMN);
        if (!isMultiStepDmn) {
            return generateSingleStepRuleMap(indexToYesAndNoRulesMap, rules);
        }
        WorkflowLogger.logInfo("step=parseAndGenerateMultiStepRuleMap");
        int implicitIndexColumn = attributeToHeaderMap.get(WorkflowConstants.INPUT)
                .get(WorkflowConstants.INDEX_COLUMN).getColumnIndex();
        AtomicReference<Rule> prevRule = new AtomicReference<>();
        AtomicReference<String> prevIndexValue = new AtomicReference<>();
        rules.stream().forEach(rule -> {
            OutputEntry currentRuleOutput = rule.getOutputEntries().stream().findFirst().get();
            List<InputEntry> inputEntries = new ArrayList<>(rule.getInputEntries());
            // inputEntryText will have values like "Index == 0" or "Index == 1"
            String indexEntryText = inputEntries.get(implicitIndexColumn).getText().getTextContent();
            // extract the number at the end from the string
            String indexValue = getIndexValueFromInputEntry(indexEntryText);
            if (Objects.nonNull(prevIndexValue.get()) && prevIndexValue.get().equals(indexValue)) {
                OutputEntry ruleOutput = prevRule.get().getOutputEntries().stream().findFirst().get();
                //for OR condition
                if (isDecisionResultValueEqual(ruleOutput, currentRuleOutput)
                    && MultiStepUtil.isNotEmptyRuleLine(inputEntries)) {
                    // if current and previous decisionResult values are same then it is
                    // an OR type rule, so it will be added to the list of yes rules
                    WorkflowLogger.logInfo("step=readDmn ruleType=YES indexEntryValue=%s conditionType=OR",
                        indexEntryText);
                    supportOrConditionInRuleMap(indexToYesAndNoRulesMap, indexValue, rule);
                } else {
                    // if decisionResult values are different then add it to the list of no rules
                    WorkflowLogger.logInfo("step=readDmn ruleType=YES indexEntryValue=%s conditionType=AND",
                        indexEntryText);
                    indexToYesAndNoRulesMap.get(indexValue).get(WorkflowConstants.YES_RULE)
                        .add(new ArrayList<>(List.of(rule)));
                }
            } else {
                WorkflowLogger.logInfo("step=readDmn ruleType=NO indexEntryValue=%s conditionType=AND",
                    indexEntryText);

                //TODO move this into a private method
                if (!indexToYesAndNoRulesMap.isEmpty()) {
                    Map<String, List<List<Rule>>> prevRuleMap = indexToYesAndNoRulesMap.get(
                        prevIndexValue.get());
                    List<List<Rule>> yesRules = prevRuleMap.get(WorkflowConstants.YES_RULE);
                    yesRules.remove(yesRules.size() - 1);
                    prevRuleMap.put(WorkflowConstants.NO_RULE, List.of(Arrays.asList(prevRule.get())));
                }

                // if no existing entries are found in the rule map then insert a new entry
                // in the rule map and since the first rule we parse will always be a yes rule
                Map<String, List<List<Rule>>> ruleMap = new HashMap<>();
                List<Rule> currentYesRules = new ArrayList<>(Collections.singletonList(rule));
                List<List<Rule>> yesRules = new ArrayList<>(Collections.singletonList(currentYesRules));
                ruleMap.put(WorkflowConstants.YES_RULE, yesRules);
                indexToYesAndNoRulesMap.put(indexValue, ruleMap);

            }
            prevIndexValue.set(indexValue);
            prevRule.set(rule);
        });
        if (!indexToYesAndNoRulesMap.isEmpty()) {
            Map<String, List<List<Rule>>> prevRuleMap = indexToYesAndNoRulesMap.get(
                prevIndexValue.get());
            List<List<Rule>> yesRules = prevRuleMap.get(WorkflowConstants.YES_RULE);
            if(yesRules.size() > 1) {
                yesRules.remove(yesRules.size() - 1);
                prevRuleMap.put(WorkflowConstants.NO_RULE, List.of(List.of(prevRule.get())));
            }
        }
        return indexToYesAndNoRulesMap;
    }

    /**
     * This function adds support for or type rules in the dmn rules map
     * Or type rules will be added to the list of yes rules for a
     * particular index value
     *
     * @param indexToYesAndNoDmnRulesMap dmn rules map
     * @param indexValue                 index value
     * @param ruleObject                 rule object to be added in the map
     */
    private void supportOrConditionInRuleMap(
        Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap,
        String indexValue, Rule ruleObject) {
        // get the list of yes rules from the map keyed by indexValue
        List<List<Rule>> yesRules = indexToYesAndNoDmnRulesMap.get(indexValue)
            .get(WorkflowConstants.YES_RULE);

        // add current rule object to the existing list of yes rules
        List<Rule> lastYesRule = yesRules.get(yesRules.size() - 1);
        lastYesRule.add(ruleObject);
    }

    /**
     * This function checks if the current and previous rules decisionResult values are same
     *
     * @param firstRuleOutput  current rule output entry
     * @param secondRuleOutput previous rule output entry
     * @return boolean comparison result
     */
    private boolean isDecisionResultValueEqual(
            OutputEntry firstRuleOutput,
            OutputEntry secondRuleOutput) {
        return firstRuleOutput.getText().getTextContent().equals(secondRuleOutput.getText().getTextContent());
    }

    /**
     * This function generates the rule map for legacy
     * non multi-condition dmns. This is necessary when we
     * try to read old approval workflows via the new
     * multi-condition readOne payload
     *
     * @param indexToYesAndNoDmnRulesMap dmn rules map
     * @param rules                      list of dmn rules
     * @return dmn rules map keyed by index
     */
    private Map<String, Map<String, List<List<Rule>>>> generateSingleStepRuleMap(
            Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap,
            List<Rule> rules) {
        Map<String, List<List<Rule>>> ruleMap = new HashMap<>();
        // remove last rule from list as in old dmn's the last rule will
        // always be a no rule
        Rule noRule = rules.remove(rules.size() - 1);
        ruleMap.put(WorkflowConstants.YES_RULE, Arrays.asList(rules));
        ruleMap.put(WorkflowConstants.NO_RULE, Arrays.asList(Arrays.asList(noRule)));
        indexToYesAndNoDmnRulesMap.put(WorkflowConstants.INDEX_INITIAL_VALUE, ruleMap);
        WorkflowLogger.logInfo("step=parseAndGenerateSingleStepRuleMap yesRules=%s noRules=%s",
                ruleMap.get(WorkflowConstants.YES_RULE),
                ruleMap.get(WorkflowConstants.NO_RULE));
        return indexToYesAndNoDmnRulesMap;
    }

    /**
     * This function extracts the index value from the inputEntry text content
     * The content will be in the following format - "Index == 0" and this
     * function extracts the digit at the end and returns it as a result
     *
     * @param inputEntryContent input entry text content
     * @return index value
     */
    private String getIndexValueFromInputEntry(String inputEntryContent) {
        String indexValue = null;
        if (StringUtils.isNotEmpty(inputEntryContent)) {
            String[] indexEntryValues = inputEntryContent.split(V4QueryHelper.FILTER_OPERATOR_DOUBLE_EQUAL);
            if (indexEntryValues.length > 0) {
                indexValue = indexEntryValues[indexEntryValues.length - 1].trim();
            }
        }
        return indexValue;
    }
}
