package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

@UtilityClass
public class CustomAttributesUtil {


  /**
   * A function which takes rules as input and returns the List of custom attributes for the custom
   * rules
   *
   * <p>For example -> "rules": [ { "parameterName": "TxnAmount", "conditionalExpression": "GTE
   * 100", "parameterType": "DOUBLE" }, { "parameterName": "3600000000000155715",
   * "conditionalExpression": "GTE 120", "parameterType": "DOUBLE" } ]
   *
   * <p>retrieves generic custom field attributes from the config based on paramType and creates
   * custom field attributes over-riding the name and id of the attribute using custom field
   * ruleLine parameter name
   *
   * <p>
   *
   * <p>These list of custom field attributes will returned and appended to the default set of
   * attributes
   *
   * <p>Attribute(name -> CF3600000000000155715, type-> double,id-> CF3600000000000155715) todo
   *
   */
  public static List<Attribute> getRulesForCustomAttributes(
          Map<String, Attribute> nameToAttributeMapping, List<RuleLine.Rule> rules) {
    Set<Attribute> customFieldAttributeSet = new LinkedHashSet<>();

    try {
      List<RuleLine.Rule> customFieldRuleLines =
          rules.stream()
              .filter(customAttributeFilterPredicate(nameToAttributeMapping))
              .filter(ruleLineToGenericCustomAttributeFilterPredicate(nameToAttributeMapping))
              .collect(Collectors.toList());

      customFieldRuleLines.forEach(
          rule -> {
            Attribute customAttribute = new Attribute();
            Attribute genericCustomFieldAttribute =
                nameToAttributeMapping.get(
                    getGenericCustomFieldAttributeName(rule.getParameterType()));
            customAttribute.setName(
                new StringBuilder()
                    .append(WorkflowConstants.CUSTOM_FIELD_PREFIX)
                    .append(rule.getParameterName())
                    .toString());
            customAttribute.setId(rule.getParameterName());
            CustomWorkflowConfigUtil.copyFromAttribute(
                genericCustomFieldAttribute, customAttribute);
            customFieldAttributeSet.add(customAttribute);
          });
    } catch (Exception ex) {
      WorkflowLogger.error(
          () -> {
            return WorkflowLoggerRequest.builder()
                .message(
                    "Exception occurred while creating custom field rules: unsupported parameter type")
                .stackTrace(ex)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .className(CustomAttributesUtil.class.getSimpleName());
          });
      throw new WorkflowGeneralException(
          "Exception occurred while creating custom field rules: unsupported parameter type");
    }
    return new ArrayList<>(customFieldAttributeSet);
  }

  /**
   * This predicate filters the attributes where the parameter name of rule line doesn't match the
   * param name of attributes from config
   */
  public static Predicate<RuleLine.Rule> customAttributeFilterPredicate(
      Map<String, Attribute> nameToAttributeMapping) {
    Predicate<RuleLine.Rule> customFieldRulelinesPredicate =
        rule -> !nameToAttributeMapping.containsKey(rule.getParameterName());
    return customFieldRulelinesPredicate;
  }

  /**
   * This filter takes the nameToAttribute Map and custom field rule-lines and returns the rules
   * which matches with the customFieldAttributes based on the paramType of rule-line and suffix For
   * example rule { "parameterName": "3600000000000155715", "conditionalExpression": "GTE 100",
   * "parameterType": "DOUBLE" } it finds a generic CF Attribute based on type(for example double)
   * and suffix, if available Above case will return the rules since generic Double attribute is
   * supported for the record type
   *
   * <p>for this example { "parameterName": "CF3600000000000155715", "conditionalExpression": "GTE
   * 100", "parameterType": "xyz" }
   *
   * <p>this will not return anything since parameter type "xyz" is not supported in the config for
   * the record type
   */
  public static Predicate<RuleLine.Rule> ruleLineToGenericCustomAttributeFilterPredicate(
      Map<String, Attribute> nameToAttributeMapping) {
    Predicate<RuleLine.Rule> mappedCustomRules =
        rule ->
            nameToAttributeMapping.containsKey(
                getGenericCustomFieldAttributeName(rule.getParameterType()));
    return mappedCustomRules;
  }


  /**
   * A utility function to check if the provided rule is a customRule or not Take input as the rule
   * and based on filter of paramName being present int the nameAttributeMapping returns true or
   * false
   */
  public static boolean isCustomFieldRuleLine(Map<String,Attribute> paramNameToAttributeMapping, RuleLine.Rule rule) {

    return !paramNameToAttributeMapping.containsKey(rule.getParameterName());
  }

  /**
   * A utility function to generate the customAttribute name from the paramType
   *
   * <p>take ParameterType as input -> enum (DOUBLE) o/p -> DoubleCustomField
   *
   * <p>This is used for attributeMapping for generic customFields in config
   */
  public static String getGenericCustomFieldAttributeName(FieldTypeEnum parameterType) {

    StringBuilder customFieldName =
        new StringBuilder(StringUtils.capitalize(parameterType.toString().toLowerCase()))
            .append(WorkflowConstants.CUSTOM_FIELD_SUFFIX);
    return customFieldName.toString();
  }

  /**
   * A function which appends the customFields into the processVariable
   *
   * <p>This take input of custom field attributes, processVariableDetailMap and appends custom
   * field attributes into the passed processVariableDetailMap. This is used to add
   * customProcessVariables in definition xml
   */
  public static void updateProcessVariableMapWithCustomFieldAttributes(
      List<Attribute> customAttributes,
      Map<String, ProcessVariableDetail> processNameToDetailMap,
      Map<String, String> typeToNativeTypeMap) {
    customAttributes.forEach(
        attribute -> {
          ProcessVariableDetail processVariableDetail = new ProcessVariableDetail();
          processVariableDetail.setVariableName(attribute.getName());
          processVariableDetail.setVariableType(typeToNativeTypeMap.get(attribute.getType()));
          processNameToDetailMap.put(
              processVariableDetail.getVariableName(), processVariableDetail);
        });
  }


  /**
   * This is a generic function to modify the attribute name Input will be("CF3600000000000155715")
   * <op>"3600000000000155715"
   */
  public static String getCustomFieldName(String paramName) {
    return paramName
        .split(WorkflowConstants.CUSTOM_FIELD_PREFIX)[
        paramName.lastIndexOf(WorkflowConstants.CUSTOM_FIELD_PREFIX) + 1];
  }
}
