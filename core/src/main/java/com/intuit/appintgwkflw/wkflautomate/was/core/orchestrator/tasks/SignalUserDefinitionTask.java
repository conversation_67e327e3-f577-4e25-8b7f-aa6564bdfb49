package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_START_EVENTS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus.ERROR_SIGNALING_PROCESS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus.NO_ACTION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus.PROCESS_SIGNALLED;
import static java.util.Objects.isNull;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3SignalProcess;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 *     <p>Signal given process by process Id and message name via calling Correlate API.
 */
@AllArgsConstructor
public class SignalUserDefinitionTask implements Task {

  private Optional<DefinitionDetails> defDetailsOptional;
  private ProcessDetails proccessDetails;
  private ProcessDetailsRepository processDetailsRepository;
  private V3SignalProcess v3SignalProcess;
  private V3RunTimeHelper runtimeHelper;

  @Override
  public State execute(State inputRequest) {
    TransactionEntity transactionEntity = inputRequest.getValue(TRIGGER_TRANSACTION_ENTITY);
    Map<String, Object> initialStartEventExtensionPropertiesMap = inputRequest.getValue(BPMN_START_EVENTS);
    Boolean signalProcessResponse = null;

    try {

      signalProcessResponse =
          v3SignalProcess.signalProcessById(
              transactionEntity, defDetailsOptional, proccessDetails, initialStartEventExtensionPropertiesMap);
      inputRequest.addValue(proccessDetails.getProcessId(), signalProcessResponse);

    } catch (WorkflowGeneralException ex) {

      /**
       * in case co-relate message API fails, exception is caught, process is marked error only if
       * blockProcessOnSignalMiss is enabled and error is re-thrown.
       */
      if (transactionEntity.getEventHeaders().isBlockProcessOnSignalFailure()) {
        runtimeHelper.markProcessError(transactionEntity, proccessDetails);
      }
    }

    inputRequest.addValue(
        proccessDetails.getProcessId(), getTriggerResponse(getStatus(signalProcessResponse)));
    return inputRequest;
  }

  /**
   * populate trigger response.
   *
   * @param triggerStatus
   * @return {@link WorkflowTriggerResponse}
   */
  private WorkflowTriggerResponse getTriggerResponse(TriggerStatus triggerStatus) {
    Optional<DefinitionDetails> definitionDetails = defDetailsOptional.filter(Objects::nonNull);
    String definitionId =
        definitionDetails.isPresent() ? defDetailsOptional.get().getDefinitionId() : null;
    String definitionName =
        definitionDetails.isPresent() ? defDetailsOptional.get().getDefinitionName() : null;

    return WorkflowTriggerResponse.builder()
        .definitionId(definitionId)
        .processId(proccessDetails.getProcessId())
        .status(triggerStatus)
        .definitionName(definitionName)
        .build();
  }

  /**
   * @param signalProcess signal process response
   * @return trigger status based in signalProcess response
   */
  private TriggerStatus getStatus(Boolean signalProcess) {
    return isNull(signalProcess)
        ? ERROR_SIGNALING_PROCESS
        : (signalProcess.booleanValue() ? PROCESS_SIGNALLED : NO_ACTION);
  }

  public boolean isFatal() {
    return false;
  }
}
