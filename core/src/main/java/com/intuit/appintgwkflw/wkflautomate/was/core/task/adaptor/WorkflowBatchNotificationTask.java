package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor.OINPAdaptor;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers.OINPAdaptorMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> class is responsible for sending batch OINP notifications
 */
@AllArgsConstructor
@Component
public class WorkflowBatchNotificationTask extends WorkflowTask<BatchNotificationTask> {

  private OINPAdaptor oinpAdaptor;

  @Override
  public TypeReference<BatchNotificationTask> typeReference() {
    return new TypeReference<BatchNotificationTask>() {
    };
  }

  @Override
  public TaskType type() {
    return TaskType.BATCH_NOTIFICATION_TASK;
  }

  /**
   * Invokes OINP to send batch notification
   *
   * @param notificationTask notification info
   * @return success or failure.
   */
  @Override
  public WorkflowTaskResponse create(BatchNotificationTask notificationTask) {
    oinpAdaptor.sendBatchEvent(
        OINPAdaptorMapper.mapOINPRequestData(notificationTask),
        getTaskConfig());
    return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_CREATED)
        .txnId(notificationTask.getId()).build();
  }

  @Override
  public WorkflowTaskResponse update(BatchNotificationTask notificationTask) {
    throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
  }

  @Override
  public WorkflowTaskResponse complete(BatchNotificationTask notificationTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.NO_ACTION).build();
  }

  @Override
  public WorkflowTaskResponse failed(BatchNotificationTask notificationTask) {
    return WorkflowTaskResponse.builder().status(ActivityConstants.NO_ACTION).build();
  }

  @Override
  public WorkflowTaskResponse get(BatchNotificationTask notificationTask) {
	  return WorkflowTaskResponse.builder().status(ActivityConstants.TASK_STATUS_FAILED).build();
  }
}
