package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.AdditionalDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@UtilityClass
public class DomainEventUtil {

  public DomainEntityRequest getDomainEventRequest(
      EventHeaderEntity eventHeaderEntity, Object request, EntityChangeAction entityChangeAction, Map<String, Object> variableMap) {
    return DomainEntityRequest.builder()
        .entityChangeAction(entityChangeAction)
        .request(request)
        .additionalDetails(AdditionalDetails.builder().variables(variableMap).build())
        .eventHeaderEntity(eventHeaderEntity)
        .build();
  }


  /**
   * This method returns the Entity change action value for Runtime Activities
   *
   * @param workflowStateTransitionEvent
   * @return
   */
  public EntityChangeAction getEntityChangeActionForActivityRuntimeEvents(
      WorkflowStateTransitionEvents workflowStateTransitionEvent) {

    switch (workflowStateTransitionEvent.getEventType()) {
      case "start":
        return EntityChangeAction.CREATE;
      case "end":
      case "create":
      case "completed":
      default:
        return EntityChangeAction.UPDATE;
    }
  }

  public DomainEventName getName(DomainEntityRequest<?> domainEntityRequest) {
    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(domainEntityRequest.getRequest()),
        WorkflowError.INVALID_WORKFLOW_ERROR_INPUT);

    String name = domainEntityRequest.getRequest().getClass().getSimpleName();

    switch (name) {
      case "ProcessDetails":
        return DomainEventName.PROCESS;
      case "ActivityProgressDetails":
        return DomainEventName.ACTIVITY_RUNTIME;
      case "ActivityDetail":
        return DomainEventName.ACTIVITY;
      case "DefinitionDetails":
        return DomainEventName.DEFINITION;
      case "TemplateDetails":
        return DomainEventName.TEMPLATE;
    }
    return null;
  }
}
