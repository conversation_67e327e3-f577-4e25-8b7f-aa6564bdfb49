package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks;

import static java.lang.Boolean.TRUE;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectFetchTransactionsResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * This implementation is only used in the prf environment. The intention to use this is to mock the
 * app connect network calls for custom start duzzit in the prf environment. TODO: Remove this class
 * once app connect is available in prf environment.
 *
 * <AUTHOR>
 */
public class AppConnectCustomReminderTransactionsResponseMockImpl {

  private String data;

  public AppConnectCustomReminderTransactionsResponseMockImpl() {
    data = MockHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");
  }

  public AppConnectFetchTransactionsResponse fetchTransactionsResponse() {
    AppConnectFetchTransactionsResponse appConnectFetchTransactionsResponse =
        ObjectConverter.fromJson(data, AppConnectFetchTransactionsResponse.class);
    appConnectFetchTransactionsResponse.setSuccess(TRUE.toString());
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    Date date = new Date();
    for (Map<String, String> data : appConnectFetchTransactionsResponse.getOutput()) {
      data.put("TxnDate", dateFormat.format(date));
      data.put("TxnDueDays", "-30"); // BF 30
      data.put("TxnAmount", "1111.00");
      data.put("Id", UUID.randomUUID().toString());
    }

    return appConnectFetchTransactionsResponse;
  }
}
