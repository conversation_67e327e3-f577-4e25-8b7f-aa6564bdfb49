package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/** This class contains methods for string expression conversion for DMN and UI. */
@UtilityClass
public class StringExpressionParser {
  /**
   * This method is responsible for converting DMN friendly expression to a UI expression. Ex.
   * expression: Customer.equals("1") || Customer.equals("2") parameterName: Customer operation:
   * CONTAINS
   *
   * <p>returns "CONTAINS 1,2"
   */
  private String createDefinitionRuleExpression(
      String expression, String parameterName, String operation) {
    String result = "";
    boolean containsNegationOperator = !expression.contains(WorkflowConstants.NOT_OPERATOR);
    // First remove [!]Customer.equals. Result ("1") || ("2") or ("1") && ("2")
    result =
        expression.replaceAll(
            MessageFormat.format(
                containsNegationOperator ? "{0}.{1}" : "!{0}.{1}",
                parameterName,
                operation.toLowerCase()),
            "");

    // In case of Contains and Any_Match case, there will be OR Modifiers
    result = result.replace(WorkflowConstants.OR_MODIFIER, "");

    // In case of NOT_Contains and NO_MATCH case, there will be AND Modifiers
    result = result.replaceAll(WorkflowConstants.AND_MODIFIER, "");

    // Result now is ("1") ("2"). Remove all the extra characters to get 1,2
    String values = valueExtractor(result);

    switch (operation) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        result =
            expressionCreator(
                WorkflowConstants.CONTAINS_OPERATOR, containsNegationOperator, values);
        break;
      case WorkflowConstants.EQUALS_OPERATOR:
        result =
            expressionCreator(WorkflowConstants.EQUALS_OPERATOR, containsNegationOperator, values);
        break;
      default:
        throw new WorkflowGeneralException((WorkflowError.UNSUPPORTED_OPERATION));
    }

    return result;
  }
  /**
   * Select ALL check for Contains Use Case. Returns true if it is case of Select All
   *
   * @param parameterName : Name of the DMN Parameter [ex, Customer,Department]
   * @param expression : input expression/rule to be set.
   * @return
   */
  public boolean isSelectAllRule(String parameterName, String expression) {
    String selectAllExpressionValue =
        MessageFormat.format("{0}_{1}", WorkflowConstants.KEYWORD_ALL, parameterName);
    String[] stringTokens = expression.split(WorkflowConstants.SPACE);
    WorkflowVerfiy.verify(stringTokens.length < 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
    String value = stringTokens[1].trim();
    return selectAllExpressionValue.equalsIgnoreCase(value);
  }

  public String transformEqualsExpressionforUI(String expression, String parameterName) {
    return createDefinitionRuleExpression(
        expression, parameterName, WorkflowConstants.EQUALS_OPERATOR);
  }

  public String transformContainsExpressionforUI(String expression, String parameterName) {
    return createDefinitionRuleExpression(
        expression, parameterName, WorkflowConstants.CONTAINS_OPERATOR);
  }
  /**
   * This method takes care of String Expression in case of no mutual exclusion. i.e. Expression
   * having both contains and not contains operator
   *
   * <p>For example Customer CONTAINS "1","2","43" && NOT_CONTAINS "3"
   *
   * @param expression : Expression containing both Contains and Not-Contains query
   * @param parameterName : Parameter Name of the DMN
   * @param dmnFriendlyExpression : Returned Parsed/Transformed Expression
   * @return
   */
  public String prepareStringExpressionForDMN(
      String type, String expression, String parameterName, String dmnFriendlyExpression) {
    String[] stringToken = expression.split(WorkflowConstants.AND_MODIFIER);
    for (int i = 0; i < stringToken.length; i++) {
      /**
       * limit = 2 added to handle cases of string operands having space char
       * Example: CONTAINS "ab","cd ef" && NOT_CONTAINS "pq","wx yz"
       *
       * TODO: handle special chars in operand values by encoding during read/decoding during create
       */
      String[] stringTokens = stringToken[i].trim().split(WorkflowConstants.SPACE,2);
      List<String> values = Arrays.asList(stringTokens[1].split(WorkflowConstants.COMMA));
      if (WorkflowConstants.CONTAINS_OPERATOR.equalsIgnoreCase(stringTokens[0])
          || WorkflowConstants.NOT_CONTAINS.equalsIgnoreCase(stringTokens[0])
          || WorkflowConstants.ANY_MATCH.equalsIgnoreCase(stringTokens[0])
          || WorkflowConstants.NO_MATCH.equalsIgnoreCase(stringTokens[0])) {
        dmnFriendlyExpression =
            prepareStringExpressionForDMN(
                type, values, parameterName, stringTokens[0], dmnFriendlyExpression);
      }
    }
    // Removing Trailing spaces
    return dmnFriendlyExpression.trim();
  }

  /**
   * @param values : Token Values
   * @param parameterName : Name of the DMN Column
   * @param operatorKeyword : IF it is of type contains and not contains
   * @param dmnFriendlyExpression : Returned Parsed/Transformed Expression
   * @return
   */
  public String prepareStringExpressionForDMN(
      String type,
      List<String> values,
      String parameterName,
      String operatorKeyword,
      String dmnFriendlyExpression) {

    if (type.equals(WorkflowConstants.STRING_MODIFIER)) {
      switch (operatorKeyword) {
        case WorkflowConstants.CONTAINS_OPERATOR:
          dmnFriendlyExpression =
              StringOperations.createContainsExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        case WorkflowConstants.NOT_CONTAINS:
          dmnFriendlyExpression =
              StringOperations.createNotContainsExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        case WorkflowConstants.ANY_MATCH:
          dmnFriendlyExpression =
              StringOperations.createAnyMatchExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        case WorkflowConstants.NO_MATCH:
          dmnFriendlyExpression =
              StringOperations.createNoMatchExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        default:
          throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
      }
    } else if (type.equals(WorkflowConstants.LIST_MODIFIER)) {
      switch (operatorKeyword) {
        case WorkflowConstants.CONTAINS_OPERATOR:
          dmnFriendlyExpression =
              StringOperations.createContainsExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        case WorkflowConstants.NOT_CONTAINS:
          dmnFriendlyExpression =
              StringOperations.createNotContainsExpression(
                  dmnFriendlyExpression, parameterName, values);
          break;
        default:
          throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
      }
    }

    if (StringUtils.isEmpty(dmnFriendlyExpression)) {
      return dmnFriendlyExpression;
    }
    return dmnFriendlyExpression.trim();
  }

  private String expressionCreator(String operator, boolean containsNegationOperator, String values) {
    if (operator.equalsIgnoreCase(WorkflowConstants.CONTAINS_OPERATOR)) {
      return expressionCreatorHelper(
          WorkflowConstants.ANY_MATCH,
          WorkflowConstants.NO_MATCH,
          containsNegationOperator,
          values);
    } else {
      return expressionCreatorHelper(
          WorkflowConstants.CONTAINS_OPERATOR,
          WorkflowConstants.NOT_CONTAINS,
          containsNegationOperator,
          values);
    }
  }

  private String expressionCreatorHelper(
      String operator1, String operator2, boolean containsNegationOperator, String values) {
    return MessageFormat.format(
            WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE,
            containsNegationOperator ? operator1 : operator2,
            values)
        .trim();
  }

  private String valueExtractor(String expression) {
    // expression: ("1 3") ("2"). This method performs a regex match for string inside parenthesis.
    // We extract all the data inside parenthesis and concatenate them using a comma and return
    List<String> allMatches = new ArrayList<>();
    Pattern regex = Pattern.compile(WorkflowConstants.PARENTHESIS_REGEX);
    Matcher regexMatcher = regex.matcher(expression);
    while (regexMatcher.find()) {
      /**
       * each regexMatcher.group(1) will return the value within the parenthesis.
       * for expression ("1 3") ("2"), it will be "1 3" and "2"
       */
      allMatches.add(regexMatcher.group(1));
    }

    return String.join(WorkflowConstants.COMMA, allMatches)
        .replaceAll(WorkflowConstants.QUOTE, WorkflowConstants.BLANK);
  }

  public String[] splitStringByPattern(String input, String pattern) {
    String[] result = input.split(pattern);
    return result;
  }
}
