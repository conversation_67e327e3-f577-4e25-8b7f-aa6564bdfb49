package com.intuit.appintgwkflw.wkflautomate.was.core.ticket;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OfflineTicketServiceImpl implements OfflineTicketService {

  private final AuthDetailsServiceHelper authDetailsService;
  private final WASContextHandler contextHandler;

  @Override
  public WorkflowGenericResponse updateOfflineTicket() {
    return WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).build();
  }
}
