package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.CompositeStepBuilderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * MultiSplitDefinitionConditionBuilder is used to build the rule lines of the
 * payload, when we have multi splits instead of multi condition. Multi Split is more than two
 * braches , instead of traditional yes/no branch.
 */
@Component(WorkflowBeansConstants.MULTI_SPLIT_DEFINITION_CONDITION_BUILDER)
public class MultiSplitConditionRuleLineBuilder extends MultiStepConditionRuleLineBuilder {

  private final WASContextHandler wasContextHandler;

  public MultiSplitConditionRuleLineBuilder(
      WASContextHandler wasContextHandler,
      MultiStepRuleLineBuilder multiStepRuleLineBuilder,
      CompositeStepBuilderFactory compositeStepBuilderFactory
  ) {
    super(wasContextHandler, multiStepRuleLineBuilder, compositeStepBuilderFactory);
    this.wasContextHandler = wasContextHandler;
  }

  /**
   * This function initiates dfs traversal of the rulesMap that was generated post reading the dmn
   *
   * @param record                  Record instance
   * @param multiStepWorkflowEntity MultiStepWorkflowEntity instance
   * @param indexToRulesMap         map of dmn rules keyed by index values
   * @param attributeToHeaderMap    map of dmn input column values
   */
  @Override
  public void convertRulesToWorkflowStepCondition(
      Record record,
      MultiStepWorkflowEntity multiStepWorkflowEntity,
      Map<String, Map<String, List<List<Rule>>>> indexToRulesMap,
      Map<String, Map<String, DmnHeader>> attributeToHeaderMap) {

    if (indexToRulesMap.isEmpty()) {
      // if an empty map is returned after reading the dmn then
      // we cannot construct condition type workflowStep, return directly
      return;
    }
    // get list of yes rules for 0th key value from the dmn rules map
    List<List<Rule>> yesRules = indexToRulesMap.get(
            String.valueOf(WorkflowConstants.START_INDEX_VALUE))
        .get(WorkflowConstants.YES_RULE);


    // generate first workflowStep's id which will be used in upcoming recursive calls

    WorkflowLogger.logInfo("step=convertRulesToWorkflowStepCondition ruleIndex=%s",
        WorkflowConstants.START_INDEX_VALUE);

    yesRules.stream().forEach(yesRule -> {

      WorkflowStep currentWorkflowStep = new WorkflowStep();

      GlobalId currentWorkflowStepId = MultiStepUtil.generateWorkflowStepId(
          currentWorkflowStep.getTypeId(),
          WorkflowConstants.CUSTOM_DECISION_ELEMENT,
          wasContextHandler);

      buildWorkflowStepFromRuleDFS(
          record,
          yesRule,
          new ArrayList<>(),
          attributeToHeaderMap,
          multiStepWorkflowEntity,
          indexToRulesMap,
          currentWorkflowStepId);
    });

  }
}
