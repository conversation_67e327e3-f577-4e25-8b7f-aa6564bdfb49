package com.intuit.appintgwkflw.wkflautomate.was.core.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import lombok.experimental.UtilityClass;

import java.util.Map;

/** <AUTHOR> */
@UtilityClass
public class SchedulingWorkflowNameMapper {
    private static final Map<WorkflowNameEnum, WorkflowNameEnum> workflowNameMap = Map.of(
            WorkflowNameEnum.CUSTOM_REMINDER, WorkflowNameEnum.REMINDER,
            WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS, WorkflowNameEnum.SCHEDULED_ACTIONS
    );

    public static WorkflowNameEnum getActionsByType(String workflowName) {
        return workflowNameMap.get(WorkflowNameEnum.fromName(workflowName));
    }
}
