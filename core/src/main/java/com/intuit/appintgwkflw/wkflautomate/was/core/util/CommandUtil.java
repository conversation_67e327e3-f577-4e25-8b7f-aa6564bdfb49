package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.DataStoreDeleteTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CamundaDeleteDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DataStoreDeleteDefinitionAndProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class CommandUtil {

  private DefinitionDetailsRepository definitionDetailsRepository;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private DataStoreDeleteTaskService dataStoreDeleteTaskService;

  /**
   * @param definitionLists input definition list from DB
   * @param inputRequest input State request for tasks
   * @return List of Tasks to execute in chain
   */
  public Pair<List<Task>, List<Task>> prepareCamundaAndDatastoreTasks(
      List<DefinitionDetails> definitionLists, State inputRequest) {

    if (CollectionUtils.isEmpty(definitionLists)) {
      return Pair.of(new ArrayList<>(), new ArrayList<>());
    }
    return Pair.of(prepareCamundaTasks(definitionLists, true), definitionLists.stream().map(definitionDetails ->
        new DataStoreDeleteDefinitionAndProcessTask(definitionDetailsRepository,
            definitionDetails.getDefinitionId(), dataStoreDeleteTaskService)).collect(Collectors.toList()));
  }

  /**
   * Delete the definition in camunda without deleting the processes
   * @param definitionLists input definition list from DB
   * @param cascade true if processes to be deleted
   * @return
   */
  public List<Task> prepareCamundaTasks(
      List<DefinitionDetails> definitionLists, boolean cascade) {

    List<Task> camundaDeleteDefinitionTaskList = new ArrayList<>();

    if (CollectionUtils.isEmpty(definitionLists)) {
      return camundaDeleteDefinitionTaskList;
    }

    definitionLists.stream()
        .forEach(
            definitionDetails -> camundaDeleteDefinitionTaskList.add(
                new CamundaDeleteDefinitionTask(
                    bpmnEngineDefinitionServiceRest,
                    definitionDetails.getDefinitionId(),
                    cascade,
                    true)));
    return camundaDeleteDefinitionTaskList;
  }
}