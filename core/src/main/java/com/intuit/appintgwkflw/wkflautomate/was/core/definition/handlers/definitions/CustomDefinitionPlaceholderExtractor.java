package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_PARAMETERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ACTION_SELECTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FIELD_VALUE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RULE_LINE_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.STRING_TYPE_CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsValueType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.springframework.stereotype.Component;

/**
 * This class extracts placeholder values for a given definition. As of now, it is used during
 * create/update of a custom definition as part of single definition.
 *
 * The output in json format looks like this:-
 *
 * * <pre>{@Code
 * {
 *   "bpmn_placeholder_values": {
 *     "process_variables": {
 *       "processVariableName": {
 *         "value": "xyz",
 *         "type": "string"
 *       }
 *     },
 *     "user_variables": {
 *       "actionKey:actionId": {
 *         "parameters": {
 *           "parameterName-1": {
 *             "fieldValue": [
 *               "xyz"
 *             ]
 *           },
 *           "parameterName-2": {
 *             "fieldValue": [
 *               "pqr"
 *             ]
 *           }
 *         },
 *         "selected": "true"
 *       },
 *
 *      "recurrenceRule": "{\"recurType\":\"WEEKLY\",\"interval\":1,\"daysOfWeek\":[\"THURSDAY\"],\"startDate\":{\"secondOfDay\":0,\"minuteOfDay\":0,\"centuryOfEra\":20,\"yearOfEra\":2022,\"yearOfCentury\":22,\"weekyear\":2022,\"monthOfYear\":2,\"weekOfWeekyear\":8,\"hourOfDay\":0,\"minuteOfHour\":0,\"secondOfMinute\":0,\"millisOfSecond\":0,\"year\":2022,\"dayOfMonth\":22,\"millisOfDay\":0,\"dayOfWeek\":2,\"era\":1,\"dayOfYear\":53,\"chronology\":{\"zone\":{\"uncachedZone\":{\"cachable\":true,\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"fixed\":false,\"id\":\"Asia/Kolkata\"}},\"zone\":{\"uncachedZone\":{\"cachable\":true,\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"fixed\":false,\"id\":\"Asia/Kolkata\"},\"millis\":1645468200000,\"afterNow\":false,\"beforeNow\":true,\"equalNow\":false},\"active\":true,\"$sdk_validated\":true}"
 *     }
 *   },
 *   "dmn_placeholder_values": {
 *       "rule_line_variables": {
 *         "TxnAmount": "GTE 10",
 *         "TxnDueDays": "AF 10"
 *      }
 *     }
 *   }
 * }
 *
 */
@Component(WorkflowBeansConstants.CUSTOM_DEFINITION_PLACEHOLDER_EXTRACTOR)
@AllArgsConstructor
public class CustomDefinitionPlaceholderExtractor implements PlaceholderExtractor {

  private final CustomWorkflowConfig customWorkflowConfig;
  private final PlaceholderExtractorHelper placeholderExtractorHelper;
  private final WASContextHandler wasContextHandler;



  /**
   * Extract placeholder values from the definition
   *
   * @param definitionInstance
   * @return map of bpmn and dmn placeholder values
   */
  @Override
  @Metric(name = MetricName.CUSTOM_DEFINITION_PLACEHOLDER_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public Map<String, Object> extractPlaceholderValue(
      DefinitionInstance definitionInstance) {
    // Map to store bpmn and dmn placeholder values.
    Map<String, Object> definitionPlaceholders = new HashMap<>();

    final String recordType = definitionInstance.getDefinition().getRecordType();

    // Store user entered field values like subject/message in the email
    Map<String, Object> userVariables = new HashMap<>();
    // Store process variables to be used by process instance like action is select/not-selected
    Map<String, ProcessVariableData> processVariables = new HashMap<>();
    // Store user entered workflow rules
    Map<String, Object> ruleLineVariables = new HashMap<>();

    //Extract rule line variables from definition
    ruleLineVariables.put(
            RULE_LINE_VARIABLES,
            SingleDefinitionUtil.getRuleLinesForWorkflowCondition(
                    definitionInstance.getDefinition()));

    // Gets the process and user variables from the workflow steps
    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep -> {
              Map<String, Object> stepActionVariables =
                  getStepActionVariables(workflowStep.getActions(), recordType, workflowStep.getTrigger());
              userVariables.putAll((Map<String, Object>) stepActionVariables.get(USER_VARIABLES));
              processVariables.putAll(
                  (Map<String, ProcessVariableData>) stepActionVariables.get(PROCESS_VARIABLES));
            });

    FlowElement startEventElement =
        CustomWorkflowUtil.findStartEventElement(definitionInstance.getBpmnModelInstance());
    // Gets the process variables from the start event
    processVariables.putAll(
        SingleDefinitionUtil.getProcessVariablesFromStartEvent(startEventElement));

    // Sets the value of sequence variables extracted from the start-event
    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep -> {
              getActionSelectionProcessVariables(workflowStep)
                  .forEach(
                      (key, value) -> {
                        processVariables.putIfAbsent(
                            key, ProcessVariableData.builder().type(STRING_TYPE_CAMUNDA).build());
                        processVariables.get(key).setValue(value);
                      });
            });

    //check for recurrence and set as process variables if available
    SingleDefinitionUtil.setRecurrenceRuleInUserVariables(definitionInstance, userVariables);
    SingleDefinitionUtil.setRecurrenceVariablesInProcessVariables(definitionInstance, processVariables, startEventElement);



    // bpmn placeholder values comprising of user and process variables
    Map<String, Object> bpmnPlaceHolders = new HashMap<>();
    bpmnPlaceHolders.put(PROCESS_VARIABLES, processVariables);
    bpmnPlaceHolders.put(USER_VARIABLES, userVariables);

    // add locale here
    Map<String, String> localeMap = new HashMap<>();
    localeMap.put(
        WASContextEnums.INTUIT_WAS_LOCALE.getValue(),
        wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE));
    bpmnPlaceHolders.put(USER_META_DATA, localeMap);

    definitionPlaceholders.put(BPMN_PLACEHOLDER_VALUES, bpmnPlaceHolders);
    definitionPlaceholders.put(DMN_PLACEHOLDER_VALUES, ruleLineVariables);
    return definitionPlaceholders;
  }

  /**
   * Get the process and user variable map from trigger, condition and action
   *
   * @param actions
   * @param recordType
   * @return map of process variable and its corresponding parameters
   */
  private Map<String, Object> getStepActionVariables(
          List<ActionMapper> actions, String recordType, Trigger trigger) {
    Map<String, Object> actionVariables = new HashMap<>();

    // Map to store user variable Field Values like subject in the email, set by the user for a
    // particular actionKey:actionId.
    // actionkey can be reminder/approval, actionid can be create-task/send-customer-email
    Map<String, Object> userVariables = new HashMap<>();
    // Map to store process variables used by camunda for evaluation like create task is true/false
    Map<String, ProcessVariableData> processVariables = new HashMap<>();

    //  User Variables for trigger
    if (ObjectUtils.isNotEmpty(trigger)) {
      String triggerId = placeholderExtractorHelper.getOriginalActionIdFromTrigger(trigger);
      Map<String, Object> variableMap = SingleDefinitionUtil.getTriggerVariables(trigger);
      userVariables.putAll(Collections.singletonMap(triggerId, variableMap));
    }

    // process variables map for action
    actions.forEach(
        actionMapper -> {
          String actionId =
              placeholderExtractorHelper.getOriginalActionId(actionMapper.getAction());
          Map<String, Object> variableMap = getActionVariables(actionMapper, actionId, recordType);
          // uniquely identifies the action.
          String actionDescription = String.format("%s:%s", actionMapper.getActionKey(), actionId);
          userVariables.putAll(
              Collections.singletonMap(actionDescription, variableMap.get(USER_VARIABLES)));
          processVariables.putAll(
              (Map<String, ProcessVariableData>) variableMap.get(PROCESS_VARIABLES));
        });

    actionVariables.put(PROCESS_VARIABLES, processVariables);
    actionVariables.put(USER_VARIABLES, userVariables);
    return actionVariables;
  }

  /**
   * Get the process and user variable map from action
   *
   * @param actionMapper
   * @param actionId
   * @param recordType
   * @return map of process variable and its corresponding parameters
   */
  private Map<String, Object> getActionVariables(
      ActionMapper actionMapper, String actionId, String recordType) {
    Action action = actionMapper.getAction();
    if (Objects.isNull(action)) {
      return Collections.emptyMap();
    }

    Map<String, Object> actionVariables = new HashMap<>();

    // Get user parameters and config process parameters.
    Map<String, Object> consolidatedActionVariables =
        getConsolidatedActionVariables(actionMapper, actionId, recordType);

    Map<String, Object> actionUserVariables = new HashMap<>();
    // Add user parameters corresponding to the action id
    actionUserVariables.put(ACTION_PARAMETERS, consolidatedActionVariables.get(USER_VARIABLES));
    actionUserVariables.put(ACTION_SELECTED, BooleanUtils.toBoolean(action.isSelected()));
    actionVariables.put(USER_VARIABLES, actionUserVariables);

    // Add config process parameters
    actionVariables.put(PROCESS_VARIABLES, consolidatedActionVariables.get(PROCESS_VARIABLES));
    return actionVariables;
  }

  /**
   * Get the config parameters for a particular record type and action
   *
   * @param actionMapper
   * @param actionId
   * @param recordType
   * @return map of process variable and its corresponding parameters
   */
  private Map<String, Object> getConsolidatedActionVariables(
      ActionMapper actionMapper, String actionId, String recordType) {
    Map<String, Object> result = new HashMap<>();

    // Get action record from the config.
    com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action recordAction =
        CustomWorkflowUtil.getConfigActionEntry(
            this.customWorkflowConfig, recordType, actionMapper.getActionKey(), actionId);

    Map<String, List<String>> clientPassedActionParameters =
        actionMapper.getAction().getParameters().stream()
            .collect(
                Collectors.toMap(
                    InputParameter::getParameterName, param -> param.getFieldValues()));

    // Extract non-configurable metadata parameters like TaskType/Project-type
    Map<String, HandlerDetails.ParameterDetails> actionMetaData =
        CustomWorkflowUtil.extractProjectMetaDataForAction(recordAction, actionMapper, recordType);

    // Iterate through all action parameters. We will store field values of each parameter after
    // replacing help variable display names.
    Map<String, ProcessVariableData> processVariables = new HashMap<>();
    Map<String, Object> userVariables = new HashMap<>();
    for (Parameter parameter : recordAction.getParameters()) {
      List<String> fieldValues = parameter.getFieldValues();
      final String parameterName = parameter.getName();
      // if the parameter is part of metadata like task-type/project-type, then it has to be
      // replaced first.
      if (actionMetaData.containsKey(parameterName)) {
        fieldValues = actionMetaData.get(parameterName).getFieldValue();
      }
      // If field values are passed by client, use it
      fieldValues = clientPassedActionParameters.getOrDefault(parameterName, fieldValues);

      fieldValues = CustomWorkflowUtil.replaceHelpVariableInFieldValues(parameter, fieldValues);

      if (ParameterDetailsValueType.PROCESS_VARIABLE.equals(parameter.getValueType())) {
        processVariables.put(
            parameterName, ProcessVariableData.builder().type(parameter.getFieldType()).build());
      } else {
        userVariables.put(parameterName, Collections.singletonMap(FIELD_VALUE, fieldValues));
      }
    }

    // Extract Action help variables map, will extract name and type from config, will have no
    // fieldValue
    processVariables.putAll(getHelpProcessVariablesFromActionParams(recordAction.getParameters()));

    result.put(PROCESS_VARIABLES, processVariables);
    result.put(USER_VARIABLES, userVariables);
    return result;
  }

  /**
   * Get a map of helpvariables from action parameters. The map key is help variable's internal
   * name.
   *
   * @param parameters
   * @return map of process variable and its corresponding parameters
   */
  private Map<String, ProcessVariableData> getHelpProcessVariablesFromActionParams(
      List<Parameter> parameters) {
    Map<String, ProcessVariableData> helpVariables = new HashMap<>();

    for (Parameter parameter : parameters) {
      if (!ObjectUtils.isEmpty(parameter.getHelpVariables())) {
        for (String helpVariable : parameter.getHelpVariables()) {
          String[] tokens = helpVariable.split(WorkflowConstants.COLON);
          String handlerFieldName = tokens[1];

          if (!helpVariables.containsKey(handlerFieldName)) {
            helpVariables.put(
                handlerFieldName, ProcessVariableData.builder().type(tokens[2]).build());
          }
        }
      }
    }
    return helpVariables;
  }

  /**
   * Get the value of sequence flow elements from the actions, true if action is selected otherwise
   * false.
   *
   * @param workflowStep
   * @return map of process variable and its selected value
   */
  private Map<String, String> getActionSelectionProcessVariables(
      WorkflowStep workflowStep) {
    Map<String, String> processVariablesMap = new HashMap<>();
    workflowStep
        .getActions()
        .forEach(
            actionMapper ->
                processVariablesMap.put(
                    placeholderExtractorHelper.getOriginalActionId(actionMapper.getAction()),
                    String.valueOf(BooleanUtils.toBoolean(actionMapper.getAction().isSelected()))));
    return processVariablesMap;
  }

}
