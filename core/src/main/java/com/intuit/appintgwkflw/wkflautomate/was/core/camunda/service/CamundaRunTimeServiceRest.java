package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_EXTEND_LOCK_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_FAILURE_TASK_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_PROCESS_INSTANCE_DELETE_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_TASK_NOT_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_UPDATE_PROCESS_INSTANCE_VARIABLE_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.RULE_EVALUATION_CAMUNDA_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.TRIGGER_NO_VARIABLES_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.TRIGGER_START_PROCESS_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UPDATE_EXECUTION_VARIABLE_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CamundaRestConstants.ASYNC_CORRELATION_NO_ACTIVE_PROCESSES_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CamundaRestConstants.EXTERNAL_TASK_IS_SUSPENDED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CamundaRestConstants.MISMATCH_MESSAGE_CORRELATION;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.CamundaRequestResponseLoggerConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.ServiceTaskCompleteRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowErrorDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskFailure;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExternalTaskSuccess;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CamundaUpdateRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.StartProcessRequest;
import io.github.resilience4j.retry.annotation.Retry;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class CamundaRunTimeServiceRest implements BPMNEngineRunTimeServiceRest {

  private static final String URL_SEPERATOR = "/";

  private CamundaWASClient httpClient;

  private OfflineTicketClient offlineTicketClient;

  private WASContextHandler contextHandler;

  private WorkflowCoreConfig workflowCoreConfig;

  private CamundaRestUtil camundaRestUtil;
  
  private CamundaRequestResponseLoggerConfig camundaRequestResponseLoggerConfig;

  /**
   * @param startProcessRequest: Contains BPMN definition ID, variable map and response type to
   *     start process
   * @return Response: Starts the process and returns the API response
   */
  @Override
  public Map<String, Object> startProcess(StartProcessRequest startProcessRequest) {

    StringBuilder startProcessUrl = new StringBuilder();
    startProcessUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append(URL_SEPERATOR)
        .append(startProcessRequest.getDefinitionId())
        .append(workflowCoreConfig.getRestEndpointStartInstance());

    WASHttpRequest<StartProcessRequest, Map<String, Object>> wasHttpRequest =
        WASHttpRequest.<StartProcessRequest, Map<String, Object>>builder()
            .httpMethod(HttpMethod.POST)
            .request(startProcessRequest)
            .requestHeaders(populateAuthorization())
            .responseType(new ParameterizedTypeReference<Map<String, Object>>() {})
            .url(startProcessUrl.toString())
            .build();

    logStartProcessRequestBody(startProcessRequest);
    WASHttpResponse<Map<String, Object>> response = httpClient.httpResponse(wasHttpRequest);
    logStartProcessResponseBody(response);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), TRIGGER_START_PROCESS_ERROR, response.getError());
    /* If incorrect format is send to Camunda then variables are not initialized. Process will start but no variables will be set.*/
    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(response.getResponse().get(WorkflowConstants.BPMN_DMN_VARIABLES)),
        TRIGGER_NO_VARIABLES_FOUND);
    return response.getResponse();
  }

  /**
   * @param evaluateRuleRequest: Contains Definition ID, variable map, headers and response type
   * @return EvaluateRuleResponse: Evaluates the DMN and gives back the response with result
   */
  @Override
  public List<Map<String, Object>> evaluateDecision(EvaluateRuleRequest evaluateRuleRequest) {

    StringBuilder evaluateDecisionUrl = new StringBuilder();
    evaluateDecisionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointDecisionDefinition())
        .append(URL_SEPERATOR)
        .append(evaluateRuleRequest.getDefinitionId())
        .append(workflowCoreConfig.getRestEndpointEvaluateDecisionDefinition());

    WASHttpRequest<Map<String, Object>, List<Map<String, Object>>> wasHttpRequest =
        WASHttpRequest.<Map<String, Object>, List<Map<String, Object>>>builder()
            .httpMethod(HttpMethod.POST)
            .request(evaluateRuleRequest.getVariableMap())
            .responseType(new ParameterizedTypeReference<List<Map<String, Object>>>() {})
            .url(evaluateDecisionUrl.toString())
            .build();

    WASHttpResponse<List<Map<String, Object>>> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), RULE_EVALUATION_CAMUNDA_ERROR, response.getError());
    return response.getResponse();
  }

  /**
   * @param correlateMessage contains process instance id, message name variables and headers
   * @return signal tasks for given execution id
   */
  @Override
  @Retry(name = ResiliencyConstants.CAMUNDA_SIGNAL)
  public boolean correlateMessage(CorrelateMessage correlateMessage) {
    return correlateMsg(correlateMessage);
  }

  /**
   * This method does not retry but throws Retriable exception
   *
   * @param correlateMessage contains process instance id, message name variables and headers
   * @return signal tasks for given execution id
   */
  @Override
  public boolean correlateMessageEvent(CorrelateMessage correlateMessage) {
    return correlateMsg(correlateMessage);
  }

  private boolean correlateMsg(CorrelateMessage correlateMessage) {
    StringBuilder correlateMessageUrl = new StringBuilder();
    correlateMessageUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointMessage());

    WASHttpRequest<CorrelateMessage, Map<String, Object>> wasHttpRequest =
        WASHttpRequest.<CorrelateMessage, Map<String, Object>>builder()
            .httpMethod(HttpMethod.POST)
            .request(correlateMessage)
            .responseType(new ParameterizedTypeReference<Map<String, Object>>() {})
            .url(correlateMessageUrl.toString())
            .build();

    // adding message name in MDC context
    contextHandler.addKey(WASContextEnums.MESSAGE_EVENT, correlateMessage.getMessageName());

    WASHttpResponse<Map<String, Object>> correlateMessageResponse =
        httpClient.httpResponse(wasHttpRequest,RetryHandlerName.PROCESS_ENGINE_EXCEPTION_CODE);
    return handleResponse(correlateMessageResponse);
  }

  @Override
  public boolean correlateAllMessage(CorrelateAllMessage correlateAllMessage) {

    StringBuilder correlateMessageUrl = new StringBuilder();
    correlateMessageUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointMessage());
    // Calling via System Offline Ticket
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());

    WASHttpRequest<CorrelateAllMessage, Object> wasHttpRequest =
        WASHttpRequest.<CorrelateAllMessage, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(correlateAllMessage)
            .requestHeaders(httpHeaders)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(correlateMessageUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), TRIGGER_SIGNAL_PROCESS_ERROR, response.getError());
    return true;
  }

  @Override
  public boolean correlateAllMessageAsync(CorrelateAllMessageAsync correlateAllMessageAsync) {

    StringBuilder correlateMessageUrl = new StringBuilder();
    correlateMessageUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointMessageAsync());
    // Calling via System Offline Ticket
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());

    WASHttpRequest<CorrelateAllMessageAsync, Object> wasHttpRequest =
        WASHttpRequest.<CorrelateAllMessageAsync, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(correlateAllMessageAsync)
            .requestHeaders(httpHeaders)
            .responseType(new ParameterizedTypeReference<>() {})
            .url(correlateMessageUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !verifyAsyncCorrelationSuccess(response),
        TRIGGER_SIGNAL_PROCESS_ERROR,
        response.getError());
    return true;
  }

  /**
   * suppressing BadUserRequestException error thrown in case of no active processes for definition
   * @param response
   * @return boolean
   */
  private boolean verifyAsyncCorrelationSuccess(Object response) {
    WASHttpResponse<Object> httpResponse = (WASHttpResponse<Object>) response;
    return httpResponse.isSuccess2xx()
        || (StringUtils.isNotEmpty(httpResponse.getError())
            && httpResponse.getError().matches(ASYNC_CORRELATION_NO_ACTIVE_PROCESSES_ERROR));
  }

  /**
   * Marks an external Task as complete
   *
   * @param task
   * @param taskId
   */
  //@Retry(name = ResiliencyConstants.CAMUNDA_SIGNAL)
  public void completeTask(ExternalTaskSuccess task, String taskId) {
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getExternalTask())
        .append(URL_SEPERATOR)
        .append(taskId)
        .append(workflowCoreConfig.getTaskComplete());

    WASHttpRequest<ExternalTaskSuccess, Object> wasHttpRequest =
        WASHttpRequest.<ExternalTaskSuccess, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(task)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(deployDefinitionUrl.toString())
            .build();

	WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest,
			RetryHandlerName.PROCESS_ENGINE_EXCEPTION);

    WorkflowVerfiy.verify(
            response.statusCode() == HttpStatus.NOT_FOUND.value(),
            CAMUNDA_TASK_NOT_FOUND,
            response.getError());

    WorkflowVerfiy.verify(
        checkExternalTaskSuspended(response),
        CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED,
        response.getError());

    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), CAMUNDA_COMPLETE_TASK_FAILED, response.getError());
  }

  /**
   * Marks an external Task as failed
   *
   * @param task
   * @param taskId
   */
  public void failureTask(ExternalTaskFailure task, String taskId) {
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getExternalTask())
        .append(URL_SEPERATOR)
        .append(taskId)
        .append(workflowCoreConfig.getTaskFailure());

    WASHttpRequest<ExternalTaskFailure, Object> wasHttpRequest =
        WASHttpRequest.<ExternalTaskFailure, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(task)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(deployDefinitionUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        response.statusCode() == HttpStatus.NOT_FOUND.value(),
        CAMUNDA_TASK_NOT_FOUND,
        response.getError());

    WorkflowVerfiy.verify(
        checkExternalTaskSuspended(response),
        CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED,
        response.getError());
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), CAMUNDA_FAILURE_TASK_FAILED, response.getError());
  }

  /**
   * Marks a service Task as complete/failed
   * @param serviceTaskCompleteRequest
   */
  public void messageServiceTask(ServiceTaskCompleteRequest serviceTaskCompleteRequest) {
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaHostUrl())
        .append(workflowCoreConfig.getServiceTask());
    WASHttpRequest<ServiceTaskCompleteRequest, WorkflowGenericResponse> wasHttpRequest =
          WASHttpRequest.<ServiceTaskCompleteRequest, WorkflowGenericResponse>builder()
                  .httpMethod(HttpMethod.POST)
                  .request(serviceTaskCompleteRequest)
                  .responseType(new ParameterizedTypeReference<WorkflowGenericResponse>() {})
                  .url(deployDefinitionUrl.toString())
                  .build();

    WASHttpResponse<WorkflowGenericResponse> response = httpClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy.verify(
            !response.isSuccess2xx(), CAMUNDA_COMPLETE_TASK_FAILED, response.getError());
    WorkflowVerfiy.verify(
            response.getResponse().getStatus() != ResponseStatus.SUCCESS, CAMUNDA_COMPLETE_TASK_FAILED,
            Optional.ofNullable(response.getResponse().getErrorDetails()).orElse(WorkflowErrorDetails.builder().build()).getErrorMessage());
  }

  private boolean handleResponse(WASHttpResponse<Map<String, Object>> response) {

    if (response.isSuccess2xx()) {
      return true;
    }
    final String error = response.getError();

    WorkflowError workflowError = WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR;

    /**
     * if Downstream throws 400 and Correlation Exception message return
     * TRIGGER_PROCES_DEFINITION_ERROR that maps to 400 bad request.
     */
    if (HttpStatus.BAD_REQUEST.value() == response.statusCode() && correlationFailed(error)) {
      workflowError = WorkflowError.TRIGGER_PROCES_DEFINITION_ERROR;
    }

    verifyAndThrowRetriableErrorResponse(error, workflowError);

    final WorkflowError workflowErrorFinal = workflowError;

    WorkflowVerfiy.verify(true, () -> {
      throw new WorkflowGeneralException(workflowErrorFinal, error);
    });
    return false;
  }

  /**
   * @param error response error
   * @param workflowError {@link WorkflowError}
   */
  private void verifyAndThrowRetriableErrorResponse(
      final String error, final WorkflowError workflowError) {
    // exception will be thrown if true
    WorkflowVerfiy.verify(
        correlationFailed(error),
        () -> {
          throw new WorkflowRetriableException(workflowError, error);
        });
  }

  /**
   * @param error error from downstream
   * @return true - if error is mismatch correlation
   */
  private boolean correlationFailed(final String error) {
    return StringUtils.isNotEmpty(error) && error.contains(MISMATCH_MESSAGE_CORRELATION);
  }

  /** Use System offline ticket if Authorization ticket is not present in context. */
  private HttpHeaders populateAuthorization() {
    HttpHeaders httpHeaders = new HttpHeaders();

    // if Auth is not present use system offline ticket.
    if (StringUtils.isBlank(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER))) {
      httpHeaders.set(
          HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    }
    return httpHeaders;
  }

  /**
   * @param externalTaskId
   * @return
   */
  public ExternalTaskDetail getExtenalTaskDetails(String externalTaskId) {
    StringBuilder processInstanceUpdateVariableUrl = new StringBuilder();
    processInstanceUpdateVariableUrl.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getExternalTask()).append(URL_SEPERATOR).append(externalTaskId);

    WASHttpResponse<ExternalTaskDetail> response = httpClient
        .getResponse(processInstanceUpdateVariableUrl.toString(), null, ExternalTaskDetail.class);

    WorkflowVerfiy.verify(!response.isSuccess2xx(), EXTERNAL_TASK_DETAILS_NOT_FOUND_ERROR,
        response.getError());

    return response.getResponse();
  }

  /**
   * Updates Execution-Instance Local Variable.
   *
   * @param executionUpdateRequest
   */
  public void updateExecutionVariables(CamundaUpdateRequest executionUpdateRequest) {
    StringBuilder processInstanceUpdtVariableUrl = new StringBuilder();
    processInstanceUpdtVariableUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointExecution())
        .append(URL_SEPERATOR)
        .append(executionUpdateRequest.getExecutionId())
        .append(workflowCoreConfig.getExecutionLocalVariables());

    // Calling via System Offline Ticket
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set(
        HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());

    WASHttpRequest<CamundaUpdateRequest, WorkflowGenericResponse> wasHttpRequest =
        WASHttpRequest.<CamundaUpdateRequest, WorkflowGenericResponse>builder()
            .httpMethod(HttpMethod.POST)
            .request(executionUpdateRequest)
            .requestHeaders(httpHeaders)
            .responseType(new ParameterizedTypeReference<WorkflowGenericResponse>() {})
            .url(processInstanceUpdtVariableUrl.toString())
            .build();

    WASHttpResponse<WorkflowGenericResponse> response = httpClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), UPDATE_EXECUTION_VARIABLE_FAILED, response.getError());
  }

  /**
   * Updates Process-Instance Variable.
   *
   * @param updateRequest - ProcessInstanceUpdateRequest having variables to save in Camunda workflow.
   */
  public void updateProcessInstanceVariables(CamundaUpdateRequest updateRequest) {
    StringBuilder processInstanceUpdtVariableUrl = new StringBuilder();
    processInstanceUpdtVariableUrl.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessInstance()).append(URL_SEPERATOR)
        .append(updateRequest.getProcessInstanceId())
        .append(workflowCoreConfig.getRestEndpointProcessInstanceVariables());

    WASHttpRequest<CamundaUpdateRequest, WorkflowGenericResponse> wasHttpRequest = WASHttpRequest
        .<CamundaUpdateRequest, WorkflowGenericResponse>builder()
        .httpMethod(HttpMethod.POST)
        .request(updateRequest)
        .responseType(new ParameterizedTypeReference<WorkflowGenericResponse>() {
        }).url(processInstanceUpdtVariableUrl.toString()).build();

    WASHttpResponse<WorkflowGenericResponse> response = httpClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy
        .verify(!response.isSuccess2xx(), CAMUNDA_UPDATE_PROCESS_INSTANCE_VARIABLE_FAILED,
            response.getError());
  }

  /**
   * Delete Process-Instance.
   *
   * @param
   */
  public void deleteProcessInstance(String processInstanceId) {
    String processInstanceDeleteUrl = camundaRestUtil.getCamundaBaseURL()
        + workflowCoreConfig.getRestEndpointProcessInstance() + URL_SEPERATOR
        + processInstanceId;

    WASHttpRequest<Void, Void> wasHttpRequest = WASHttpRequest
        .<Void, Void>builder()
        .httpMethod(HttpMethod.DELETE)
        .responseType(new ParameterizedTypeReference<Void>() {
        }).url(processInstanceDeleteUrl).build();

    WASHttpResponse<Void> response = httpClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy
        .verify(!response.isSuccess2xx(), CAMUNDA_PROCESS_INSTANCE_DELETE_FAILED,
            response.getError());
  }

  /**
   * Marks an external Task as complete without WorkerId. The caller doesn't need to provide worker
   * id, CamundaService will automatically query the service's db internally and will get the id
   * required.
   *
   * @param task
   * @param taskId
   */
  public void customCompleteTask(ExternalTaskSuccess task, String taskId) {

    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaHostUrl())
        .append(workflowCoreConfig.getV1EndPoint())
        .append(URL_SEPERATOR)
        .append(taskId)
        .append(workflowCoreConfig.getTaskComplete());

    WASHttpRequest<ExternalTaskSuccess, Object> wasHttpRequest =
        WASHttpRequest.<ExternalTaskSuccess, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(task)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(deployDefinitionUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        response.statusCode() == HttpStatus.NOT_FOUND.value(),
        CAMUNDA_TASK_NOT_FOUND,
        response.getError());

    WorkflowVerfiy.verify(
        checkExternalTaskSuspended(response),
        CAMUNDA_TASK_SUSPENDED_COMPLETE_FAILED,
        response.getError());
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), CAMUNDA_COMPLETE_TASK_FAILED, response.getError());
  }

  /**
   * Marks an external Task as complete without WorkerId. The caller doesn't need to provide worker
   * id, CamundaService will automatically query the service's db internally and will get the id
   * required.
   *
   * @param task
   * @param taskId
   */
  public void customFailureTask(ExternalTaskFailure task, String taskId) {
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaHostUrl())
        .append(workflowCoreConfig.getV1EndPoint())
        .append(URL_SEPERATOR)
        .append(taskId)
        .append(workflowCoreConfig.getTaskFailure());

    WASHttpRequest<ExternalTaskFailure, Object> wasHttpRequest =
        WASHttpRequest.<ExternalTaskFailure, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(task)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(deployDefinitionUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        response.statusCode() == HttpStatus.NOT_FOUND.value(),
        CAMUNDA_TASK_NOT_FOUND,
        response.getError());

    WorkflowVerfiy.verify(
        checkExternalTaskSuspended(response),
        CAMUNDA_TASK_SUSPENDED_FAILURE_FAILED,
        response.getError());

    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), CAMUNDA_FAILURE_TASK_FAILED, response.getError());
  }
  
  //@Retry(name = ResiliencyConstants.CAMUNDA_SIGNAL)
  public void extendLock(ExtendExternalTask task, String taskId) {

    StringBuilder extendLockUrl = new StringBuilder();
    extendLockUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getExternalTask())
        .append(URL_SEPERATOR)
        .append(taskId)
        .append(workflowCoreConfig.getExtendLock());

    WASHttpRequest<ExtendExternalTask, Object> wasHttpRequest =
        WASHttpRequest.<ExtendExternalTask, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(task)
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url(extendLockUrl.toString())
            .build();

    WASHttpResponse<Object> response = httpClient.httpResponse(wasHttpRequest);

    WorkflowVerfiy.verify(
            response.statusCode() == HttpStatus.NOT_FOUND.value(),
            CAMUNDA_TASK_NOT_FOUND,
            response.getError());

    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), CAMUNDA_EXTEND_LOCK_FAILED, response.getError());
  }

  private boolean checkExternalTaskSuspended(Object response) {
    WASHttpResponse<Object> httpResponse = (WASHttpResponse<Object>) response;
    return StringUtils.isNotEmpty(httpResponse.getError()) && httpResponse.getError().matches(EXTERNAL_TASK_IS_SUSPENDED);
  }

  private void logStartProcessRequestBody(StartProcessRequest startProcessRequest) {
    logIfOwnerIdMatches(startProcessRequest, "startProcessRequest for camunda start process");
  }

  private void logStartProcessResponseBody(WASHttpResponse<Map<String, Object>> response) {
    logIfOwnerIdMatches(response, "response for camunda start process");
  }

  private void logIfOwnerIdMatches(Object objectToLog, String message) {
    Optional.ofNullable(camundaRequestResponseLoggerConfig)
            .map(CamundaRequestResponseLoggerConfig::getOwnerIds)
            .filter(ObjectUtils::isNotEmpty)
            .filter(ownerIds -> isValidOwnerId(contextHandler.get(WASContextEnums.OWNER_ID), ownerIds))
            .ifPresent(ownerIds -> WorkflowLogger.info(() ->
                    WorkflowLoggerRequest.builder()
                            .className(this.getClass().getSimpleName())
                            .message(
                                    message + " for realmId: %s and for userId: %s is " + ObjectConverter.toJson(objectToLog),
                                    contextHandler.get(WASContextEnums.OWNER_ID),
                                    contextHandler.get(WASContextEnums.INTUIT_USERID)
                            ).downstreamComponentName(DownstreamComponentName.CAMUNDA)
                            .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS)));
  }

  private boolean isValidOwnerId(String ownerIdStr, Set<Long> ownerIdsFromConfig) {
    if (StringUtils.isBlank(ownerIdStr) || !NumberUtils.isParsable(ownerIdStr)) {
      return false;
    }
    Long ownerIdLong = Long.parseLong(ownerIdStr);
    return ownerIdsFromConfig.contains(ownerIdLong);
  }

}