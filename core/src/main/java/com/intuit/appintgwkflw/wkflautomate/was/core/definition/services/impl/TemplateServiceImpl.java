package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static com.google.common.collect.MoreCollectors.onlyElement;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.templateTagFilterPredicate;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.ACTIVITY_DETAILS_TABLE_ATTRIBUTES_KEY_MODEL_ATTRIBUTES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TEMPLATE_TYPE_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.FilterUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.helpers.CamundaServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ClientAccessConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepTemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.util.BpmnOverwatchUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.DynamicBpmnWasCamundaTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.WorkflowTaskProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.TemplateHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.TemplateProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator.TemplateValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateFilter;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateFilters;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.services.WFAIService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowTemplateConstant;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BPMNTriggerType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BPMNTriggerTypeDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ConnectionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TriggerType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTemplateResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.VariabilityEngineService;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.globalid.GlobalIdV41;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.query.FilterExpression;
import com.intuit.v4.workflows.Template;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.camunda.CamundaInputOutputImpl;
import org.camunda.bpm.model.bpmn.instance.BoundaryEvent;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.Event;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.MessageEventDefinition;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.Task;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.xml.ModelInstance;
import org.camunda.bpm.model.xml.ModelValidationException;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.camunda.commons.utils.IoUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
public class TemplateServiceImpl implements TemplateService {

  private TemplateDetailsRepository templateDetailsRepository;
  @SuppressWarnings("rawtypes")
  private BpmnProcessorImpl bpmnProcessor;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private WASContextHandler contextHandler;
  private ProviderHelper providerHelper;
  private TranslationService translationService;
  private AuthDetailsServiceHelper authDetailsServiceHelper;
  private TemplateBuilder templateBuilder;
  private TemplateValidator templateValidator;
  private WorkflowTaskConfig bpmnParserConfig;
  private ClientAccessConfig clientAccessConfig;
  private TemplateLabelsService templateLabelsService;
  private DomainEventConfig domainEventTopiConfig;
  private CustomWorkflowConfig customWorkflowConfig;
  private MultiStepTemplateBuilder multiStepTemplateBuilder;
  private AppConfig appConfig;

  private VariabilityEngineService variabilityEngineService;

  private WFAIService wfaiService;

  private ActivityDetailsRepository activityDetailsRepository;

  public TemplateServiceImpl(TemplateDetailsRepository templateDetailsRepository, BpmnProcessorImpl bpmnProcessor,
      BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest, WASContextHandler contextHandler,
      ProviderHelper providerHelper, TranslationService translationService,
      AuthDetailsServiceHelper authDetailsServiceHelper, TemplateValidator templateValidator, WorkflowTaskConfig bpmnParserConfig,
      ClientAccessConfig clientAccessConfig, TemplateLabelsService templateLabelsService,
      DomainEventConfig domainEventTopiConfig, CustomWorkflowConfig customWorkflowConfig,
      @Qualifier(WorkflowBeansConstants.TEMPLATE_BUILDER) TemplateBuilder templateBuilder,
      @Qualifier(WorkflowBeansConstants.MULTI_STEP_TEMPLATE_BUILDER) MultiStepTemplateBuilder multiStepTemplateBuilder,
      WFAIService wfaiService, AppConfig appConfig, VariabilityEngineService variabilityEngineService,
      ActivityDetailsRepository activityDetailsRepository) {
    this.templateDetailsRepository = templateDetailsRepository;
    this.bpmnProcessor = bpmnProcessor;
    this.bpmnEngineDefinitionServiceRest = bpmnEngineDefinitionServiceRest;
    this.contextHandler = contextHandler;
    this.providerHelper = providerHelper;
    this.translationService = translationService;
    this.authDetailsServiceHelper = authDetailsServiceHelper;
    this.templateBuilder = templateBuilder;
    this.templateValidator = templateValidator;
    this.bpmnParserConfig = bpmnParserConfig;
    this.clientAccessConfig = clientAccessConfig;
    this.templateLabelsService = templateLabelsService;
    this.domainEventTopiConfig = domainEventTopiConfig;
    this.customWorkflowConfig = customWorkflowConfig;
    this.multiStepTemplateBuilder = multiStepTemplateBuilder;
    this.wfaiService = wfaiService;
    this.appConfig = appConfig;
    this.variabilityEngineService = variabilityEngineService;
    this.activityDetailsRepository = activityDetailsRepository;
  }

  @Override
  public Optional<TemplateDetails> getTemplateDetails(final String templateId) {
    return templateDetailsRepository.findById(templateId);
  }

  @Override
  public Optional<List<TemplateDetails>> getReferencedChildTemplates(final TemplateDetails templateDetails) {
    return templateDetailsRepository.getTemplateDetailsByParentIdAndVersion(
        templateDetails.getId(), templateDetails.getVersion());
  }

  /*
   * @param templates
   * Validates the size of the template, DMN references in BPMN and also does Camunda library API validation
   */
  @Override
  public boolean validateTemplate(final MultipartFile[] templates, final TemplateMetadata templateMetadata)
          throws ModelValidationException, IOException {
    boolean hasBPMNFile = false;
    final List<String> inputDmnList = new ArrayList<>();
    List<String> dmnListFromBpmn = new ArrayList<>();


    for (final MultipartFile template : templates) {
      final String templateName = template.getOriginalFilename();
      if (templateName.endsWith(WorkflowConstants.BPMN_TYPE)) {
        hasBPMNFile = true;
        final BpmnModelInstance modelInstance = Bpmn.readModelFromStream(template.getInputStream());
        Bpmn.validateModel(modelInstance);
        validateStepDetails(modelInstance, templateMetadata);
        dmnListFromBpmn = getAllDMNFromBPMN(modelInstance);

        //validateCallActivityInBPMN(modelInstance);

      } else if (templateName.endsWith(WorkflowConstants.DMN_TYPE)) {
        DmnProcessorUtil.validateDMN(template.getInputStream());
        final DmnModelInstance modelInstance = Dmn.readModelFromStream(template.getInputStream());
        Dmn.validateModel(modelInstance);
        inputDmnList.add(SchemaDecoder.getTemplateName(modelInstance, ModelType.DMN));
      } else {
        throw new WorkflowGeneralException(WorkflowError.INVALID_FILE_FORMAT);
      }
    }
    WorkflowVerfiy.verify(!hasBPMNFile, WorkflowError.NO_BPMN_FILE);

    return verifyExtraDmn(dmnListFromBpmn, inputDmnList);
  }

  /**
    Validation required in parent BPMN to prevent Child BPMNs from having state transition events inside start event of CallActivity
    This check is required to prevent process_not_found errors when that state transition event is consumed in WAS.
    This check can be removed if we start saving the child process in DB upon receiving the state transition event.
   */
  private void validateCallActivityInBPMN(BpmnModelInstance bpmnModelInstance) {
    // Find all CallActivity elements in the BPMN model

    Optional<Process> processOptional = bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst();

    WorkflowVerfiy.verify(processOptional.isEmpty(), WorkflowError.INVALID_BPMN_PROCESS_DETAILS);

    Collection<CallActivity> callActivities = processOptional.get().getChildElementsByType(CallActivity.class);

    if (CollectionUtils.isEmpty(callActivities)) {
      return;
    }

    Set<String> distinctCalledElements =
        callActivities.stream().map(CallActivity::getCalledElement).collect(Collectors.toSet());

    Optional<List<TemplateDetails>> optionalCalledElementTemplateDetails =
        templateDetailsRepository.findTopByTemplateNameInAndModelTypeAndStatusOrderByCreatedDateDesc(
            new ArrayList<>(distinctCalledElements), ModelType.BPMN, Status.ENABLED);

    // if any child template is not present in database, then parent bpmn should not be saved.
    WorkflowVerfiy.verify(optionalCalledElementTemplateDetails.isEmpty() || optionalCalledElementTemplateDetails.get().isEmpty()
            || (distinctCalledElements.size() != optionalCalledElementTemplateDetails.get().size()),
        WorkflowError.CALL_ACTIVITY_TEMPLATE_NOT_FOUND);

    optionalCalledElementTemplateDetails.get().stream().forEach(templateDetails -> {

      List<ActivityDetail> startEventsActivityDetails =
          activityDetailsRepository.findByTemplateIdAndActivityType(
              templateDetails.getId(), BpmnComponentType.START_EVENT.getName());

      ActivityDetail activityDetail = BpmnStartElementUtil.fetchInitialStartEventActivityDetail(startEventsActivityDetails);

      Map<String, String> extensionProperties = ObjectConverter.fromJson(activityDetail.getAttributes(),
          new TypeReference<Map<String, Object>>() {});

      if (MapUtils.isNotEmpty(extensionProperties)) {
        Map<String, Object> modelAttributes = ObjectConverter.fromJson(
            ObjectConverter.toJson(extensionProperties.get(ACTIVITY_DETAILS_TABLE_ATTRIBUTES_KEY_MODEL_ATTRIBUTES)),
            new TypeReference<Map<String, Object>>() {});

        if (MapUtils.isNotEmpty(modelAttributes) && modelAttributes.containsKey(WorkFlowVariables.EVENTS.getName())) {
          WorkflowLogger.logError("State transition event found for calledElement= %s", templateDetails.getId());
          throw new WorkflowGeneralException(WorkflowError.EVENT_NOT_SUPPORTED, templateDetails.getId());
        }
      } else {
        WorkflowLogger.logError("No extension properties found for calledElement= %s", templateDetails.getId());
        throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_ELEMENT, templateDetails.getId());
      }
    });
  }

  private boolean validateBpmnAndDmnTemplateModels(BpmnModelInstance bpmnModelInstance,
                                                   List<DmnModelInstance> dmnModelInstanceList, TemplateMetadata templateMetadata){
    WorkflowVerfiy.verify(Objects.isNull(bpmnModelInstance), WorkflowError.NO_BPMN_FILE);
    WorkflowVerfiy.verify(Objects.isNull(dmnModelInstanceList), WorkflowError.MISSING_DMN);

    Bpmn.validateModel(bpmnModelInstance);
    validateStepDetails(bpmnModelInstance, templateMetadata);
    final List<String> dmnListFromBpmn = getAllDMNFromBPMN(bpmnModelInstance);

    List<String> inputDmnList = new ArrayList<>();
    for (DmnModelInstance dmnModelInstance : dmnModelInstanceList){
      Dmn.validateModel(dmnModelInstance);
      inputDmnList.add(SchemaDecoder.getTemplateName(dmnModelInstance, ModelType.DMN));
    }

    return verifyExtraDmn(dmnListFromBpmn, inputDmnList);
  }

  private boolean verifyExtraDmn(final List<String> dmnListFromBpmn, List<String> inputDmnList){
    for (final String dmn : dmnListFromBpmn) {
      if (inputDmnList.contains(dmn)) {
        inputDmnList.remove(dmn);
      } else {
        throw new WorkflowGeneralException(WorkflowError.MISSING_DMN);
      }
    }
    WorkflowVerfiy.verify(inputDmnList.size() > 0, WorkflowError.EXTRA_DMN);
    return true;
  }

  @Override
  @Metric(name = MetricName.READ_CUSTOM_TEMPLATE, type = Type.API_METRIC)
  public Template getTemplateMetadataForRecordType(
      String source,
      @Tracer(key = WASContextEnums.RECORD_TYPE) String recordType,
      String actionKey,
      boolean isMultiStep) {
    Template template = null;
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    if (record == null) {
      WorkflowLogger.logWarn(
          "Template metadata requested for invalid record type. Source={}, RecordType={}",
          source,
          recordType);
      throw new WorkflowGeneralException(WorkflowError.INVALID_INPUT);
    }
    // if read one custom template graphql query includes templateData and the particular
    // record and action key combination has call activity support defined in the
    // custom workflow config then return the new multi-step template object otherwise
    // return the old template
    if (MultiStepUtil.isTemplateDataForReadOneTemplate(record, actionKey) && isMultiStep) {
      template = multiStepTemplateBuilder.build(source, recordType, actionKey, false);
    } else {
      template = templateBuilder.build(source, recordType, actionKey, false);
    }
    variabilityEngineService.filterTemplate(template);
    return template;
  }

  @Override
  public List<Template> getConfigTemplates() {
    return templateBuilder.buildConfigTemplates();
  }

  @Override
  public Template getConfigTemplateById(final String templateId, boolean isMultiStep) {
    ConfigTemplate configTemplate = customWorkflowConfig.getTemplateMap().get(templateId);
    // TODO: Add Alpha check here to not return Project Template
    if (ObjectUtils.isEmpty(configTemplate)) {
      return null;
    }
    Record record = customWorkflowConfig.getRecordObjForType(configTemplate.getRecord());
    // if read one precanned graphql call includes templateStep, and the precanned template
    // has steps defined in the config and the particular action group has call activity defined
    // default config then return new multi step template object otherwise return the old template
    Template template;
    if (MultiStepUtil.isTemplateDataForReadOneTemplate(
        record, configTemplate.getActionGroups().get(0).getId()) &&
        !CollectionUtils.isEmpty(configTemplate.getSteps()) && isMultiStep) {
      template = multiStepTemplateBuilder.getConfigTemplateById(templateId);
    }else
      template = templateBuilder.getConfigTemplateById(templateId);

    postProcessing(template);
    return template;
  }

  @Override
  public List<Template> filterConfigTemplatesFromDbTemplates(List<Template> preCannedConfigTemplates, List<Template> templates) {

    Set<String> configTemplateIds =
        preCannedConfigTemplates.stream()
            .map(template -> template.getId().getLocalId().toLowerCase())
            .collect(Collectors.toSet());
    // Exclude all such templates which are there in the config as well as the db.
    templates =
        templates.stream()
            .filter(template -> !configTemplateIds.contains(template.getName().toLowerCase()))
            .collect(Collectors.toList());
    templates.addAll(preCannedConfigTemplates);
    return templates;
  }

  private void postProcessing(@NotNull Template template){
    try{
      if (Objects.isNull(template)) return;

      wfaiService.populateAssignee(template);
    }
    catch (Exception e ){
      WorkflowLogger.error(
              () ->
                      WorkflowLoggerRequest.builder()
                              .className(this.getClass().getSimpleName())
                              .stackTrace(e)
                              .message("Error while Populating Assignee")
                              .downstreamComponentName(DownstreamComponentName.WAS));
    }
  }

  @SuppressWarnings("unchecked")
  private void validateStepDetails(final BpmnModelInstance bpmnModelInstance, TemplateMetadata templateMetadata) {

    final Collection<StartEvent> startEventList = bpmnModelInstance.getModelElementsByType(StartEvent.class);

    // Get the main process from the BPMN model
    Process mainProcess = bpmnModelInstance.getModelElementsByType(Process.class).iterator().next();

    // Get the start event from the main process (i.e from where a workflow gets started)
    StartEvent processStartEvent = mainProcess.getChildElementsByType(StartEvent.class).iterator().next();

    // Check for async before ONLY on the start event from where the process can start
    if (templateMetadata.isCanStartProcessAsync()) {
      WorkflowVerfiy.verify(processStartEvent.getEventDefinitions().isEmpty() && !processStartEvent.isCamundaAsyncBefore(),
              WorkflowError.ASYNC_BEFORE_NOT_SET);
    }

    startEventList.forEach(
        startEvent -> {
          final Map<String, String> properties =
              BpmnProcessorUtil.getMapOfCamundaProperties(startEvent.getExtensionElements());
          if (MapUtils.isEmpty(properties)) {
            return;
          }
          final String stepDetailsData =
              properties.getOrDefault(WorkFlowVariables.STEP_DETAILS_KEY.getName(), null);
          if (StringUtils.isEmpty(stepDetailsData)) {
            return;
          }
          ObjectConverter.fromJson(stepDetailsData, Map.class)
              .values()
              .forEach(
                  stepDetailData -> {
                    final List<String> stepDetailDataList = (ArrayList<String>) stepDetailData;
                    stepDetailDataList.forEach(
                        stepDetailId ->
                            WorkflowVerfiy.verifyNull(
                                bpmnModelInstance.getModelElementById(stepDetailId),
                                WorkflowError.STEP_DETAILS_NOT_FOUND));
                  });
        });
  }

  private List<String> getAllDMNFromBPMN(final BpmnModelInstance bpmnModelInstance) {
    final List<String> dmnList = new ArrayList<>();
    final Collection<BusinessRuleTask> businessRuleTaskList =
        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class);
    for (final BusinessRuleTask businessRuleTask : businessRuleTaskList) {
      //In case of Single Definition, evaluation is an external task hence the CamundaDecisionRef
      // will come empty.
      String dmnName = Optional.ofNullable(businessRuleTask.getCamundaDecisionRef())
          .orElse(businessRuleTask.getId());
      dmnName = dmnName.substring(dmnName.lastIndexOf("/") + 1);
      dmnList.add(dmnName);
    }

    // The business rule task can also be modelled using a service task where WAS will store the DMN
    // At runtime, DMN stored in WAS can be used to evaluate the DMN
    // If the task is modelled as DMN, the id of the DMN is stored in field `dmnTasks`
    Optional.ofNullable(BpmnProcessorUtil.getDmnServiceTasks(bpmnModelInstance))
        .ifPresent(dmnList::addAll);
    return dmnList;
  }

  @Override
  public WorkflowGenericResponse saveTemplate(
      final MultipartFile[] templates, final TemplateMetadata templateMetadata) {
    return processTemplates(templates, templateMetadata, false);
  }

  @Override
  public WorkflowGenericResponse updateTemplate(
      final MultipartFile[] templates, final TemplateMetadata templateMetadata) {
    return processTemplates(templates, templateMetadata, true);
  }

  @Override
  public void fetchTemplates(final String templateName, final String zipfileName) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("fetchTemplates")
                .message("Fetch template for template_name = %s", templateName)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));
    WorkflowVerfiy.verify(
        (null == templateName || !templateName.endsWith(WorkflowConstants.BPMN_TYPE)),
        WorkflowError.INVALID_FILE_FORMAT);
    final List<TemplateDetails> templateDetailsList = new ArrayList<>();
    final TemplateDetails templateDetails = getTemplateByName(templateName);
    WorkflowVerfiy.verifyNull(templateDetails, WorkflowError.BPMN_DOESNT_EXIST);
    templateDetailsList.add(templateDetails);
    final Optional<List<TemplateDetails>> list =
        templateDetailsRepository.findByParentId(templateDetails.getId());
    list.ifPresent(templateDetailsList::addAll);
    createZipFile(templateDetailsList, zipfileName);
  }

  private void createZipFile(final List<TemplateDetails> templateDetailsList, final String zipfileName) {

    try (final FileOutputStream fos = new FileOutputStream(zipfileName);
        final ZipOutputStream zos = new ZipOutputStream(fos)) {

      for (final TemplateDetails template : templateDetailsList) {
        final InputStream is = new ByteArrayInputStream(template.getTemplateData());
        final String zipEntryExtension =
            (template.getModelType() == ModelType.BPMN)
                ? WorkflowConstants.BPMN_TYPE
                : WorkflowConstants.DMN_TYPE;
        // begin writing a new ZIP entry, positions the stream to the start of the entry data
        zos.putNextEntry(new ZipEntry(template.getTemplateName() + zipEntryExtension));
        IOUtils.copy(is, zos);
        is.close();
        zos.closeEntry();
      }
    } catch (final IOException ioe) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(WorkflowError.TEMPLATE_READ_EXCEPTION.getErrorDescription())
                  .stackTrace(ioe)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.TEMPLATE_DETAILS)
                  .className(this.getClass().getSimpleName()));
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_READ_EXCEPTION);
    }
  }

  @Override
  public WorkflowGenericResponse fetchTemplateByVersion(final String templateName,
      final int templateVersion) {
    WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE);
    WorkflowVerfiy.verify(
        (StringUtils.isBlank(templateName) || !templateName.endsWith(WorkflowConstants.BPMN_TYPE)
            || templateVersion < 1),
        WorkflowError.INVALID_FILE_FORMAT);
    Optional<TemplateDetails> bpmnTemplateDetails =
        templateDetailsRepository.findByTemplateNameAndVersion(
            FilenameUtils.removeExtension(templateName), templateVersion);
    String definitionId = bpmnTemplateDetails.map(TemplateDetails::getDeployedDefinitionId)
        .orElse(null);
    WorkflowVerfiy.verify(StringUtils.isBlank(definitionId),
        WorkflowError.ONLY_SYSTEM_DEFINITION_IS_SUPPORTED);
    WASHttpResponse<BpmnResponse> bpmnResponse = bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(
        definitionId);
    WorkflowVerfiy.verify(!bpmnResponse.isSuccess2xx(), WorkflowError.TEMPLATE_READ_EXCEPTION);
    WorkflowVerfiy.verify(ObjectUtils.isEmpty(bpmnResponse.getResponse()),
        WorkflowError.EMPTY_BPMN_EXCEPTION);
    String bpmnXml = bpmnResponse.getResponse().getBpmn20Xml();
    WorkflowVerfiy.verify(StringUtils.isBlank(bpmnXml), WorkflowError.EMPTY_BPMN_EXCEPTION);
    response.status(ResponseStatus.SUCCESS)
        .response(new WorkflowTemplateResponse(bpmnXml));
    return response.build();
  }

  @Override
  public TemplateDetails getTemplateByName(final String templateName) {
    final Optional<TemplateDetails> templateDetails =
        templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(
            FilenameUtils.removeExtension(templateName));
    return templateDetails.orElse(null);
  }

  @Override
  public List<TemplateDetails> getAllEnabledTemplatesByName(final String templateName) {
    final Optional<List<TemplateDetails>> templateDetails =
        templateDetailsRepository.findTemplateDetailsExceptTemplateData(
            templateName,Status.ENABLED,ModelType.BPMN);
    return templateDetails.orElse(Collections.emptyList());
  }

  /**
   * Get TemplateDetails entity from templateName
   *
   * @param templateName
   * @param tagVersion
   * @return
   */
  public void checkDuplicateTagVersion(final String templateName, final String tagVersion) {
    final Optional<List<TemplateDetails>> templateDetailsList =
        templateDetailsRepository.findTemplateDetailsExceptTemplateData(
            templateName, Status.ENABLED, ModelType.BPMN);
    if (ObjectUtils.isNotEmpty(templateDetailsList)) {
      final Optional<TemplateDetails> templateDetails =
          templateDetailsList.get().stream()
              .filter(templateTagFilterPredicate(tagVersion))
              .findFirst();
      WorkflowVerfiy.verify(templateDetails.isPresent(), WorkflowError.DUPLICATE_TEMPLATE_AND_TAG);
    }
  }

  private TemplateDetails getTemplateDetailsInstance(final MultipartFile template,
      final ModelType modelType, final TemplateMetadata templateMetadata,
      final TemplateDetails templateDetails, final ModelInstance modelInstance,
      final String tagVersion) throws IOException {

    final int initial_version = 1;
    int version = initial_version;
    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
    final String createdByUser = authorization.getAuthId();

    // Add default offeringId when the appId is missing in the header only in case of dynamic templates
    final String offeringId = (StringUtils.isEmpty(authorization.get(WorkflowConstants.APP_ID)) && ObjectUtils.isEmpty(template))
        ? appConfig.getAppId() : authorization.get(WorkflowConstants.APP_ID);
    final String ownerId = authorization.getRealm();
    final Long createdByUserTemplate = getCreatedByUserTemplate(templateDetails, createdByUser);
    final String templateName = SchemaDecoder.getTemplateName(modelInstance, modelType);
    String templateDescription = null;
    String displayName = null;
    RecordType recordType = null;
    String parentId = null;
    Long modifiedByUserId = null;
    SystemTags templateTag = new SystemTags();
    if (StringUtils.isNotEmpty(tagVersion)) {
      templateTag.addSystemTag(SYSTEM_TAG, tagVersion);
    }

    byte[] templateData = Objects.nonNull(template) ? IoUtil.inputStreamAsByteArray(template.getInputStream()) : null;
    if (modelType.equals(ModelType.BPMN)) {
      version =
          (!ObjectUtils.isEmpty(templateDetails))
              ? (templateDetails.getVersion() + 1)
              : initial_version;
      templateDescription = fetchDescription((BpmnModelInstance) modelInstance);
      recordType = fetchRecordTypeFromBpmn((BpmnModelInstance) modelInstance);
      displayName =
          SchemaDecoder.getTemplateDisplayName(templateName, (BpmnModelInstance) modelInstance);
      modifiedByUserId =
          StringUtils.isNotEmpty(createdByUser) ? Long.parseLong(createdByUser) : null;

    } else if (ModelType.DMN.equals(modelType) && Objects.nonNull(templateDetails)) {
      version = templateDetails.getVersion();
      recordType = templateDetails.getRecordType();
      parentId = templateDetails.getId();
      modifiedByUserId = templateDetails.getModifiedByUserId();
    }
    return TemplateDetails.builder()
        .id(UUID.randomUUID().toString())
        .templateName(templateName)
        .modelType(modelType)
        .createdByUserId(createdByUserTemplate)
        .creatorType(templateMetadata.getCreatorType())
        .offeringId(offeringId)
        .displayName(displayName)
        .status(templateMetadata.getStatus())
        .description(templateDescription)
        .ownerId(ownerId != null ? Long.parseLong(ownerId) : null)
        .version(version)
        .recordType(recordType)
        .modifiedByUserId(modifiedByUserId)
        .parentId(parentId)
        .allowMultipleDefinitions(templateMetadata.isAllowMultipleDefs())
        .templateData(templateData)
        .definitionType(templateMetadata.getDefinitionType())
        .templateCategory(Objects.toString(templateMetadata.getTemplateCategory()))
        .tag(
            templateTag.getTagsMapInstance().get(SYSTEM_TAG) != null
                ? new ObjectMapper().writeValueAsString(templateTag.tagsMapInstance)
                : null)
        .build();
  }

  private Long getCreatedByUserTemplate(TemplateDetails templateDetails, String createdByUser) {
    if (Objects.isNull(templateDetails)) {
      return Long.parseLong(createdByUser);
    }
    return templateDetails.getCreatedByUserId();
  }

  private RecordType fetchRecordTypeFromBpmn(final BpmnModelInstance modelInstance) {
    final Collection<StartEvent> startEvents =
        modelInstance.getModelElementsByType(StartEvent.class);
    for (final StartEvent startEvent : startEvents) {
      final Optional<HandlerDetails> handlerDetails =
          SchemaDecoder.getHandlerDetailsFromExtensionElements(startEvent.getExtensionElements());
      if (handlerDetails.isPresent()) {
        return handlerDetails.get().getRecordType();
      }
    }
    return null;
  }

  private String fetchDescription(final BpmnModelInstance modelInstance) {
    final Optional<Process> templateProcesses =
        modelInstance.getModelElementsByType(Process.class).stream().findFirst();
    if (templateProcesses.isPresent()) {
      final ExtensionElements extensionElements = templateProcesses.get().getExtensionElements();
      if (null != extensionElements) {
        final Collection<CamundaProperty> properties =
            extensionElements
                .getElementsQuery()
                .filterByType(CamundaProperties.class)
                .singleResult()
                .getCamundaProperties();
        for (final CamundaProperty property : properties) {
          if (property.getCamundaName().equalsIgnoreCase(WorkflowConstants.DESCRIPTION)) {
            return property.getCamundaValue();
          }
        }
      }
    }
    return null;
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  public List<TriggerDetails> buildTriggerDetails(
      final BpmnModelInstance modelInstance, final TemplateDetails templateDetails)
      throws ClassNotFoundException {
    final List<TriggerDetails> triggerDetailsList = new ArrayList<>();
    final BPMNTriggerTypeDetails[] bpmnTriggerTypeDetails = BPMNTriggerTypeDetails.values();
    for (final BPMNTriggerTypeDetails bpmnTriggerTypeDetail : bpmnTriggerTypeDetails) {
      final Class bpmnTriggerClass = Class.forName(bpmnTriggerTypeDetail.getBpmnTriggername());
      if (bpmnTriggerTypeDetail.getBpmnTriggerType().equals(BPMNTriggerType.EVENT)) {
        final Collection<Event> bpmnTriggers =
            modelInstance.getModelElementsByType(bpmnTriggerClass);
        for (final Event event : bpmnTriggers) {
          final TriggerType triggerType;
          triggerType = getTriggerType(event);
          final String messageName = getTriggerMessageNameFromEvent(modelInstance, event);
          triggerDetailsList.add(
              getTriggerDetailsInstance(getMessageName(messageName, event),
                  triggerType, ConnectionType.REST,
                  templateDetails, false));
        }
      } else if (bpmnTriggerTypeDetail.getBpmnTriggerType().equals(BPMNTriggerType.TASK)) {
        final Collection<Task> bpmnTriggers =
            modelInstance.getModelElementsByType(bpmnTriggerClass);
        for (final Task task : bpmnTriggers) {
          final String messageName = getTriggerMessageNameFromTask(modelInstance, task);
          // todo: hasLinkedId to be retrieved from xml
          triggerDetailsList.add(
              getTriggerDetailsInstance(
                  messageName, TriggerType.WFTASK, ConnectionType.REST, templateDetails, false));
        }
      } else if (BPMNTriggerType.BOUNDARY_EVENT.equals(
          bpmnTriggerTypeDetail.getBpmnTriggerType())) {
        final Collection<BoundaryEvent> bpmnTriggers =
            modelInstance.getModelElementsByType(bpmnTriggerClass);

        for (final BoundaryEvent boundaryEvent : bpmnTriggers) {
          /* Only message events are expected to have a message reference name that can be used for
           * signalling later. Hence we only save these as trigger events. Other boundary events
           * need not be saved */
          Optional.ofNullable(getTriggerMessageNameFromEvent(modelInstance, boundaryEvent))
              .ifPresent(
                  messageName -> {
                    triggerDetailsList.add(
                        getTriggerDetailsInstance(messageName,
                            TriggerType.BOUNDARYEVENT, ConnectionType.REST,
                            templateDetails, false));
                  });
        }
      }
    }
    return triggerDetailsList;
  }

  private String getMessageName(String messageName, Event event) {
    return StringUtils.isEmpty(messageName)
        ? event.getAttributeValue(WorkflowTemplateConstant.ID.getName())
        : messageName;
  }

  private TriggerType getTriggerType(Event event) {
    if (event instanceof StartEvent) {
      return TriggerType.STARTPROCESS;
    }
    return TriggerType.WFTASK;
  }

  /**
   * * Receive task should have message ref, else throw exception
   *
   * @param modelInstance BPMN model instance
   * @param task          Receive task to be created
   * @return
   */
  private String getTriggerMessageNameFromTask(
      final BpmnModelInstance modelInstance, final Task task) {
    final String messageRefValue = task.getAttributeValue(WorkflowConstants.BPMN_MESSAGE_REF);
    WorkflowVerfiy.verifyNull(messageRefValue, WorkflowError.TEMPLATE_READ_EXCEPTION);
    return getMessageNamefromModelInstance(modelInstance, messageRefValue);
  }

  /**
   * This method checks the boundary event to extract message event nodes. This is because only
   * message events will have a message reference that can be used for correlating the process
   * during a signal. Other boundary events will not have any references, so we return null.
   *
   * @param modelInstance
   * @param event
   * @return
   */
  private String getTriggerMessageNameFromEvent(
      final BpmnModelInstance modelInstance, final Event event) {
    final Collection<MessageEventDefinition> messageEventNodes =
        event.getChildElementsByType(MessageEventDefinition.class);
    if (CollectionUtils.isEmpty(messageEventNodes)) {
      logWarn(event);
      return null;
    }
    final String messageRefValue =
        messageEventNodes.stream()
            .findFirst()
            .orElseThrow(
                () ->
                    new WorkflowGeneralException(WorkflowError.MESSAGE_EVENT_DEFINITION_NOT_FOUND))
            .getAttributeValue(WorkflowConstants.BPMN_MESSAGE_REF);
    if (StringUtils.isEmpty(messageRefValue)) {
      return messageRefValue;
    }
    return getMessageNamefromModelInstance(modelInstance, messageRefValue);
  }

  /**
   * @param modelInstance
   * @param messageRefValue
   * @return
   */
  private String getMessageNamefromModelInstance(
      final BpmnModelInstance modelInstance, final String messageRefValue) {
    final ModelElementInstance modelElementInstance =
        modelInstance.getModelElementById(messageRefValue);
    WorkflowVerfiy.verifyNull(modelElementInstance, WorkflowError.TEMPLATE_READ_EXCEPTION);
    return modelElementInstance.getAttributeValue(WorkflowConstants.BPMN_MESSAGE_NAME);
  }

  private TriggerDetails getTriggerDetailsInstance(final String triggerName,
      final TriggerType triggerType, final ConnectionType connectionType,
      final TemplateDetails templateDetails, final boolean hasLinkedId) {
    return TriggerDetails.builder()
        .id(UUID.randomUUID().toString())
        .triggerName(triggerName)
        .triggerType(triggerType)
        .connectionType(connectionType)
        .templateDetails(templateDetails)
        .hasLinkedId(hasLinkedId)
        .recordType(templateDetails.getRecordType())
        .build();
  }

  @SuppressWarnings({"unchecked", "rawtypes"})
  @Trace
  @Override
  @Metric(name = MetricName.READ_ONE_TEMPLATE, type = Type.API_METRIC)
  public Template fetchTemplate(
      @Tracer(key = WASContextEnums.TEMPLATE_ID) final String name, final GlobalId globalId,
      boolean isMultiStep)
      throws IOException {
    // Try to get template from the config. If found get it and return from here.
    Template template = getConfigTemplateById(name, isMultiStep);
    // Add variability check
    variabilityEngineService.filterTemplate(template);

    if (ObjectUtils.isNotEmpty(template)) {
      return template;
    }

    final TemplateDetails templateDetails =
        templateDetailsRepository
            .findTopByIdOrderByVersionDesc(name)
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.TEMPLATE_DOES_NOT_EXIST));
    final List<TemplateDetails> dmnList;
    final BpmnModelInstance bpmnModelInstance;
    final List<DmnModelInstance> dmnModelInstances = new ArrayList<>();
    bpmnModelInstance =
        Bpmn.readModelFromStream(new ByteArrayInputStream(templateDetails.getTemplateData()));
    final Optional<List<TemplateDetails>> optionalDmns =
        templateDetailsRepository.getTemplateDetailsByParentIdAndVersion(
            templateDetails.getId(), templateDetails.getVersion());
    if (optionalDmns.isPresent()) {
      dmnList = optionalDmns.get();
      dmnList.forEach(
          dmn ->
              dmnModelInstances.add(
                  Dmn.readModelFromStream(new ByteArrayInputStream(dmn.getTemplateData()))));
    }

    // generate DefinitionInstance object from bpmn and dmn model instances
    DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
        bpmnModelInstance, dmnModelInstances, templateDetails);

    return (Template)
        bpmnProcessor.processBpmn(definitionInstance,
            globalId, false);
  }

  @Override
  public List<Template> fetchAllTemplates(final QueryHelper query) {
    final Map<Boolean, List<TemplateDetails>> mapOfTemplates = getMapOfTemplates();
    List<TemplateDetails> bpmnFiles = mapOfTemplates.get(true);
    // Handling query filters for template name
    final FilterExpression filterExpression = (FilterExpression) FilterUtil.getQueryFilterExpression(
        query);
    final boolean hasFilter = ObjectUtils.isNotEmpty(filterExpression);
    if (hasFilter) {
      final TemplateFilter templateFilter = TemplateFilters.get(filterExpression.getProperty());
      if (Objects.nonNull(templateFilter)) {
        bpmnFiles = templateFilter.filter(bpmnFiles, filterExpression);
      }
    }
    return mergeTemplatesFromConfig(
        query,
        prepareTemplateResponse(bpmnFiles, contextHandler.get(WASContextEnums.OWNER_ID)));
  }

  /**
   * @param bpmnFiles : Template Details list of bpmns
   * @param realmId   : RealmID
   * @return : Prepare Template v4 Record
   */
  private List<Template> prepareTemplateResponse(
      final List<TemplateDetails> bpmnFiles, final String realmId) {
    WorkflowVerfiy.verify(CollectionUtils.isEmpty(bpmnFiles), WorkflowError.TEMPLATE_NOT_FOUND);
    final List<Template> templates = new ArrayList<>();
    // TODO: Add Alpha check here for filtering
    bpmnFiles.forEach(
        bpmnTemplate -> {
          final Template template = new Template();
          final GlobalId<?> globalId =
              GlobalIdV41.builder()
                  .setRealmId(realmId)
                  .setTypeId(TEMPLATE_TYPE_ID)
                  .setLocalId(bpmnTemplate.getId())
                  .build();
          template.setId(globalId);
          template.setDescription(
              translationService.getString(bpmnTemplate.getDescription(), getLocale()));
          template.setDisplayName(
              translationService.getString(bpmnTemplate.getDisplayName(), getLocale()));
          template.setName(bpmnTemplate.getTemplateName());
          template.setCategory(bpmnTemplate.getTemplateCategory());
          template.setVersion(String.valueOf(bpmnTemplate.getVersion()));
          template.setAllowMultipleDefinitions(bpmnTemplate.getAllowMultipleDefinitions());
          templates.add(template);
        });
    return templates;
  }

  @Override
  public WorkflowGenericResponse updateTemplateStatus(final String templateName, final String templateStatus) {
    final WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE);

    final TemplateDetails bpmnTemplate = getTemplateByName(templateName);
    WorkflowVerfiy.verifyNull(bpmnTemplate, WorkflowError.BPMN_DOESNT_EXIST);
    final Status status = Status.lookupStatus(templateStatus);
    final Authorization authorization =
        new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
    templateDetailsRepository.updateTemplateDetailsStatus(
        bpmnTemplate.getId(), status, Long.parseLong(authorization.getAuthId()));
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("updateTemplateStatus")
                .message("%s got %s", templateName, templateStatus)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));
    response
        .status(ResponseStatus.SUCCESS)
        .response(
            new WorkflowTemplateResponse(
                String.format("%s got %s successfully.", templateName, templateStatus)));
    return response.build();
  }

  @SuppressWarnings("unchecked")
  @Override
  public List<Template> fetchAllTemplatesWithWorkflowSteps(final QueryHelper query)
      throws IOException {
    final Map<Boolean, List<TemplateDetails>> mapOfTemplates = getMapOfTemplates();
    List<TemplateDetails> bpmnFiles = mapOfTemplates.get(true);
    List<TemplateDetails> dmnFiles = mapOfTemplates.get(false);
    // Todo: Need to refactor for other filter types
    // Handling query filters for template name
    final FilterExpression filterExpression = (FilterExpression) FilterUtil.getQueryFilterExpression(
        query);
    final boolean hasFilter = ObjectUtils.isNotEmpty(filterExpression);
    if (hasFilter) {
      final TemplateFilter templateFilter = TemplateFilters.get(filterExpression.getProperty());
      if (Objects.nonNull(templateFilter)) {
        bpmnFiles = templateFilter.filter(bpmnFiles, filterExpression);
      }
    }
    final Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
    bpmnFiles.forEach(
        bpmnTemplate -> {
          final List<TemplateDetails> dmnTemplateFiles =
              dmnFiles.stream()
                  .filter(
                      dmnTemplate ->
                          bpmnTemplate.getId().equalsIgnoreCase(dmnTemplate.getParentId()))
                  .collect(Collectors.toList());
          map.put(bpmnTemplate, dmnTemplateFiles);
        });

    return bpmnProcessor.processBpmn(map);
  }

  @Override
  @Metric(name = MetricName.READ_ALL_TEMPLATES, type = Type.API_METRIC)
  public ListResult<Template> readAllTemplates(
      final Authorization authorization, final QueryHelper query) throws IOException {
    authDetailsServiceHelper.populateAuthDetails(authorization);
    final boolean isWorkflowSteps = providerHelper.checkForWorkflowSteps(query);

    List<Template> templateList;
    if (isWorkflowSteps) {
      templateList = fetchAllTemplatesWithWorkflowSteps(query);
      providerHelper.populateRealmInGlobalId(templateList, authorization.getRealm());
    } else {
      templateList = fetchAllTemplates(query);
    }
    templateLabelsService.fill(templateList);
    return new ListResult<>(templateList);
  }

  private Map<Boolean, List<TemplateDetails>> getMapOfTemplates() {
    List<TemplateDetails> templateList = templateDetailsRepository.getAllRecords();
    WorkflowVerfiy.verify(
        CollectionUtils.isEmpty(templateList), WorkflowError.TEMPLATE_DOES_NOT_EXIST);

    templateList = filterOutSystemDefTemplates(templateList);
    return templateList.stream()
        .collect(Collectors.partitioningBy(t -> ObjectUtils.isEmpty(t.getParentId())));
  }

  private List<TemplateDetails> filterOutSystemDefTemplates(
      List<TemplateDetails> templateListResult) {
    return templateListResult.stream()
        .filter(template -> !DefinitionType.SYSTEM.equals(template.getDefinitionType()))
        .collect(Collectors.toList());
  }

  /**
   * Process the templates and save it in DB
   *
   * @param templates        list of template files
   * @param templateMetadata {@link TemplateMetadata}
   * @param isUpdate         if it is create or update
   * @return {@link WorkflowGenericResponse}
   */
  private WorkflowGenericResponse processTemplates(final MultipartFile[] templates,
      final TemplateMetadata templateMetadata, final boolean isUpdate) {
    final WorkflowGenericResponseBuilder response =
        WorkflowGenericResponse.builder().status(ResponseStatus.FAILURE);
    try {
      checkAccessForAPI();
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("processTemplates")
                  .message("Saving BPMN template")
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));

      validateTemplate(templates, templateMetadata);
      final MultipartFile bpmnfile =
          Arrays.stream(templates)
              .filter(
                  template -> template.getOriginalFilename().endsWith(WorkflowConstants.BPMN_TYPE))
              .collect(onlyElement());
      final BpmnModelInstance bpmnModelInstance =
          Bpmn.readModelFromStream(bpmnfile.getInputStream());

      final String bpmnTemplateName =
          SchemaDecoder.getTemplateName(bpmnModelInstance, ModelType.BPMN);
      final String bpmnTemplateTagVersion =
          SchemaDecoder.getAndValidateTemplateTagVersion(bpmnModelInstance);

      addTemplateNameAndTagVersionInContext(bpmnTemplateName, bpmnTemplateTagVersion);

      templateValidator.validate(bpmnModelInstance, templateMetadata);

      final TemplateDetails bpmnTemplate = getTemplateByName(bpmnTemplateName);
      verifyTemplateDetails(bpmnTemplate, templateMetadata, isUpdate);
      handleTemplateTags(bpmnTemplateName, bpmnTemplateTagVersion, isUpdate, bpmnTemplate,
          templateMetadata);

      // Create TemplateDetails instance to save
      final TemplateDetails bpmnTemplateDetails = getTemplateDetailsInstance(
              bpmnfile, ModelType.BPMN, templateMetadata, bpmnTemplate, bpmnModelInstance,
              bpmnTemplateTagVersion);

      validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails);

      // getting extra details from camunda instance
      List<ActivityDetail> activityDetails = populateActivityDetailsFromCamundaInstance(bpmnModelInstance);

      final List<String> savedFileNames = new ArrayList<>();
      savedFileNames.add(bpmnfile.getOriginalFilename());

      // Saving DMNs
      final List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
      final List<TemplateDetails> dmnTemplateDetailsList = new ArrayList<>();
      for (final MultipartFile template : templates) {
        final String templateName = template.getOriginalFilename();
        if (templateName.endsWith(WorkflowConstants.DMN_TYPE)) {
          DmnProcessorUtil.validateDMN(template.getInputStream());
          final DmnModelInstance dmnModelInstance =
              Dmn.readModelFromStream(template.getInputStream());
          dmnModelInstanceList.add(dmnModelInstance);
          dmnTemplateDetailsList.add(
              getTemplateDetailsInstance(template, ModelType.DMN,
                  templateMetadata, bpmnTemplateDetails,
                  dmnModelInstance, bpmnTemplateTagVersion));
          savedFileNames.add(templateName);
        }
      }
      deployToCamundaAndSaveTemplateDetailsInDatabase(bpmnModelInstance, dmnModelInstanceList,
              templateMetadata, bpmnTemplateDetails, dmnTemplateDetailsList, activityDetails, isUpdate);
      // save bpmn, dmn and trigger details after
      handleTemplateForOverwatchUseCase(bpmnfile, bpmnModelInstance, dmnModelInstanceList, bpmnTemplateDetails,
      bpmnTemplate, dmnTemplateDetailsList, isUpdate, templateMetadata);
      response
          .status(ResponseStatus.SUCCESS)
          .response(new WorkflowTemplateResponse(String.join(", ", savedFileNames)));
    } catch (final WorkflowGeneralException wge) {
      throw wge;
    } catch (final Exception e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message(WorkflowError.TEMPLATE_SAVE_EXCEPTION.getErrorDescription())
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.TEMPLATE_DETAILS)
                  .className(this.getClass().getSimpleName()));
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_SAVE_EXCEPTION);
    }
    return response.build();
  }


  /**
   * This function forks the original template and deploys in camunda and stores in DB for Overwatch purposes.
   * In the context of automations for approvals and reminders, significant delays in the task queue have been observed,
   * causing the automation tests to exceed the expected completion time of 15-20 minutes.
   * To mitigate this issue, the template can be forked with a new name and topic details specific to Overwatch,
   * thus avoiding reliance on the main BPMN topics and instead using the Overwatch topics specified in the configuration.
   * <p>
   * Use case: We call this function only for automations in approvals & reminders use case for callActivities BPMNs.
   * We have a custom logic in trigger API flow where we change the deployed camunda template id whenever we pass scope as Scope.TEST.
   * Also along with overriding topics, we need to override callActivities with overwatch callActivities.
   * <p>
   * This property helps us in creating overwatch templates accordingly with different topic configuration.
   * By enabling this property we deploy 2 templates. One for original business flow and another for automation.
   * The trigger flows will manage replacing the camunda template accordingly.
   */
  private void handleTemplateForOverwatchUseCase(MultipartFile bpmnfile, BpmnModelInstance bpmnModelInstance,
  List<DmnModelInstance> dmnModelInstanceList, TemplateDetails bpmnTemplateDetails, TemplateDetails previousTemplateDetails,
  List<TemplateDetails> dmnTemplateDetailsList, boolean isUpdate, TemplateMetadata templateMetadata) throws ClassNotFoundException, IOException {
    // for callActivity in reminder/approval flows, update the bpmn model instance for callActivities.
    // Enable forkForOverwatch to true only for callActivity template creation.
    if (templateMetadata.isForkForOverwatch()) {
      BpmnModelInstance overwatchBpmnModelInstance = BpmnOverwatchUtil.updateBpmnModelInstanceForOverwatchTests(bpmnModelInstance);
      final TemplateDetails overwatchBpmnTemplateDetails = getTemplateDetailsInstance(bpmnfile
              , ModelType.BPMN, templateMetadata, previousTemplateDetails, overwatchBpmnModelInstance,
              bpmnTemplateDetails.getTag());
      overwatchBpmnTemplateDetails.setTemplateName(bpmnTemplateDetails.getTemplateName() + BpmnOverwatchUtil.OVERWATCH_SUFFIX);
      File file = CamundaServiceHelper.createBpmnFile(overwatchBpmnModelInstance);
      byte[] templateData = IoUtil.inputStreamAsByteArray(new FileInputStream(file));
      overwatchBpmnTemplateDetails.setTemplateData(templateData);
      List<ActivityDetail> overWatchActivityDetails = populateActivityDetailsFromCamundaInstance(overwatchBpmnModelInstance);
      deployToCamundaAndSaveTemplateDetailsInDatabase(overwatchBpmnModelInstance, dmnModelInstanceList,
              templateMetadata, overwatchBpmnTemplateDetails, dmnTemplateDetailsList, overWatchActivityDetails, isUpdate);
    }
  }

  private void addTemplateNameAndTagVersionInContext(final String bpmnTemplateName, final String bpmnTemplateTagVersion) {
    // adding workflow name in context
    contextHandler.addKey(WASContextEnums.WORKFLOW, bpmnTemplateName);
    contextHandler.addKey(WASContextEnums.TAGS_VERSION, bpmnTemplateTagVersion);
  }

  private State deployToCamundaAndSaveTemplateDetailsInDatabase(
                final BpmnModelInstance bpmnModelInstance,
                final List<DmnModelInstance> dmnModelInstanceList,
                final TemplateMetadata templateMetadata,
                final TemplateDetails bpmnTemplateDetails,
                final List<TemplateDetails> dmnTemplateDetailsList,
                final List<ActivityDetail> activityDetails,
                final boolean isUpdate) throws ClassNotFoundException {

    final List<TriggerDetails> triggerDetailsList =
            buildTriggerDetails(bpmnModelInstance, bpmnTemplateDetails);

    validateTriggerNames(triggerDetailsList, templateMetadata);

    TemplateModelInstance templateModelInstance =
            new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, isUpdate);

    // deploy and save template in DB
    return executeTasks(templateMetadata, templateModelInstance,
            bpmnTemplateDetails, dmnTemplateDetailsList,
            triggerDetailsList, activityDetails);
  }

  private void checkAccessForAPI() {
    if (WASContext.getApplicationId().isEmpty()) {
      throw new WorkflowGeneralException(WorkflowError.MISSING_APP_ID);
    }
    if (clientAccessConfig.getTemplateSaveUpdateConfig().isBlock()) {
      throw new WorkflowGeneralException(WorkflowError.API_ACCESS_BLOCKED);
    }
    if (!clientAccessConfig.getTemplateSaveUpdateConfig().getWhitelistAppId().isEmpty()
        && clientAccessConfig.getTemplateSaveUpdateConfig().getWhitelistAppId().stream()
        .noneMatch(WASContext.getApplicationId().get()::contains)) {
      throw new WorkflowGeneralException(WorkflowError.ACCESS_DENIED_FOR_CLIENT_APP_ID);
    }
  }

  private List<ActivityDetail> populateActivityDetailsFromCamundaInstance(BpmnModelInstance bpmnModelInstance){
    Map<String ,ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
    if (bpmnParserConfig.isEnable()) {
      WorkflowTaskProcessor.processBPMNElementsForServiceTask(bpmnModelInstance, activityIdToActivityDetailsMap);
    }
    // Separate handling for Domain Events
    if (domainEventTopiConfig.isEnabled()) {
      WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
    }
    WorkflowTaskProcessor.processInitialStartEventElement(bpmnModelInstance, activityIdToActivityDetailsMap);

    return new ArrayList<>(activityIdToActivityDetailsMap.values());
  }

  /**
   * Executes tasks for Save/Update template
   *
   * @param templateMetadata       {@link TemplateMetadata}
   * @param bpmnTemplateDetails    {@link TemplateDetails} of Bpmn type
   * @param dmnTemplateDetailsList list of {@link TemplateDetails}, type DMN
   * @param triggerDetailsList     list of {@link TriggerDetails}
   * @param triggerDetailsList     list of {@link ActivityDetail}
   */
  private State executeTasks(final TemplateMetadata templateMetadata, final TemplateModelInstance templateModelInstance,
      final TemplateDetails bpmnTemplateDetails, final List<TemplateDetails> dmnTemplateDetailsList,
      final List<TriggerDetails> triggerDetailsList, final List<ActivityDetail> activityDetails) {

    final State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetails);
    inputRequest.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetailsList);
    if (!CollectionUtils.isEmpty(activityDetails)) {
      inputRequest
          .addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
    }
    inputRequest.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, triggerDetailsList);
    inputRequest.addValue(WorkflowConstants.INTUIT_TID,
        contextHandler.get(WASContextEnums.INTUIT_TID));

    // Execute Trigger call based on definition type
    TemplateProcessor processor = TemplateHandler.getHandler(templateMetadata.getDefinitionType());
    return processor.execute(inputRequest, templateModelInstance);
  }

  private void verifyTemplateDetails(final TemplateDetails bpmnTemplate,
      final TemplateMetadata templateMetadata, final boolean isUpdate) {
    if (!isUpdate) {
      // in case of Save, template bpmnTemplate would be null
      WorkflowVerfiy.verify(
          ObjectUtils.isNotEmpty(bpmnTemplate), WorkflowError.TEMPLATE_ALREADY_EXISTS);
    } else {
      WorkflowVerfiy.verify(
          ObjectUtils.isEmpty(bpmnTemplate), WorkflowError.TEMPLATE_DOES_NOT_EXIST);
      final DefinitionType oldDefinitionType =
          Objects.isNull(bpmnTemplate.getDefinitionType())
              ? DefinitionType.USER
              : bpmnTemplate.getDefinitionType();
      WorkflowVerfiy.verify(
          !canUpdate(templateMetadata, oldDefinitionType),
          WorkflowError.DEFINITION_TYPE_UPDATE_ERROR);
    }
  }

  public void handleTemplateTags(final String templateName,
      final String tagVersion, boolean isUpdate,
      TemplateDetails bpmnTemplate, TemplateMetadata templateMetadata) {
    WorkflowVerfiy.verify(
        (StringUtils.isNotEmpty(tagVersion)
            && templateMetadata.getDefinitionType() != DefinitionType.SYSTEM),
        WorkflowError.TEMPLATE_TAG_NOT_SUPPORTED);
    if (isUpdate) {
      if (StringUtils.isNotEmpty(tagVersion)) {
        checkDuplicateTagVersion(templateName, tagVersion);
      } else {
        checkAndThrowExceptionIfTagRemoved(templateName, bpmnTemplate);
      }
    }
  }

  /**
   * @param templateName Throws an error if the user tries to remove the template tag version
   * @param bpmnTemplate
   */
  private void checkAndThrowExceptionIfTagRemoved(
      String templateName, TemplateDetails bpmnTemplate) {
    if (Objects.nonNull(bpmnTemplate)) {
      WorkflowVerfiy.verify(
          Objects.nonNull(bpmnTemplate.getTag()),
          WorkflowError.UPDATE_FAILED_DUE_TO_TAG_VERSION_REMOVAL);
    }
  }

  /**
   * @param templateMetadata
   * @param oldDefinitionType
   * @return Update of BPMN should be allowed when Migrating Existing User Defn to Single Definition
   * or Rollback Single Defn to User defn
   */
  private boolean canUpdate(TemplateMetadata templateMetadata, DefinitionType oldDefinitionType) {
    if (templateMetadata.getDefinitionType().equals(oldDefinitionType)) {
      return true;
    }
//  RollBack use case
    if (oldDefinitionType == DefinitionType.SINGLE
        && templateMetadata.getDefinitionType() == DefinitionType.USER) {
      return true;
    }
//  Migrate Existing Definition to Single
    if (oldDefinitionType == DefinitionType.USER
        && templateMetadata.getDefinitionType() == DefinitionType.SINGLE) {
      return true;
    }
    return false;
  }

  private void validateTriggerNames(
      List<TriggerDetails> triggerDetailsList, TemplateMetadata templateMetadata) {
    if (templateMetadata.isValidateTriggerNames()) {
      BpmnProcessorUtil.validateDuplicateTriggerNames(triggerDetailsList);
    }
  }

  private void logWarn(final Event event) {
    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message("Message event name is not present for ID=%s", event.getId())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));
  }

  /**
   * This method remove the db template whose name is equal to id of templates in configuration
   * map.
   *
   * @param query     : {@link QueryHelper}
   * @param templates : {@link List<Template>} List of Templates from Database
   * @return : Combined List
   */
  private List<Template> mergeTemplatesFromConfig(QueryHelper query, List<Template> templates) {

    final FilterExpression filterExpression = (FilterExpression) FilterUtil.getQueryFilterExpression(
        query);

    if (ObjectUtils.isNotEmpty(filterExpression)
        && filterExpression.getArgs().contains(TemplateCategory.CUSTOM.name())) {
      templates = filterConfigTemplatesFromDbTemplates(getConfigTemplates(), templates);
    }
    
    // apply variability rules and filter templates/attributes
    variabilityEngineService.filterTemplates(templates);

    return templates;
  }

  private String getLocale() {
    return contextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE);
  }

  /**
   * This method is used to validate the call activity properties across children and parent Bpmns
   *
   * @param bpmnModelInstance   BpmnModelInstance
   * @param bpmnTemplateDetails TemplateDetails
   * @throws ClassNotFoundException
   * <AUTHOR>
   */
  public void validateCallActivityElements(BpmnModelInstance bpmnModelInstance,
      TemplateDetails bpmnTemplateDetails) throws ClassNotFoundException {
    try {

      Collection<CallActivity> callActivities = bpmnModelInstance.getModelElementsByType(CallActivity.class);
      WorkflowLogger.logInfo("List of CallActivities present in the bpmn= %s",
          callActivities.stream().map(CallActivity::getCalledElement).collect(Collectors.toList()));
      if (CollectionUtils.isEmpty(callActivities)) return;

      // Signal Message names cannot be duplicated inside the parent Bpmn.
      final List<TriggerDetails> triggerDetailsList =
          buildTriggerDetails(bpmnModelInstance, bpmnTemplateDetails);

      final List<TriggerDetails> filteredTriggerDetails = triggerDetailsList.stream().filter(triggerDetail ->
          !triggerDetail.getTriggerType().equals(TriggerType.STARTPROCESS)).collect(Collectors.toList());

      Set<String> distinctCalledElements =
          callActivities.stream().map(CallActivity::getCalledElement).collect(Collectors.toSet());

      for (String calledElementName : distinctCalledElements) {

        Optional<TemplateDetails> optionalCalledElementTemplateDetails =
            templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(
                calledElementName, Status.ENABLED);

        // if no such template is present in database, then parent bpmn should not be saved.
        if (optionalCalledElementTemplateDetails.isEmpty()) {
          WorkflowLogger.logError("Call Activity template details not found for calledElement= %s", calledElementName);
          throw new WorkflowGeneralException(WorkflowError.CALL_ACTIVITY_TEMPLATE_NOT_FOUND);
        }

        // Signal Message names cannot be duplicated across parent and child Bpmns.
        TemplateDetails calledElementTemplateDetails = optionalCalledElementTemplateDetails.get();
        BpmnModelInstance childBpmnModelInstance = BpmnProcessorUtil.readBPMN(
            calledElementTemplateDetails.getTemplateData());

        final List<TriggerDetails> childTemplateTriggerDetails =
            buildTriggerDetails(childBpmnModelInstance, calledElementTemplateDetails);

        validateDuplicateTriggerNamesInChildTemplate(filteredTriggerDetails,
            childTemplateTriggerDetails, bpmnTemplateDetails.getTemplateName(), calledElementName);
      }

      for (CallActivity callActivity : callActivities) {

        // called element binding should be latest - by default it is null, which is equivalent to latest
        if (Objects.nonNull(callActivity.getCamundaCalledElementBinding()) &&
            !WorkflowConstants.CALLED_ELEMENT_LATEST_BINDING.equals(
                callActivity.getCamundaCalledElementBinding())) {
          WorkflowLogger.logError("Invalid called element binding found= %s", callActivity.getCamundaCalledElementBinding());
          throw new WorkflowGeneralException(WorkflowError.INVALID_CALLED_ELEMENT_BINDING);
        }

        // Call activities should have rootProcessInstanceId in the inputs.
        CamundaInputOutputImpl camundaInputOutput =
            (CamundaInputOutputImpl) callActivity.getExtensionElements().getElements().stream()
                .filter(
                    CamundaInputOutputImpl.class::isInstance).findFirst().orElseThrow(
                    () -> new WorkflowGeneralException(
                        WorkflowError.EMPTY_INPUT_OUTPUT_IN_CALL_ACTIVITY));

        Optional<CamundaInputParameter> camundaInputParameterOptional = camundaInputOutput.getCamundaInputParameters()
            .stream().filter(
                camundaInput -> camundaInput.getCamundaName()
                    .equals(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID)).findFirst();
        if (camundaInputParameterOptional.isEmpty()) {
          WorkflowLogger.logError(
              "Process variable rootProcessInstanceId is not found in activityId= %s", callActivity.getCalledElement());
          throw new WorkflowGeneralException(
              WorkflowError.CALL_ACTIVITY_ROOT_PROCESS_INSTANCE_ID_NOT_FOUND);
        }
      }

    } catch (Exception ex) {
      WorkflowLogger.logError(
          "Exception encountered while validating call activity elements. Error details= %s", ex);
      throw ex;
    }
  }

  /**
   * This method validates that parent and child called activity bpmn do not have duplicate trigger
   * signal names. Trigger details are fetched for all types of BPMNTriggerType events
   *
   * @param triggerDetails List<TriggerDetails>
   * @param childTemplateTriggerDetails List<TriggerDetails>
   * @param parentBpmnTemplateName String
   * @param childActivityId String
   */
  private void validateDuplicateTriggerNamesInChildTemplate(List<TriggerDetails> triggerDetails,
      List<TriggerDetails> childTemplateTriggerDetails, String parentBpmnTemplateName,
      String childActivityId) {
    Set<String> distinctTriggersSet =
        triggerDetails.stream()
            .map(details -> details.getTriggerName().toLowerCase())
            .collect(Collectors.toSet());

    String duplicateName = null;

    for (TriggerDetails childDetails : childTemplateTriggerDetails) {
      if (distinctTriggersSet.contains(childDetails.getTriggerName())) {
        duplicateName = childDetails.getTriggerName();
        break;
      }
    }

    if (Objects.nonNull(duplicateName)) {
      WorkflowLogger.logError(
          "Parent Bpmn= %s and child called activity= %s contains duplicate trigger name= %s",
          parentBpmnTemplateName, childActivityId, duplicateName);
      throw new WorkflowGeneralException(
          WorkflowError.PARENT_TEMPLATE_AND_CHILD_CALL_ACTIVITY_CONTAINS_DUPLICATE_TRIGGER_NAMES);
    }
  }

  /**
   *  This method returns the template details for a given TemplateAdjacencyValuesMd5Sum,
   *  If templateDetails is found empty then create a new record in templateDetails table
   * @param bpmnModelInstance BpmnModelInstance
   * @param dmnModelInstanceList List<DmnModelInstance>
   * @param md5Sum String
   * @return TemplateDetails
   */
  public TemplateDetails getOrSaveTemplateDetailsByHashValue(BpmnModelInstance bpmnModelInstance,
                                                             List<DmnModelInstance> dmnModelInstanceList,
                                                             String md5Sum){

    WorkflowLogger.logInfo("step=getOrSaveTemplateDetailsByHashValue status=fetching templateDetails");

    TemplateDetails templateDetails = getTemplateByTemplateAdjacencyValuesMd5Sum(md5Sum);

    if (Objects.isNull(templateDetails)) {
      List<TemplateDetails> savedTemplateDetailsList =
              processTemplatesForDynamicBpmn(bpmnModelInstance, dmnModelInstanceList, md5Sum, false);

      // savedTemplateDetailsList will have one row for BPMN with non-null md5Sum and multiple rows for DMN with null md5Sum
      return savedTemplateDetailsList.stream().filter(
              // filter templateDetails with non-null Md5Sum to find the BPMN templateDetails
              details -> Objects.nonNull(details.getTemplateAdjacencyValuesMd5Sum()))
              .findFirst().orElseThrow(() -> new WorkflowGeneralException(
                      WorkflowError.TEMPLATE_SAVE_EXCEPTION));

    }
    return templateDetails;
  }

  private TemplateDetails getTemplateByTemplateAdjacencyValuesMd5Sum(final String md5Sum) {
    Optional<TemplateDetails> optionalTemplateDetails =
            templateDetailsRepository.findByTemplateAdjacencyValuesMd5Sum(md5Sum);
    return optionalTemplateDetails.orElse(null);
  }

  private List<TemplateDetails> processTemplatesForDynamicBpmn(BpmnModelInstance bpmnModelInstance,
                                              List<DmnModelInstance> dmnModelInstanceList,
                                              String md5Sum, boolean isUpdate) {

    TemplateMetadata templateMetadata = prepareTemplateMetaDataForDynamicTemplates(md5Sum);

    try {

    WorkflowLogger.logInfo("methodName=processTemplatesForDynamicBpmn, TemplateDetails not found for hashSum=%s " +
                    "Saving templateDetails in DB and deploying in camunda", md5Sum);

    validateBpmnAndDmnTemplateModels(bpmnModelInstance, dmnModelInstanceList, templateMetadata);

    final String bpmnTemplateName =
            SchemaDecoder.getTemplateName(bpmnModelInstance, ModelType.BPMN);
    final String bpmnTemplateTagVersion =
            SchemaDecoder.getAndValidateTemplateTagVersion(bpmnModelInstance);

    addTemplateNameAndTagVersionInContext(bpmnTemplateName, bpmnTemplateTagVersion);

    templateValidator.validate(bpmnModelInstance, templateMetadata);

    handleTemplateTags(bpmnTemplateName, bpmnTemplateTagVersion, isUpdate, null, templateMetadata);

    // Create TemplateDetails instance to save
    final TemplateDetails bpmnTemplateDetails = getTemplateDetailsInstance(null, ModelType.BPMN,
        templateMetadata, null, bpmnModelInstance, bpmnTemplateTagVersion);
    DynamicBpmnWasCamundaTransformer.transformTemplateDetailsValuesForBpmn(
            templateMetadata, bpmnModelInstance, bpmnTemplateDetails);

    validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails);

    // getting extra details from camunda instance
    List<ActivityDetail> activityDetails = populateActivityDetailsFromCamundaInstance(bpmnModelInstance);

    final List<TemplateDetails> dmnTemplateDetailsList = new ArrayList<>();
    for (DmnModelInstance dmnModelInstance : dmnModelInstanceList) {
      TemplateDetails dmnTemplateDetails = getTemplateDetailsInstance(null, ModelType.DMN,
                      templateMetadata, bpmnTemplateDetails,
                      dmnModelInstance, bpmnTemplateTagVersion);

      DynamicBpmnWasCamundaTransformer.transformTemplateDetailsValuesForDmn(
              templateMetadata, dmnModelInstance, dmnTemplateDetails);
      dmnTemplateDetailsList.add(dmnTemplateDetails);
    }

    State state = deployToCamundaAndSaveTemplateDetailsInDatabase(bpmnModelInstance, dmnModelInstanceList,
            templateMetadata, bpmnTemplateDetails, dmnTemplateDetailsList, activityDetails, isUpdate);
    List<TemplateDetails> templateDetailsList = state.getValue(AsyncTaskConstants.SAVE_TEMPLATE_DETAILS_LIST_KEY);
    return templateDetailsList;

    } catch (final WorkflowGeneralException wge) {
      throw wge;
    } catch (final Exception e) {
      WorkflowLogger.logError(e,
              "WorkflowError.TEMPLATE_SAVE_EXCEPTION.getErrorDescription() " +
              "downstreamComponentName=%s downstreamServiceName=%s className=%s",
              DownstreamComponentName.WAS_DB, DownstreamServiceName.TEMPLATE_DETAILS,
              this.getClass().getSimpleName());
      throw new WorkflowGeneralException(WorkflowError.TEMPLATE_SAVE_EXCEPTION);
    }
  }

  private TemplateMetadata prepareTemplateMetaDataForDynamicTemplates(final String hashSum){
    TemplateMetadata templateMetadata = new TemplateMetadata();
    templateMetadata.setAllowMultipleDefs(true);
    templateMetadata.setDefinitionType(DefinitionType.SINGLE);
    templateMetadata.setTemplateCategory(TemplateCategory.CUSTOM);
    templateMetadata.setTemplateAdjacencyValuesMd5Sum(hashSum);
    templateMetadata.setTemplateCreatedDynamically(true);
    return templateMetadata;
  }
}