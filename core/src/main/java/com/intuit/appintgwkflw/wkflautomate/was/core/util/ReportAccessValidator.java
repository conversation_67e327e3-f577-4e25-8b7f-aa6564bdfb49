package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ReportAccessValidationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Service to validate report access for workflow automation to prevent security vulnerabilities
 * where users can bypass UI controls to access unauthorized reports.
 * 
 * <AUTHOR> Team
 */
@Component
@AllArgsConstructor
public class ReportAccessValidator {

    private final AccessVerifier accessVerifier;
    private final WASContextHandler contextHandler;
    private final IXPManager ixpManager;
    private final ReportAccessValidationConfig config;
    
    // Pattern to extract report ID from conditional expressions like "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2"
    private static final Pattern REPORT_ID_PATTERN = Pattern.compile("sbg:([a-f0-9-]{36})");
    
    /**
     * Validates that the current user has access to the specified report ID.
     * This prevents users from bypassing UI dropdown controls to access unauthorized reports.
     * 
     * @param reportId The report ID to validate (format: sbg:uuid)
     * @throws WorkflowGeneralException if user doesn't have access to the report
     */
    public void validateReportAccess(String reportId) {
        // Check if validation is enabled via feature flag
        if (!isReportAccessValidationEnabled()) {
            WorkflowLogger.logInfo("step=validateReportAccess, reportId=%s, validation=disabled", reportId);
            return;
        }
        
        if (StringUtils.isBlank(reportId)) {
            return;
        }
        
        WorkflowLogger.logInfo("step=validateReportAccess, reportId=%s, validation=enabled", reportId);
        
        try {
            // Get current user's realm ID for cross-realm validation
            String currentRealmId = contextHandler.get(WASContextEnums.OWNER_ID);
            
            // Extract realm from report ID (sbg format: sbg:uuid where uuid contains realm info)
            String reportRealmId = extractRealmFromReportId(reportId);
            
            // Validate cross-realm access
            if (!currentRealmId.equals(reportRealmId)) {
                WorkflowLogger.logError("step=validateReportAccess, reportId=%s, currentRealm=%s, reportRealm=%s, error=cross-realm-access", 
                    reportId, currentRealmId, reportRealmId);
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, 
                    "Cross-realm report access denied for report: " + reportId);
            }
            
            // Use existing RBAC system to validate workflow creation access
            boolean hasWorkflowAccess = accessVerifier.verifyUserAccess("customScheduledActions", "create");
            
            if (!hasWorkflowAccess) {
                WorkflowLogger.logError("step=validateReportAccess, reportId=%s, access=denied", reportId);
                throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, 
                    "User does not have permission to create workflows with report: " + reportId);
            }
            
            // Additional validation: Check if user has report access permissions
            // This would ideally call a report service to validate specific report access
            // For now, we rely on the workflow creation permissions
            
            WorkflowLogger.logInfo("step=validateReportAccess, reportId=%s, access=granted", reportId);
            
        } catch (WorkflowGeneralException ex) {
            // Re-throw workflow exceptions
            throw ex;
        } catch (Exception ex) {
            WorkflowLogger.logError("step=validateReportAccess, reportId=%s, error=%s", 
                reportId, ex.getMessage());
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, 
                "Report access validation failed for: " + reportId, ex);
        }
    }
    
    /**
     * Validates report IDs found in conditional expressions.
     * 
     * @param conditionalExpression The conditional expression that may contain report IDs
     */
    public void validateReportIdsInExpression(String conditionalExpression) {
        if (StringUtils.isBlank(conditionalExpression)) {
            return;
        }
        
        Matcher matcher = REPORT_ID_PATTERN.matcher(conditionalExpression);
        while (matcher.find()) {
            String reportId = "sbg:" + matcher.group(1);
            validateReportAccess(reportId);
        }
    }
    
    /**
     * Checks if report access validation is enabled via configuration and feature flag.
     *
     * @return true if validation is enabled, false otherwise
     */
    private boolean isReportAccessValidationEnabled() {
        // Check global configuration first
        if (!config.isEnabled()) {
            return false;
        }

        try {
            return ixpManager.getBoolean(config.getFeatureFlag(), true);
        } catch (Exception ex) {
            WorkflowLogger.logError("step=isReportAccessValidationEnabled, error=%s", ex.getMessage());
            // Default to enabled for security
            return true;
        }
    }
    
    /**
     * Extracts realm ID from report ID.
     * This is a simplified implementation - in reality, you'd need to call
     * the report service to get the actual realm associated with the report.
     * 
     * @param reportId The report ID in format sbg:uuid
     * @return The realm ID associated with the report
     */
    private String extractRealmFromReportId(String reportId) {
        // For now, return current realm ID as we don't have report service integration
        // In a real implementation, this would call the report service to get the report's realm
        try {
            return contextHandler.get(WASContextEnums.OWNER_ID);
        } catch (Exception ex) {
            WorkflowLogger.logError("step=extractRealmFromReportId, reportId=%s, error=%s", 
                reportId, ex.getMessage());
            throw new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, 
                "Unable to validate report realm for: " + reportId, ex);
        }
    }
}
