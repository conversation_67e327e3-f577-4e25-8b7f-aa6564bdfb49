package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask.HumanTaskServiceFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers.IPMMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This is the implementation handling for HumanTask models.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WorkflowHumanTask extends WorkflowTask<HumanTask> {
  
  private HumanTaskServiceFactory humanTaskServiceFactory;

  @Override
  public TaskType type() {
    return TaskType.HUMAN_TASK;
  }

  @Override
  public TypeReference<HumanTask> typeReference() {
    return new TypeReference<>() {
    };
  }

  @Override
  public WorkflowTaskResponse create(HumanTask request) {
    return humanTaskServiceFactory.getService(request).create(request);
  }

  @Override
  public WorkflowTaskResponse update(HumanTask request) {
    return humanTaskServiceFactory.getService(request).update(request);
  }

  @Override
  public WorkflowTaskResponse complete(HumanTask request) {
    return humanTaskServiceFactory.getService(request).complete(request);
  }
  
  @Override
  public WorkflowTaskResponse failed(HumanTask request) {
    return humanTaskServiceFactory.getService(request).failed(request);
  }

  @Override
  public WorkflowTaskResponse get(HumanTask request) {
    return humanTaskServiceFactory.getService(request).get(request);
  }
  
  @Override
  public String getWASActivityStatus(String status) {
	  return IPMMapper.getReversedStatus(status);
  }
}
