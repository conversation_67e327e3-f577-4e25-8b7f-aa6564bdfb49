package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.MULTIPLE_CALLED_PROCESSES_FOUND;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_USERID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.OBJECT_TYPE_CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.STRING_TYPE_CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_OWNER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction.CUSTOM_REMINDER_CUSTOM_RECUR;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.TemplateQueryCapabilityFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.TemplateQueryCapabilityIf;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ApprovalTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.AdditionalDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.TypedProcessVariableInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse.WorkflowGenericResponseBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggersResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableDetail;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.spin.Spin;
import org.camunda.spin.json.SpinJsonNode;
import org.javatuples.Pair;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@AllArgsConstructor
public class V3RunTimeHelper {

  private final ProcessDetailsRepository processDetailsRepository;

  private final DefinitionDetailsRepository definitionDetailsRepository;

  private final TemplateQueryCapabilityFactory templateQueryCapabilityFactory;

  private final ProcessDomainEventHandler processDomainEventHandler;

  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  private final CustomWorkflowConfig customWorkflowConfig;

  private final AuthHelper authHelper;

  private final CustomWorkflowQueryCapability customWorkflowQueryCapability;

  private final IXPManager ixpManager;

  //supporting these types for unsolicited variables in the payload, others will be defaulted to String
  private static Map<String, String> sDataTypeMap;
  static {
    sDataTypeMap = new HashMap<>();
    sDataTypeMap.put("java.lang.Boolean", "Boolean");
    sDataTypeMap.put("java.lang.Short", "Short");
    sDataTypeMap.put("java.lang.Integer", "Integer");
    sDataTypeMap.put("java.lang.Long", "Long");
    sDataTypeMap.put("java.lang.Double", "Double");
    sDataTypeMap.put("java.lang.String", "String");
  }

  private static final String tokenRegex = "\\$\\{.*?}";
  private static final Pattern pattern = Pattern.compile(tokenRegex);


  /**
   * Merges the processDetails and definitionDetails with all it's called processes and their definitionDetails
   * @param processDetails - list of parent processDetails to merge with
   * @param definitionDetails - list of definitions of the parent processDetails
   */
  public void mergeCalledProcesses(
      List<ProcessDetails> processDetails, List<DefinitionDetails> definitionDetails
  ) {
    int totalProcessesBeforeMerging = processDetails.size();

    //  Fetch called processes.
    List<ProcessDetails> calledProcessDetails =
        fetchAllCalledProcesses(processDetails).orElse(Collections.emptyList());

    Map<String, ProcessDetails> processDetailsMap =
        processDetails.stream().collect(Collectors.toMap(
            ProcessDetails::getProcessId,
            processDetail -> processDetail
        ));

    calledProcessDetails.forEach(
        calledProcess -> calledProcess.setParentProcessDetails(
            processDetailsMap.get(calledProcess.getParentId())
        )
    );

    WorkflowVerfiy.verify(calledProcessDetails.size() > 1, MULTIPLE_CALLED_PROCESSES_FOUND);

    processDetails.addAll(calledProcessDetails);
    definitionDetails.addAll(getDefinitionDetailsForProcesses(calledProcessDetails));

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Merged with called processes; step=mergeCalledProcesses, beforeMerging=%s, afterMerging=%s",
                    totalProcessesBeforeMerging,
                    calledProcessDetails.size()
                ).className(this.getClass().getName()));
  }

  private Optional<List<ProcessDetails>> fetchAllCalledProcesses(List<ProcessDetails> processDetails) {
    return processDetailsRepository.findByProcessStatusAndParentIdIn(
        ProcessStatus.ACTIVE,
        processDetails.stream().map(ProcessDetails::getProcessId).collect(
            Collectors.toList())
        );
  }

  private List<DefinitionDetails> getDefinitionDetailsForProcesses(List<ProcessDetails> processDetails) {
    return processDetails.stream()
        .map(ProcessDetails::getDefinitionDetails)
        .collect(Collectors.toList());
  }

  /**
   * Remove the processes which are started today for multi-condition workflows.
   * We want to prevent signalling the processes with recur event which were started today.
   * @param processDetails
   */
  public void filterProcessesStartedTodayForRecurringWorkflows(
      List<ProcessDetails> processDetails,
      TransactionEntity transactionEntity) {

    int sizeBeforeFiltering = processDetails.size();

    processDetails.removeIf(process ->
        MultiStepUtil.isMultiConditionWorkflow(process.getDefinitionDetails()) &&
            transactionEntity.getEventHeaders().getEntityChangeType().equals(
                CUSTOM_REMINDER_CUSTOM_RECUR.getEntityChangeType()) &&
        process.getCreatedDate().toLocalDateTime().toLocalDate().isEqual(LocalDate.now()));

    int sizeAfterFiltering = processDetails.size();

    WorkflowLogger.logInfo("Filter Recur processes; step=filterProcessesStartedTodayForMultiConditionWorkflows, sizeBeforeFiltering=%s, sizeAfterFiltering=%s",
        sizeBeforeFiltering, sizeAfterFiltering);
  }


  /**
   * To merge the trigger responses for the parent and the called processes, and returns the
   * trigger response for the parent processes only. The grouping for the processes happens on
   * the parent process associated, i.e., on basis of parent id in case it's not null (called process),
   * and the process id in case the parent id is null (parent process).
   * <p>
   * In case any of the called process is errored, it will return the parent process as errored,
   * and in case any of the called process is signalled, it would return the parent process as signalled.
   * Else, it would return no action on the parent process.
   *
   * @param triggerResponses - List of trigger responses for both parent and called processes
   * @param processDetails - List of all parent and called processes
   * @return - List of trigger responses for parent processes only with TriggerStatus for all called processes
   */
  public List<WorkflowTriggerResponse> mergeTriggerResponse(
      List<WorkflowTriggerResponse> triggerResponses,
      List<ProcessDetails> processDetails
  ) {
    Map<String, ProcessDetails> processIdToProcessDetailsMap =
        processDetails.stream().collect(
            Collectors.toMap(
                ProcessDetails::getProcessId, processDetail -> processDetail
            )
        );

    Map<String, List<WorkflowTriggerResponse>> processToTriggerResponseMap =
        triggerResponses.stream().collect(
            Collectors.groupingBy(
                workflowTriggerResponse ->
                    getAssociatedParentProcessId(
                        processIdToProcessDetailsMap.get(workflowTriggerResponse.getProcessId())
                    )
            )
        );

    List<WorkflowTriggerResponse> mergedTriggerResponses = processToTriggerResponseMap.entrySet().stream().map(
        triggerResponsesForParentProcessMapEntry -> {
          String parentProcessId = triggerResponsesForParentProcessMapEntry.getKey();
          List<WorkflowTriggerResponse> allTriggerResponsesForParent = triggerResponsesForParentProcessMapEntry.getValue();

          WorkflowTriggerResponse parentWorkflowTriggerResponse =
              allTriggerResponsesForParent.stream()
                  .filter(
                      workflowTriggerResponse ->
                          workflowTriggerResponse.getProcessId().equals(parentProcessId)
                  ).findFirst().get();

          allTriggerResponsesForParent.forEach(
              triggerResponse -> WorkflowLogger.info(
                  () ->
                      WorkflowLoggerRequest.builder()
                          .message(
                              "Trigger Responses; step=mergeTriggerResponse, parentProcessId=%s, calledProcessId=%s, triggerStatus=%s",
                              parentWorkflowTriggerResponse.getProcessId(),
                              triggerResponse.getProcessId(),
                              triggerResponse.getStatus()
                          ).className(this.getClass().getName()))
          );

          TriggerStatus combinedTriggerStatus =
              getCombinedTriggerStatus(allTriggerResponsesForParent);

          return getWorkflowTriggerResponse(
              combinedTriggerStatus,
              parentWorkflowTriggerResponse.getProcessId(),
              parentWorkflowTriggerResponse.getDefinitionId(),
              parentWorkflowTriggerResponse.getDefinitionName()
          );
        }
    ).collect(Collectors.toList());

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Merged Trigger Responses; step=mergeTriggerResponse, beforeMerging=%s, afterMerging=%s",
                    triggerResponses.size(),
                    mergedTriggerResponses.size()
                ).className(this.getClass().getName()));

    return mergedTriggerResponses;
  }

  private TriggerStatus getCombinedTriggerStatus(List<WorkflowTriggerResponse> allTriggerResponsesForParent) {
    return allTriggerResponsesForParent.stream()
        // if any called process is errored, then error returned on parent process
        .anyMatch(workflowTriggerResponse ->
            workflowTriggerResponse.getStatus().equals(TriggerStatus.ERROR_SIGNALING_PROCESS)
        )
        ? TriggerStatus.ERROR_SIGNALING_PROCESS : allTriggerResponsesForParent.stream()
        // if any called process is signalled, then signalled returned on parent process
        .anyMatch(workflowTriggerResponse ->
            workflowTriggerResponse.getStatus().equals(TriggerStatus.PROCESS_SIGNALLED)
        )
        ? TriggerStatus.PROCESS_SIGNALLED
        // if none of the called process is signalled/errored, then no action returned on parent
        : TriggerStatus.NO_ACTION;
  }


  private String getAssociatedParentProcessId(ProcessDetails processDetail) {
    return Objects.isNull(processDetail.getParentId()) ?
        // parent process, so group by process id
        processDetail.getProcessId()
        // called process, so group by parent id
        : processDetail.getParentId();
  }

  /**
   * * Extract the variables from entity. This will be set as process variables or variables for DMN
   * evaluation
   *
   * @param entityObj - The v3 transaction entity as a hashmap
   * @param inputVar - The input variable name to be set on process or dmn evaluation
   * @param variableType - type of variable
   * @return the variables payload in the form acceptable by camunda.
   */
  @SuppressWarnings("unchecked")
  public Map<String, Object> extractVariablesFromEntity(
      final Map<String, Object> entityObj,
      final String inputVar,
      final String variableType,
      final boolean overrideProcessVariable) {

    WorkflowVerfiy.verify(
        MapUtils.isEmpty(entityObj) || StringUtils.isEmpty(inputVar), WorkflowError.INVALID_INPUT);
    final String[] inputStrArr;
    final Map<String, Object> subValueMap = new HashMap<>();

    /**
     * check for parameters whose values can be extracted from auth header
     * for example: user id or realm id
     * as these need not be sent explicitly as part of the entity payload
     */

    if (authHelper.canExtractVariablesFromAuth(inputVar)) {
      // We are removing the auto approval for task assignee in approval workflow. This check will
      // add an extra character during the rule evaluation so that the condition check on user_id
      // is always false
      String inputValue = authHelper.getAuthValueForKey(inputVar);

      addToSubValueMap(
          subValueMap,
          variableType,
          inputValue,
          overrideProcessVariable);
    } else if (inputVar.contains(WorkflowConstants.BPMN_DMN_VAR_SEPARATOR)) {
      inputStrArr =
          inputVar.split(
              WorkflowConstants.BPMN_DMN_VAR_SEPARATOR, WorkflowConstants.BPMN_DMN_MAX_VAR_NESTING);
      int index = 0;
      final int lastIdx = inputStrArr.length - 1;
      Map<String, Object> attributeObj = entityObj;
      while (index < lastIdx) {
        attributeObj = (Map<String, Object>) attributeObj.get(inputStrArr[index]);
        // Cases when entity does not have appropriate fields
        if (MapUtils.isEmpty(attributeObj)) {
          addToSubValueMap(subValueMap, variableType, null, overrideProcessVariable);
          break;
        }
        index++;
      }
      // If iterated through the end of loop then fetch the appropriate field value
      if (index == lastIdx) {
        addToSubValueMap(
            subValueMap,
            variableType,
            attributeObj.get(inputStrArr[index]),
            overrideProcessVariable);
      }

    } else {
      addToSubValueMap(subValueMap, variableType, entityObj.get(inputVar), overrideProcessVariable);
    }
    return subValueMap;
  }

  private void addToSubValueMap(
      Map<String, Object> subValueMap,
      final String variableType,
      final Object value,
      final boolean overrideProcessVariable) {

    if (OBJECT_TYPE_CAMUNDA.equals(variableType)) {
      if (overrideProcessVariable && Objects.isNull(value)) {
        WorkflowLogger.info(
            () -> WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message("Null variable found"));
      } else {
        setObjectValueInVariable(subValueMap, value);
      }
    } else {
      if (overrideProcessVariable) {
        subValueMap.put(
            WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, fetchDefaultValue(variableType, value));
      } else if (!Objects.isNull(value)) {
        subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, value);
      }
    }
  }

  /**
   * Handle setting of value with the variableType as Object
   *
   * @param subValueMap single element in the variable map
   * @param value value for the variable to be set
   */
  private void setObjectValueInVariable(Map<String, Object> subValueMap, final Object value) {
    String stringVal = ObjectConverter.toJson(value);
    SpinJsonNode spinJsonNode;

    try {
      spinJsonNode = Spin.JSON(stringVal);
    } catch (Exception ex) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_VARIABLE_VALUE);
    }
    subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, spinJsonNode.toString());

    String objectTypeName = value.getClass().getName();
    // Support for Collections objects only
    if (objectTypeName.contains(WorkflowConstants.JAVA_UTIL) && !objectTypeName.equals(
        WorkflowConstants.JAVA_UTIL_ALL)) {
      setValueInfo(subValueMap, value);
    } else {
      WorkflowLogger.logError("Variable object with type=%s not supported", objectTypeName);
    }
  }

  /**
   * For serialized variables of type Object, the following properties can be provided:
   * objectTypeName: A string representation of the object's type name.
   * serializationDataFormat: The serialization format used to store the variable.
   *
   * @param subValueMap single element in the variable map
   * @param value value for the variable to be set
   */
  private void setValueInfo(Map<String, Object> subValueMap, final Object value) {
    Map<String, Object> valueInfo = new HashMap<>();
    valueInfo.put(WorkflowConstants.OBJ_TYPE_NAME, value.getClass().getName());
    valueInfo.put(WorkflowConstants.SERIALIZATION_DATA_FORMAT,
        Variables.SerializationDataFormats.JSON.getName());
    subValueMap.put(WorkflowConstants.VALUE_INFO, valueInfo);
  }

  /**
   * return value as "" if type is String and value is null else returns the value
   *
   * @param variableType input type of variable
   * @param value input computed value
   * @return value
   */
  private Object fetchDefaultValue(final String variableType, final Object value) {
    if (Objects.isNull(value) && STRING_TYPE_CAMUNDA.equals(StringUtils.capitalize(variableType))) {
      return StringUtils.EMPTY;
    }
    return value;
  }

  /**
   * Get the template name from record type and workflow type ToDo: Revisit when supporting BYOAW ,
   * same template will support multiple entities. The convention of template name would change.
   *
   * @param recordType Type of record/entity example invoice , bill, estimate etc
   * @param workflow Type of workflow such as approval , reminder etc.
   * @return the template name
   */
  public String getTemplateName(final RecordType recordType, final String workflow) {

    final String templateName = StringUtils.EMPTY;
    return templateName.concat(recordType.toString()).concat(workflow);
  }

  /**
   * If the request header contains a Workflow ID, return the eligible definition for that specific
   * workflow. Else return all eligible definitions for the company.
   *
   * @param transactionEntity : {@link TransactionEntity}
   * @param isDefinitionDataRequired : isDefinitionDataRequired flag guides on if we need to get
   *     bpmn definition data or not.
   */
  public List<DefinitionDetails> getEligibleDefinitions(
      final TransactionEntity transactionEntity, final boolean isDefinitionDataRequired) {
    return getRuntimeCapability(transactionEntity)
        .getEnabledDefinitions(transactionEntity, isDefinitionDataRequired);
  }

  /**
   * Get template details based on the entity payload
   *
   * @param transactionEntity entity payload
   * @return
   */
  public List<TemplateDetails> getTemplateDetails(final TransactionEntity transactionEntity) {
    return getRuntimeCapability(transactionEntity).getTemplateDetails(transactionEntity);
  }

  /**
   * Get bpmn template data based on the entity payload
   *
   * @param transactionEntity entity payload
   * @return
   */
  public byte[] getTemplateData(final TransactionEntity transactionEntity) {
    return getRuntimeCapability(transactionEntity).getTemplateData(transactionEntity);
  }


  public ActivityDetail fetchInitialStartEventActivityDetail(final TransactionEntity transactionEntity) {
    return getRuntimeCapability(transactionEntity).fetchInitialStartEventActivityDetail(transactionEntity);
  }

  /**
   * Get DMN template details based on the entity payload
   *
   * @param transactionEntity entity payload
   * @param enabledDefinitionList list of definitions which are enabled
   * @return
   */
  public Pair<String, byte[]> getDmnTemplateDetails(
      final TransactionEntity transactionEntity,
      final List<DefinitionDetails> enabledDefinitionList) {
    // Both providerWorkflowId and definitionKey will not be present in trigger payload together
    // DefinitionKey will be present
    // But if both are present then definitionKey will take preference.
    String id = Optional.ofNullable(transactionEntity.getEventHeaders().getDefinitionKey())
            .orElse(transactionEntity.getEventHeaders().getProviderWorkflowId());
    return getRuntimeCapability(transactionEntity).getDmnTemplateDetails(id, enabledDefinitionList);
  }

  /**
   * Get runtime capability based on transaction entity payload
   *
   * @param transactionEntity entity payload
   * @return
   */
  private TemplateQueryCapabilityIf getRuntimeCapability(
      final TransactionEntity transactionEntity) {
    TemplateQueryCapabilityIf templateQueryCapabilityIf = templateQueryCapabilityFactory.getTemplateQueryCapability(
            transactionEntity.getEventHeaders());
    WorkflowLogger.logInfo(
            "RuntimeCapability=%s selected",
            templateQueryCapabilityIf.getClass().getSimpleName()
    );
    return templateQueryCapabilityIf;
  }

  public List<ProcessVariableDetail> extractParametersFromConfig(
      List<ProcessVariableDetail> processVariables, List<String> actions, String workflow,
      String recordType) {
    List<ProcessVariableDetail> processVariableDetails = new ArrayList<>(processVariables);

    try {
      // in case we have overridable parameters in config we can add them in process variable list
      customWorkflowConfig.getOldConfig().getRecords().stream()
          .filter(record -> StringUtils.equalsIgnoreCase(record.getId(), recordType))
          .flatMap(record -> record.getActionGroups().stream()
              .filter(actionGroup -> StringUtils.equalsIgnoreCase(actionGroup.getId(), workflow))
              .flatMap(actionGroup -> actionGroup.getActions().stream()
                  .filter(action -> actions.contains(action.getId()))
                  .flatMap(action -> action.getParameters().stream()
                      .filter(parameter -> Boolean.TRUE.equals(parameter.getIsOverridable()))
                      .map(parameter -> {
                        ProcessVariableDetail configParameter = new ProcessVariableDetail();
                        configParameter.setVariableName(parameter.getName());
                        configParameter.setVariableType(parameter.getFieldType());
                        return configParameter;
                      }))
              )).forEach(processVariableDetails::add);
    } catch (Exception e) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("extractParametersFromConfig")
                  .message(
                      "Exception extracting parameter from config. ProcessVariableDetails=%s. Actions=%s. Workflow=%s. RecordType=%s.%s",
                      processVariableDetails, actions, workflow, recordType, e.getMessage()));
    }
    return processVariableDetails;
  }

  // for approval workflows we extract the sub actions from the definition_activity_details table
  // for each approval workflow we have entries of subActions in the definition_activity_details
  // table
  public List<String> extractDefinitionSubActions(DefinitionDetails definitionDetails) {
    List<String> filteredSubActions = new ArrayList<>();
    Optional<List<DefinitionActivityDetail>> definitionSubActions =
        definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(
            definitionDetails.getDefinitionId());
    if (definitionSubActions.isEmpty() || definitionSubActions.get().isEmpty()) {
      return new ArrayList<>();
    } else {
      List<DefinitionActivityDetail> definitionSubActionsList =
          definitionSubActions.get().get(0).getChildActivityDetails();
      definitionSubActionsList.stream()
          .forEach(
              definitionActivityDetail -> {
                String attributes = definitionActivityDetail.getUserAttributes();
                JSONObject jsonObject = new JSONObject(attributes);
                if ((Boolean) jsonObject.get(WorkflowConstants.SELECTED)) {
                  filteredSubActions.add(definitionActivityDetail.getActivityId());
                }
              });
    }

    return filteredSubActions;
  }

  private HashMap<String, String> getWorkflowAndRecordType(TransactionEntity transactionEntity,
      TemplateDetails templateDetails) {
    HashMap<String, String> workflowAndRecordType = new HashMap<>();
    String workflow = CustomWorkflowType.getActionKey(templateDetails.getTemplateName());
    String recordType = transactionEntity.getEventHeaders().getEntityType().getRecordType();
    workflowAndRecordType.put(WorkflowConstants.WORKFLOW, workflow);
    workflowAndRecordType.put(WorkflowConstants.RECORD_TYPE, recordType);
    return workflowAndRecordType;
  }

  /**
   * Get variables map payload to start or signal process
   * isEntity flag determines whether entityObjMap consists of entity variables from the payload
   * or the misc variables from the payload for trigger.
   **/
  public Map<String, Object> getVariablesMap(
      final TransactionEntity transactionEntity,
      Map<String, Object> entityObjMap,
      final boolean isStartProcess,
      final Map<String, Object> initialStartEventExtensionPropertiesMap,
      final DefinitionDetails definitionDetails,
      final boolean isEntity,
      final ProcessDetails processDetails,
      final boolean isLocal) {

    /**
     * In case of Single Definition get the process variables from placeholder values stored in the
     * database if the flag is on.
     */
    final Map<String, Object> varValueMap = new HashMap<>();
    final Map<String, Object> variables = new HashMap<>();
    ArrayList<String> extractionActions = new ArrayList<>();

    // If entity is empty return empty variables as processing is not required.
    if (MapUtils.isNotEmpty(entityObjMap)) {
      List<ProcessVariableDetail> processVariableDetails = new ArrayList<>();
      Map<String, Object> processVariableAndSequenceVariableDetailsMap = new HashMap<>();
      Map<String, Object> sequenceVariableDetailMap = new HashMap<>();
      //this map manages to get the payload variables that is not in the start event list and still manage to send to camunda
      Map<String, Boolean> variablesAllowed = new HashMap<>();

      // In case of system definition for a custom workflow
      boolean isCustomWorkflowSystemDefinition = CustomWorkflowUtil.isCustomWorkflowSystemDefinition(processDetails, definitionDetails);

      // Flag in-case the process variables details from the definition start event have to be ignored.
      final boolean ignoreProcessVariableDetails = (isCustomWorkflowSystemDefinition || isIgnoreProcessVariablesFlagEnabled(initialStartEventExtensionPropertiesMap, isEntity));

      if (ignoreProcessVariableDetails) {
        entityObjMap.keySet().forEach(v -> {
                  variablesAllowed.put(v, false);
                }
        );
      }


    if (Objects.nonNull(
        getParentOrElseCurrentDefinitionDetails(processDetails, definitionDetails, isStartProcess).getPlaceholderValue()
    )) {
      processVariableAndSequenceVariableDetailsMap =
          getProcessVariableDetailsAndSequenceVariableDetailsFromDefinition(
              getParentOrElseCurrentDefinitionDetails(processDetails, definitionDetails, isStartProcess)
          );
      // Extract the process variable details list
      processVariableDetails =
          (List<ProcessVariableDetail>)
              processVariableAndSequenceVariableDetailsMap.get(WorkflowConstants.PROCESS_VARIABLES);
      // Extract the sequence variable map
      sequenceVariableDetailMap =
          (Map<String, Object>)
              processVariableAndSequenceVariableDetailsMap.get(
                  WorkflowConstants.SEQUENCE_VARIABLES);

        // Merge the config parameters for action in case of start process from custom workflows
      if (ixpManager.getBoolean(WorkflowConstants.CONFIG_MERGE_FF_ENABLED, false) && isStartProcess
          &&
          customWorkflowQueryCapability.isCustomWorkflow(
              definitionDetails.getTemplateDetails().getTemplateName(),
              RecordType.fromType(
                  transactionEntity.getEventHeaders().getEntityType().getRecordType()))
      ) {
        sequenceVariableDetailMap.forEach(
            (action, attribute) -> {
              if (Boolean.parseBoolean(
                  ((HashMap<String, String>) attribute)
                      .get(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE))) {
                extractionActions.add(action);
              }
            });
        if (CollectionUtils.isNotEmpty(extractionActions)) {
          HashMap<String, String> workflowAndRecordType =
              getWorkflowAndRecordType(transactionEntity, definitionDetails.getTemplateDetails());
          processVariableDetails =
              extractParametersFromConfig(
                  processVariableDetails,
                  extractionActions,
                  workflowAndRecordType.get(WorkflowConstants.WORKFLOW),
                  workflowAndRecordType.get(WorkflowConstants.RECORD_TYPE));
        } else {
          ArrayList<String> subActions =
              (ArrayList<String>) extractDefinitionSubActions(definitionDetails);
          if (CollectionUtils.isNotEmpty(subActions)) {
            HashMap<String, String> workflowAndRecordType =
                getWorkflowAndRecordType(
                    transactionEntity, definitionDetails.getTemplateDetails());
            processVariableDetails =
                extractParametersFromConfig(
                    processVariableDetails,
                    subActions,
                    workflowAndRecordType.get(WorkflowConstants.WORKFLOW),
                    workflowAndRecordType.get(WorkflowConstants.RECORD_TYPE));
          }
        }
      }
    } else {
      processVariableDetails = getProcessVariableDetailsFromStartEvents(initialStartEventExtensionPropertiesMap, ignoreProcessVariableDetails);
    }

      // if the feature ignoreProcessVariablesDetails is enabled , then we proceed anyways even if start event does not have the map of processVariablesDetail
      if (!ignoreProcessVariableDetails) {
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(processVariableDetails),
                WorkflowError.TRIGGER_PROCESS_VARIABLE_DETAILS_NOT_FOUND);

      }

      if (!CollectionUtils.isEmpty(processVariableDetails)) {
        processVariableDetails.forEach(
                processVariableDetail -> {
                  Map<String, Object> subValueMap = new HashMap<>();
                  // Specific handling as there is no better way
                  if (WorkflowConstants.ENTITY_CHANGE_TYPE.equals(
                          processVariableDetail.getVariableName())) {
                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, WorkflowConstants.STRING_TYPE_CAMUNDA);
                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
                            transactionEntity.getEntityChangeType().toString().toLowerCase());
                    varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
                    return;
                  } else if (processVariableDetail.getVariableName().equals(INTUIT_USERID)
                          || processVariableDetail.getVariableName().equals(INTUIT_REALMID)) {

                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, WorkflowConstants.STRING_TYPE_CAMUNDA);
                    String authValue = authHelper.getAuthValueForKey(
                            processVariableDetail.getVariableName());
                    if (processVariableDetail.getVariableName().equals(INTUIT_USERID)){
                      WorkflowLogger.logInfo(
                              "intuit_userid will be set as %s in the process variable map",
                              authValue
                      );
                    }
                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, authValue);

                    if ((!isStartProcess)
                            && INTUIT_USERID.equals(processVariableDetail.getVariableName())) {
                      varValueMap.put(WorkflowConstants.TRIGGERED_BY_INTUIT_USERID, subValueMap);
                    } else {
                      varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
                    }
                    return;
                  } else if (WorkflowConstants.ENTITY_TYPE.equals(
                          processVariableDetail.getVariableName())) {
                    // Populate entity type from header
                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, WorkflowConstants.STRING_TYPE_CAMUNDA);
                    subValueMap.put(
                            WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
                            transactionEntity.getEntityType().getDisplayValue());
                    varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
                    return;
                  }

                  final String variableType = processVariableDetail.getVariableType();

                  try {
                    if (!ignoreProcessVariableDetails) {
                      subValueMap = extractVariablesFromEntity(
                              entityObjMap,
                              processVariableDetail.getVariableName(),
                              variableType,
                              processVariableDetail.getOverrideIfAbsent());
                    }
                  } catch (final Exception e) {
                    throw new WorkflowGeneralException(
                            WorkflowError.TRIGGER_PROCESS_VARIABLE_DETAILS_NOT_FOUND);
                  }

                  if (MapUtils.isNotEmpty(subValueMap) && !ignoreProcessVariableDetails) {
                    subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, variableType);
                    // Handling in-case variables are provided in typedProcessVariable format, in that case using the payload as is.
                    subValueMap = checkAndGetTypedValue(entityObjMap.get(processVariableDetail.getVariableName()), subValueMap);
                    varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
                    if (ignoreProcessVariableDetails) {
                      variablesAllowed.put(processVariableDetail.getVariableName(), true);
                    }
                  }

                  // Populate approverId from auth header if its null in entityObjMap
                  if (!isStartProcess && ApprovalTaskConstants.APPROVER_ID.equals(processVariableDetail.getVariableName())) {
                    populateApproverIdInVariableMap(transactionEntity, isLocal, subValueMap, varValueMap, processVariableDetail);
                  }
                });
      }

      // try to check which variables did not match in the preceding loop
      if (ignoreProcessVariableDetails && MapUtils.isNotEmpty(variablesAllowed)) {
        Map<String, Object> discardedVariables = getDiscardedVariables(entityObjMap, variablesAllowed);
        WorkflowLogger.logInfo("variablesAllowed: %s, Discarded variables: %s",
            variablesAllowed, discardedVariables.toString());
        varValueMap.putAll(discardedVariables);
      }

      // adding single variable map to varValueMap
      varValueMap.putAll(sequenceVariableDetailMap);
      // not setting CorrelationKeys in SYSTEM template
      boolean populateCorrelationKeys =
              Optional.of(definitionDetails)
                      .map(DefinitionDetails::getTemplateDetails)
                      .filter(templateDetails -> DefinitionType.SYSTEM != templateDetails.getDefinitionType())
                      .isPresent();

      // For Other variables that can be a candidate for Correlation Keys
      if (isStartProcess && populateCorrelationKeys) {
        prepareCorrelationKeys(
                varValueMap,
                transactionEntity.getEntityType(),
                transactionEntity.getWorkflowType(),
                definitionDetails);
      }
    }

    variables.put(WorkflowConstants.BPMN_DMN_VARIABLES, varValueMap);
    return variables;
  }

  private DefinitionDetails getParentOrElseCurrentDefinitionDetails(ProcessDetails processDetails,
      DefinitionDetails definitionDetails, boolean isStartProcess) {
    // in case of startProcess
    if(isStartProcess) return definitionDetails;

    // in case of signal
    DefinitionDetails parentDefinitionDetails =
        Objects.nonNull(processDetails.getParentProcessDetails())
            ? processDetails.getParentProcessDetails().getDefinitionDetails()
            : null;

    return Optional.ofNullable(parentDefinitionDetails).orElse(definitionDetails);
  }

  /** @return default trigger/rule evaluate response builder with status as FAILURE */
  public WorkflowGenericResponseBuilder getDefaultResponseBuilder(
      final WorkflowResponse workflowResponse) {

    return WorkflowGenericResponse.builder()
        .status(ResponseStatus.FAILURE)
        .response(workflowResponse);
  }

  /**
   * Gets trigger response.
   *
   * @param responseStatus the response status
   * @param triggerStatus the trigger status
   * @param processId the process id
   * @return the trigger response
   */
  public WorkflowGenericResponse getTriggerResponse(
      final ResponseStatus responseStatus,
      final TriggerStatus triggerStatus,
      final String processId) {

    return WorkflowGenericResponse.builder()
        .status(responseStatus)
        .response(
            WorkflowTriggerResponse.builder().processId(processId).status(triggerStatus).build())
        .build();
  }

  /**
   * @param responseStatus the response status
   * @param triggerStatus the trigger status
   * @param processId the process id
   * @param definitionId definition id
   * @param definitionName definition name
   * @return {@link WorkflowGenericResponse}
   */
  public WorkflowGenericResponse getTriggerResponse(
      final ResponseStatus responseStatus,
      final TriggerStatus triggerStatus,
      final String processId,
      final String definitionId,
      final String definitionName) {
    return WorkflowGenericResponse.builder()
        .status(responseStatus)
        .response(
            new WorkflowTriggersResponse(
                Collections.singletonList(
                    getWorkflowTriggerResponse(
                        triggerStatus,
                        processId,
                        definitionId,
                        definitionName)))).build();
  }

  public WorkflowTriggerResponse getWorkflowTriggerResponse(
      final TriggerStatus triggerStatus, final String processId,
      final String definitionId, final String definitionName
  ) {
    return WorkflowTriggerResponse.builder()
        .definitionId(definitionId)
        .processId(processId)
        .status(triggerStatus)
        .definitionName(definitionName)
        .build();
  }

  /** Fetch the process variables to be set on the process from extension elements of template. */
  private List<ProcessVariableDetail> getProcessVariableDetailsFromStartEvents(
          final Map<String, Object> initialStartEventExtensionPropertiesMap, boolean ignoreProcessVariablesDetailsDataEnabled) {

    List<ProcessVariableDetail> processVariableDetailsList = new ArrayList<>();

    final Object processVariableDetailsData = initialStartEventExtensionPropertiesMap.get(WorkFlowVariables.PROCESS_VARIABLE_DETAILS_KEY.getName());
    if (ObjectUtils.isEmpty(processVariableDetailsData)) {
      // Handle the case where the data is null or empty
      // For example, you might want to return an empty list or throw an exception
      return processVariableDetailsList;
    }
    if (processVariableDetailsData instanceof String) {
      // If the object is a JSON string, parse it into a list
      String jsonString = (String) processVariableDetailsData;
      processVariableDetailsList = ObjectConverter.fromJson(
              jsonString, new TypeReference<List<ProcessVariableDetail>>() {});
    } else {
      // Handle other types or throw an exception if the type is unsupported
      throw new WorkflowGeneralException(WorkflowError.PROCESS_VARIABLE_DETAILS_ERROR, "Unsupported type for processVariableDetailsData: %s", processVariableDetailsData);
    }
    if (!ignoreProcessVariablesDetailsDataEnabled) {
      WorkflowVerfiy.verify(
              CollectionUtils.isEmpty(processVariableDetailsList),
              WorkflowError.TRIGGER_STARTABLE_EVENTS_DETAILS_NOT_FOUND);
    }
    return processVariableDetailsList;
  }

  /**
   * Save process details to WAS DB
   *
   * @param recordId input record id
   * @param ownerId input owner id
   * @param processId input process id
   * @param status input process status
   * @param definitionDetails definition detail
   */
  @Trace
  @Transactional
  @ServiceMetric(serviceName = ServiceName.WAS_DB, methodName = "saveProcessDetailsInstance")
  public ProcessDetails saveProcessDetailsInstance(
      @Tracer(key = WASContextEnums.RECORD_ID) final String recordId,
      @Tracer(key = WASContextEnums.OWNER_ID) final Long ownerId,
      @Tracer(key = WASContextEnums.PROCESS_INSTANCE_ID) final String processId,
      final ProcessStatus status,
      final DefinitionDetails definitionDetails,
      String parentProcessId,
      Map<String, Object> entityObjMap
  ) {

    final ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId(processId)
            .ownerId(ownerId)
            .recordId(recordId)
            .processStatus(status)
            .parentId(parentProcessId)
            .definitionDetails(definitionDetails)
            .build();
    // Saving Process Detail in WAS DB
    final ProcessDetails processDetail = processDetailsRepository.save(processDetails);

    // Publish Domain Events
    processDomainEventHandler.publish(
        DomainEntityRequest.<ProcessDetails>builder()
            .request(processDetail)
            .entityChangeAction(EntityChangeAction.CREATE)
            .eventHeaderEntity(null)
            .additionalDetails(AdditionalDetails.builder().variables(entityObjMap).build())
            .build());

    return processDetail;
  }
  /**
   * This method prepares Correlation Keys and sets them in Process start
   *
   * @param varValueMap : Map of variable values to be set in Camunda
   * @param entityType : Entity Type
   * @param workflowType : Workflow Type
   * @param definitionDetails : Definition details object
   */
  private void prepareCorrelationKeys(
      final Map<String, Object> varValueMap,
      final RecordType entityType,
      final String workflowType,
      final DefinitionDetails definitionDetails) {

    Arrays.stream(CorrelationKeysEnum.values())
        .forEach(
            (correlationKey) -> {
              final Map<String, Object> subValueMap = new HashMap<>();
              // TODO : Using if else for now as there are 2 keys to set.
              if (CorrelationKeysEnum.TEMPLATE_NAME
                  .getName()
                  .equalsIgnoreCase(correlationKey.getName())) {
                subValueMap.put(
                    WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
                    CorrelationKeysEnum.TEMPLATE_NAME.getDataType());
                subValueMap.put(
                    WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
                    getTemplateName(entityType, workflowType));
                varValueMap.put(correlationKey.getName(), subValueMap);
              } else if (CorrelationKeysEnum.DEFINITION_KEY
                  .getName()
                  .equalsIgnoreCase(correlationKey.getName())) {
                subValueMap.put(
                    WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
                    CorrelationKeysEnum.DEFINITION_KEY.getDataType());
                subValueMap.put(
                    WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
                    definitionDetails.getDefinitionKey());
                varValueMap.put(correlationKey.getName(), subValueMap);
              }
            });
  }

  /**
   * Get enabled definition details by template
   *
   * @param transactionEntity transaction details
   * @param templateDetails template details
   * @return {@link DefinitionDetails}
   */
  public List<DefinitionDetails> getEnabledSystemDefinition(
      final TransactionEntity transactionEntity, final List<TemplateDetails> templateDetails) {

    // template details empty throw error
    systemDefinitionNotFound(CollectionUtils.isEmpty(templateDetails), transactionEntity);

    final Optional<List<DefinitionDetails>> definitionDetail;
    if((boolean) transactionEntity.getV3EntityPayload().getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL, false)){
      definitionDetail= definitionDetailsRepository.findEnabledSystemDefinitionForOnDemandApproval(ModelType.BPMN, templateDetails);
    }else {
      definitionDetail = definitionDetailsRepository.findEnabledSystemDefinition(ModelType.BPMN, templateDetails);
    }

    systemDefinitionNotFound(
        !definitionDetail.isPresent() || CollectionUtils.isEmpty(definitionDetail.get()),
        transactionEntity);

    return definitionDetail.get();
  }

  /**
   * @param condition condition to be evaluated
   * @param transactionEntity transaction details
   * @throws {@link WorkflowGeneralException} if condition satisfies to true
   */
  private void systemDefinitionNotFound(
      final boolean condition, final TransactionEntity transactionEntity) {
    WorkflowVerfiy.verify(
        condition,
        WorkflowError.ENABLED_DEFINITION_NOT_FOUND,
        transactionEntity.getEntityId(),
        transactionEntity.getEventHeaders().getEntityType());
  }

  /**
   * @param entityId
   * @param ownerId
   * @param status
   * @param definitionDetails
   * @return
   */
  public Optional<List<ProcessDetails>> getProcessDetailsInstance(
      String entityId, long ownerId, ProcessStatus status, DefinitionDetails definitionDetails) {
    return processDetailsRepository.findByRecordIdAndOwnerIdAndProcessStatusAndDefinitionDetails(
        entityId, ownerId, status, definitionDetails);
  }

  /**
   * Get the process variables from the placeholder values. @See
   * src/test/resources/placeholder/placeholder_value.json
   *
   * @param definitionDetails {@link DefinitionDetails}
   * @return List of ProcessVariableDetail {@link ProcessVariableDetail}
   */
  private Map<String, Object> getProcessVariableDetailsAndSequenceVariableDetailsFromDefinition(
      DefinitionDetails definitionDetails) {
    Map<String, Object> sequenceVariableMap = new HashMap<>();
    Map<String, Object> processVariableAndSequenceVariableMap = new HashMap<>();

    JSONObject placeholder = new JSONObject(definitionDetails.getPlaceholderValue());
    JSONObject processVariables = placeholder.getJSONObject(WorkflowConstants.PROCESS_VARIABLES);
    Map<String, Map<String, String>> extractedProcessVariablesFromPlaceholderValues =
        ObjectConverter.fromJson(
            processVariables.toString(), new TypeReference<Map<String, Map<String, String>>>() {});
    List<ProcessVariableDetail> processVariableDetails = new ArrayList<>();
    for (Map.Entry<String, Map<String, String>> processVarMap :
        extractedProcessVariablesFromPlaceholderValues.entrySet()) {
      ProcessVariableDetail processVariableDetail = new ProcessVariableDetail();
      processVariableDetail.setVariableName(processVarMap.getKey());
      processVariableDetail.setVariableType(
          processVarMap.getValue().get(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE));
      processVariableDetail.setOverrideIfAbsent(true);
      // Sequence variables have value set in the placeholder values so put in sequence variable map
      // else put it in the process variable map
      if (Objects.nonNull(processVarMap.getValue().get(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE))
          && !(processVarMap.getValue().get(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE).isEmpty())) {
        Map<String, Object> subValueMap = new HashMap<>();
        subValueMap.put(
            WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
            processVarMap.getValue().get(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE));
        subValueMap.put(
            WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
            processVarMap.getValue().get(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE));
        sequenceVariableMap.put(processVarMap.getKey(), subValueMap);
      } else {
        processVariableDetails.add(processVariableDetail);
      }
    }
    processVariableAndSequenceVariableMap.put(
        WorkflowConstants.PROCESS_VARIABLES, processVariableDetails);
    processVariableAndSequenceVariableMap.put(
        WorkflowConstants.SEQUENCE_VARIABLES, sequenceVariableMap);
    return processVariableAndSequenceVariableMap;
  }

  /**
   * Get the getDiscarded variables by complementing variablesAllowed from the entityObjMap
   *
   * @param entityObjMap All variables
   * @param variablesAllowed Variables that are allowed in the start event
   * @return Map of variables that are discarded
   */
  private Map<String, Object> getDiscardedVariables(Map<String, Object> entityObjMap,
      Map<String, Boolean> variablesAllowed) {

    return variablesAllowed
        .entrySet()
        .stream()
        .filter(entrySet -> !entrySet.getValue())
        .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
          Map<String, Object> subValueMap = new HashMap<>();
          // we have type support for the types defined in sDataTypeMap, others will be
          // defaulted to Object and stored as spin formats
          String variableType = sDataTypeMap.getOrDefault(
              entityObjMap.get(entry.getKey()).getClass().getTypeName(),
              OBJECT_TYPE_CAMUNDA);
          subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, variableType);
          if(OBJECT_TYPE_CAMUNDA.equals(variableType)) {
            setObjectValueInVariable(subValueMap, entityObjMap.get(entry.getKey()));
            subValueMap = checkAndGetTypedValue(entityObjMap.get(entry.getKey()), subValueMap);
          } else {
            subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE,
                entityObjMap.get(entry.getKey()));
          }
          return subValueMap;
        }));
  }


  /**
   * Checks the extension element flag, if ignore process variables flag is enabled.
   *
   * @return true if the prop is true or false
   */
  private boolean isIgnoreProcessVariablesFlagEnabled(final Map<String, Object> initialStartEventExtensionPropertiesMap, final boolean isEntity) {
    final boolean ignoreProcessVariablesDetailsDataEnabled =
        isExtensionAttributeFlagEnabled(initialStartEventExtensionPropertiesMap,
            WorkFlowVariables.IGNORE_PROCESS_VARIABLES_DETAILS);
    WorkflowLogger.logInfo(
        "isIgnoreProcessVariablesFlagEnabled, isIgnoreProcessVariablesDetailsDataEnabled=%s",
        String.valueOf(ignoreProcessVariablesDetailsDataEnabled));

    if (ignoreProcessVariablesDetailsDataEnabled) {
      return ignoreProcessVariablesDetailsDataEnabled;
    }

    // Flag to ignore process details data for only non entity variables.
    // This is short term approach flag, which is introduced to solve the issue of process variables to be passed to child
    // process without amending the extension attributes of parent BPMN. Refere Long term approach here:
    // https://docs.google.com/document/d/125NQ9sPUSY9jNh1VdZzWpmL0afRoJTfdZhxKYVjrt8E/edit#heading=h.ykhpfrudnn6
    final boolean ignoreNonEntityProcessVariablesDetails =
            isExtensionAttributeFlagEnabled(initialStartEventExtensionPropertiesMap, WorkFlowVariables.IGNORE_NON_ENTITY_PROCESS_VARIABLES_DETAILS);
    WorkflowLogger.logInfo("isIgnoreProcessVariablesFlagEnabled, ignoreNonEntityProcessVariablesDetails=%s",
            String.valueOf(ignoreNonEntityProcessVariablesDetails));

    return ignoreNonEntityProcessVariablesDetails && !isEntity;
  }

  /**
   * The extension elements prop is opt out by default. If the developer wants the flag
   * they have to go the start event and add the flag as true to the extension elements
   *
   * @return true if the prop is true or false
   */
  private boolean isExtensionAttributeFlagEnabled(final Map<String, Object> initialStartEventExtensionPropertiesMap, WorkFlowVariables name) {
    boolean isExtensionAttributeFlagEnabled;
    if (MapUtils.isEmpty(initialStartEventExtensionPropertiesMap) || !initialStartEventExtensionPropertiesMap.containsKey(name.getName())) {
      // default case is false
      return false;
    }
    Object flagValue = initialStartEventExtensionPropertiesMap.get(name.getName());
    String flagValueString = flagValue != null ? flagValue.toString() : "false";
    isExtensionAttributeFlagEnabled = Boolean.parseBoolean(flagValueString);
    return isExtensionAttributeFlagEnabled;
  }

  /**
   * This method checks all the strings except expressions are present in correlationMsg or not. It
   * calls a method getAllStaticTokens to fetch all the tokens from the triggerName. e.g.
   * checkStaticMessageExistsInCorrelationMsg("${intuit_relamId}customWait", "1234customWait") =
   * true checkStaticMessageExistsInCorrelationMsg("${intuit_relamId}approved", "1234customWait") =
   * false
   *
   * @param correlationMsg
   * @param triggerName
   * @return true if all the strings except expressions are present in correlationMsg
   */
  public boolean checkStaticMessageExistsInCorrelationMsg(
      String correlationMsg, String triggerName) {

    if (StringUtils.isBlank(triggerName) || StringUtils.isBlank(correlationMsg)) {
      return false;
    }
    if (correlationMsg.equals(triggerName)) {
      return true;
    }

    // triggerName == customWait
    if (!checkTriggerNameDynamicMessage(triggerName)) {
      return false;
    }

    List<String> tokens = getAllStaticTokens(triggerName);
    int lastProcessedIndex = 0;
    for (String token : tokens) {
      int index = correlationMsg.indexOf(token, lastProcessedIndex);
      if (index == -1) {
        return false;
      }
      lastProcessedIndex = index + token.length();
    }
    return true;
  }

  /**
   * This method filters all the substrings that does not match the tokenRegex ("\\$\\{.*?}").
   * getAllStaticTokens(${intuit_relamId}customWait) = [customWait]
   * getAllStaticTokens(${intuit_relamId}customWait${intuit_tid}ABC) = [customWait, ABC]
   * getAllStaticTokens(customWait) = [customWait]
   *
   * @param triggerName
   * @return returns list of filtered substrings
   */
  private List<String> getAllStaticTokens(String triggerName) {
    Queue<int[]> tokenIndexes = new LinkedList<>();
    List<String> staticTokens = new ArrayList<>();
    Matcher matcher = pattern.matcher(triggerName);
    while (matcher.find()) {
      tokenIndexes.add(new int[] {matcher.start(), matcher.end()});
    }
    // it means no substring matches the regex expression.
    // e.g. triggerName = customWait
    if (tokenIndexes.isEmpty()) {
      staticTokens.add(triggerName);
    } else {
      // if static substring occurs as prefix
      // e.g. customWait${abc}
      if (tokenIndexes.peek()[0] > 0) {
        staticTokens.add(triggerName.substring(0, tokenIndexes.peek()[0]));
      }
      // if only one substring matches the regex
      // e.g. customWait${abc}
      if (tokenIndexes.size() == 1) {
        int[] token = tokenIndexes.peek();
        if (token[1] != triggerName.length()) {
          // customWait${abc}event
          staticTokens.add(triggerName.substring(token[1]));
        }
        return staticTokens;
      }
      // when multiple substrings matches the regex
      int size = tokenIndexes.size() - 1;
      for (int i = 0; i < size; i++) {
        int[] firstToken = tokenIndexes.poll();
        int[] secToken = tokenIndexes.poll();
        staticTokens.add(triggerName.substring(firstToken[1], secToken[0]));
        if (i == size - 1 && secToken[1] != triggerName.length()) {
          staticTokens.add(triggerName.substring(secToken[1]));
        }
      }
    }
    return staticTokens;
  }

  /**
   * This method checks trigger name has dynamic expression or not.
   * @param triggerName
   * @return
   */
  private boolean checkTriggerNameDynamicMessage(String triggerName) {
    Matcher matcher = pattern.matcher(triggerName);
    return matcher.find();
  }

  /** Marks the status as error in WAS Db and publishes the domain event */
  @Transactional
  public void markProcessError(
      final TransactionEntity transactionEntity, ProcessDetails processDetails) {
    int version = processDetails.getEntityVersion() + 1;
    WorkflowLogger.warn(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .methodName("updateProcessStatus")
                .message(
                    "Marking process in Error state for recordId=%s",
                    transactionEntity.getEntityId())
                .downstreamComponentName(DownstreamComponentName.WAS_DB));
    processDetailsRepository.updateProcessStatusAndEntityVersion(
        processDetails.getProcessId(), ERROR, version);

    // Publish Domain Events with latest entityVersion and Process Status
    final ProcessDetails domainProcessDetails = processDetails;
    domainProcessDetails.setEntityVersion(version);
    domainProcessDetails.setProcessStatus(ERROR);

    processDomainEventHandler.publish(
        DomainEntityRequest.<ProcessDetails>builder()
            .request(domainProcessDetails)
            .entityChangeAction(EntityChangeAction.UPDATE)
            .eventHeaderEntity(null)
            .additionalDetails(AdditionalDetails.builder().variables(getEntityObjectMap(transactionEntity)).build())
            .build());
  }

  /**
   * Checks if the Variable provided in the form of TypedValue Format.
   * @param variableValue map of entity / variables
   * @return TypedVariableMap
   */
  private Map<String, Object> checkAndGetTypedValue(Object variableValue, Map<String, Object> defaultTypedValue) {
    if (variableValue instanceof Map) {
      TypedProcessVariableInput typedProcessVariable =
              Optional.ofNullable(ObjectConverter.convertObject(variableValue, TypedProcessVariableInput.class))
                      .orElse(new TypedProcessVariableInput());
      // Check if the provided value in payload is already of TypedVariableInput
      if (Objects.nonNull(typedProcessVariable.getValue()) && Objects.nonNull(typedProcessVariable.getType())) {
        return (Map<String, Object>) variableValue;
      }
    }
    return defaultTypedValue;
  }

  /**
   * Returns the trigger entity object map from transactional entity
   * @param transactionEntity
   * @return
   */
  public Map<String, Object> getEntityObjectMap(TransactionEntity transactionEntity){
    return transactionEntity.getEntityFromTransaction(
            Optional.ofNullable(transactionEntity)
                    .map(TransactionEntity::getEntityObj)
                    .filter(org.apache.commons.collections4.MapUtils::isNotEmpty)
                    .orElse(Collections.emptyMap()),
            transactionEntity.getEntityType().toString());
  }

  /**
   * Overrides approverId in variable map if not present in global or local variables
   * @param transactionEntity
   * @param isLocal
   * @param subValueMap
   * @param varValueMap
   * @param processVariableDetail
   */
  private void populateApproverIdInVariableMap(
      TransactionEntity transactionEntity,
      boolean isLocal,
      Map<String, Object> subValueMap,
      Map<String, Object> varValueMap,
      ProcessVariableDetail processVariableDetail) {

    String approverId = authHelper.getAuthValueForKey(INTUIT_USERID);

    // if transactionEntityVariables are null then update the approverId in both local and global variables
    if (Objects.isNull(transactionEntity) || Objects.isNull(transactionEntity.getVariables())) {
      WorkflowLogger.logInfo("TransactionEntityVariables are null. Overriding approverId in local "
          + "and global variables with value: %s, isLocal: %s", approverId, isLocal);
      subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE, processVariableDetail.getVariableType());
      subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, approverId);
      varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
      return;
    }

    WorkflowLogger.logInfo("TransactionEntityVariables are not null. "
            + "Checking approverId in local and global variables, isLocal: %s", isLocal);

    // for local variables if approverId is blank in local variables
    // we are not overriding for empty localVariablesMap, we are overriding if approverId key is present but value is empty
    if (isLocal && MapUtils.isNotEmpty(transactionEntity.getVariables().getLocal()) &&
        transactionEntity.getVariables().getLocal().containsKey(ApprovalTaskConstants.APPROVER_ID) &&
        StringUtils.isBlank(String.valueOf(transactionEntity.getVariables().getLocal()
            .get(ApprovalTaskConstants.APPROVER_ID)))) {
      WorkflowLogger.logInfo(
          "approverId is blank in local variables. Overriding approverId in local variables with value: ",
          approverId);
      subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
          processVariableDetail.getVariableType());
      subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, approverId);
      varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
    }

    // for global variables
    // we are not overriding for empty globalVariablesMap, we are overriding if approverId key is present but value is empty
    else if (!isLocal && MapUtils.isNotEmpty(transactionEntity.getVariables().getGlobal()) &&
        transactionEntity.getVariables().getGlobal().containsKey(ApprovalTaskConstants.APPROVER_ID)) {

      // if approverId is present in global variables but blank in subValueMap
      if (StringUtils.isBlank(String.valueOf(subValueMap.get(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE))) &&
          StringUtils.isNotBlank(String.valueOf(transactionEntity.getVariables().getGlobal()
              .get(ApprovalTaskConstants.APPROVER_ID)))) {
        approverId = String.valueOf(transactionEntity.getVariables().getGlobal()
            .get(ApprovalTaskConstants.APPROVER_ID));
        WorkflowLogger.logInfo(
            "approverId is present in global variables but blank in subValueMap. Overriding approverId in global variables with value: ",
            approverId);
        subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
            processVariableDetail.getVariableType());
        subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, approverId);
        varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
      }

      // if approverId is blank in global variables
      else if (StringUtils.isBlank(String.valueOf(transactionEntity.getVariables()
          .getGlobal().get(ApprovalTaskConstants.APPROVER_ID)))) {
        WorkflowLogger.logInfo(
            "approverId is blank in global variables. Overriding approverId in global variables with value: ",
            approverId);
        subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_TYPE,
            processVariableDetail.getVariableType());
        subValueMap.put(WorkflowConstants.BPMN_DMN_VARIABLE_VALUE, approverId);
        varValueMap.put(processVariableDetail.getVariableName(), subValueMap);
      }
    }
  }
}
