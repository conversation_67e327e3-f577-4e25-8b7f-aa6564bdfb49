package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;

import java.nio.charset.StandardCharsets;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class UserDefinitionRead implements DefinitionDetailsRead {

    private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;

    /**
     * This method returns BPMN XML from WAS and if not present goes to Camunda to fetch it.
     * TODO Deprecate the same function in {@link DefinitionServiceHelper} and use this projection
     * from {@link DefinitionServiceImpl}
     * @param definitionDetails {@link DefinitionDetails}
     * @return
     */
    @Override
    public BpmnResponse getBPMNXMLDefinition(DefinitionDetails definitionDetails) {
        // Get the data from WAS DB if present
        WorkflowLogger.logInfo("Reading user definition for definitionId=%s",
                definitionDetails.getDefinitionId());
        /**
         * TODO Check whether in PROD we have a use case where we need to go to Camunda to fetch the definition and
         * remove the logic if not required.
         */
        return getBpmnFromDefinitonDetails(definitionDetails)
                .orElseGet(
                        () -> {
                            WASHttpResponse<BpmnResponse> response =
                                    bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(
                                            definitionDetails.getDefinitionId());
                            WorkflowVerfiy.verify(!response.isSuccess2xx(), WorkflowError.EMPTY_BPMN_EXCEPTION);
                            return response.getResponse();
                        });
    }

    /**
     * Get {@link BpmnResponse} from definition details table
     *
     * @param definitionDetails {@link DefinitionDetails}
     * @return {@link BpmnResponse}
     */
    private Optional<BpmnResponse>  getBpmnFromDefinitonDetails(
            final DefinitionDetails definitionDetails) {

        return Optional.ofNullable(definitionDetails.getDefinitionData())
                .map(data -> new BpmnResponse(definitionDetails.getDefinitionId(), new String(data, StandardCharsets.UTF_8)));
    }
}
