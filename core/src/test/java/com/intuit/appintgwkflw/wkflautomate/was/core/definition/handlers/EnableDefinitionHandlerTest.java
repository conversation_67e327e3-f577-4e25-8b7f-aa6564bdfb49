package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionEnableCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.EnableDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerRecurringProcessHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

/** <AUTHOR> */
@RunWith(MockitoJUnitRunner.class)
public class EnableDefinitionHandlerTest {

  private static final String REALM_ID = "12345";

  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  private final Definition definition = TestHelper.mockDefinitionEntity();

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapproval.bpmn");

  private static final String DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");

  private static final String BPMN_XML_OLD_TEMPLATE_WITH_RECURRENCE =
      TestHelper.readResourceAsString("bpmn/invoiceapprovalRec.bpmn");

  private static final String DMN_XML_OLD_TEMPLATE_WITH_RECURRENCE =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");

  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customScheduledActionsTest2.bpmn");

  private static final String CUSTOM_WORKFLOW_STATEMENTS_DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_customScheduledActions2.dmn");

  @Mock private UpdateDefinitionStatusInDataStoreService updateDefinitionStatusInDataStoreService;

  @Mock private DefinitionServiceHelper definitionServiceHelper;

  private TemplateDetails bpmnTemplateDetail;

  @Mock private DefinitionEnableCommand command;

  @Mock private TemplateDetailsRepository templateDetailsRepository;

  @Mock private DefinitionDetailsRepository definitionDetailsRepository;

  @Mock private TemplateDetails templateDetails;

  @Mock private MetricLogger metricLogger;

  @Mock private RunTimeService runTimeService;

  @InjectMocks private EnableDefinitionHandler enableDefinitionHandler;

  @Mock private BpmnProcessorImpl bpmnProcessor;

  @Mock private ProviderHelper providerHelper;

  @Mock private WASContextHandler contextHandler;

  @Mock private EventScheduleHelper eventScheduleHelper;

 @Mock private TriggerRecurringProcessHelper triggerRecurringProcessHelper;

  @Before
  public void setup() {
    MockitoAnnotations.openMocks(this);
    Mockito.when(command.getName()).thenReturn(CrudOperation.ENABLED.name());
    Mockito.when(templateDetailsRepository.findById(anyString()))
        .thenReturn(Optional.of(templateDetails));
    DefinitionCommands.addCommand(command.getName(), command);
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("invoiceapproval");
    bpmnTemplateDetail.setAllowMultipleDefinitions(false);
    bpmnTemplateDetail.setTemplateCategory(TemplateCategory.SYSTEM.name());
    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    when(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(any()))
        .thenReturn(true);
  }

  @Test
  public void definitionIdNotSet() {
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    try {
      enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_DEFINITION_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void templateNotFound() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.empty());
    enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void fetchDefinitionInvoked() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setDefinitionData(BPMN_XML.getBytes());
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExistingEnabledCustomDefinitionException() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = new ArrayList<>();
    definitionDetails.add(definitionDetail);

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.DEFINITION_ALREADY_EXISTS)).when(providerHelper)
        .isMultipleDefinitionAllowed(definitionInstance.getDefinition().getId(),
            bpmnTemplateDetail, REALM_ID, definition.getRecordType(), true);
    enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void testExistingEnabledCustomDefinitionCheckNotForBill() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    definition.setRecordType(RecordType.BILL.name());
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    bpmnTemplateDetail.setRecordType(RecordType.BILL);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setDefinitionData(BPMN_XML.getBytes());
    List<DefinitionDetails> definitionDetails = new ArrayList<>();
    definitionDetails.add(definitionDetail);

    Mockito.when(
        definitionServiceHelper.findByDefinitionId(
            TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());
    DefinitionInstance def = enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testNullRecordTypeDuringEnable() {
    bpmnTemplateDetail.setTemplateCategory(TemplateCategory.CUSTOM.name());

    bpmnTemplateDetail.setTemplateName("customApproval");
    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    definition.setRecordType(null);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);

    enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void fetchDefinitionInvokedForMultipleDefn() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setDefinitionData(BPMN_XML.getBytes());
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
  }

  @Test
  public void updateDefnStatusFailed() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setDefinitionData(BPMN_XML.getBytes());
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    Mockito.doThrow(
            new WorkflowGeneralException(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED))
        .when(updateDefinitionStatusInDataStoreService)
        .updateStatusForEnabled(Mockito.any());

    try {
      enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void testAppConnectFailed() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    Mockito.doThrow(
            new WorkflowGeneralException(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED))
        .when(command)
        .execute(Mockito.any(), Mockito.any());

    try {
      enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage(), e.getMessage());
      // Assert no interaction with DB
      Mockito.verifyNoInteractions(updateDefinitionStatusInDataStoreService);
    }
  }

  @Test
  public void testCustomApprovalSuccess() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setDefinitionData(BPMN_XML.getBytes());
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("CustomApproval");
    templateDetails.setRecordType(RecordType.BILL);
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setTemplateCategory(TemplateCategory.CUSTOM.name());
    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(
        definitionDetail.getDefinitionId(), def.getDefinition().getId().getLocalId());
  }

  @Test
  public void testMultiConditionDefinitionEnableSuccess() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("CustomApproval");
    templateDetails.setRecordType(RecordType.BILL);
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setTemplateCategory(TemplateCategory.CUSTOM.name());
    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
            TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(
        definitionDetail.getDefinitionId(), def.getDefinition().getId().getLocalId());
  }

  @Test
  public void testEnableWorkflow_Recurrence() throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    DefinitionDetails definitionDetailsDmn = buildDMNDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    Mockito.when(definitionServiceHelper.findByParentId("353b0efa-cd76-4fbf-9b5b-dd68813b08da"))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetailsDmn)));

    Mockito.when(bpmnProcessor.processBpmn(any(), any(), anyBoolean()))
        .thenReturn(buildTemplate());

    Mockito.when(runTimeService.processTriggerMessageV2(any()))
        .thenReturn(WorkflowGenericResponse.builder().build());

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", def.getDefinition().getId().getLocalId());
    Mockito.verify(runTimeService, times(1)).processTriggerMessageV2(any());
  }

  @Test
  public void testEnableWorkflow_Recurrence_EventScheduleEnabled_NoSchedules() throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    when(templateDetailsRepository.findById(Mockito.anyString()))
            .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    when(definitionServiceHelper.findByDefinitionId(
                    TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
            .thenReturn(definitionDetail);

    Mockito.when(runTimeService.processTriggerMessageV2(any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    DefinitionInstance def =
            enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", def.getDefinition().getId().getLocalId());
    Mockito.verify(runTimeService, times(1)).processTriggerMessageV2(any());
  }

  @Test
  public void testEnableWorkflow_Recurrence_EventScheduleDisabled_NoSchedules() throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    when(templateDetailsRepository.findById(Mockito.anyString()))
            .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    when(definitionServiceHelper.findByDefinitionId(
            TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
            .thenReturn(definitionDetail);

    Mockito.when(runTimeService.processTriggerMessageV2(any()))
            .thenReturn(WorkflowGenericResponse.builder().build());

    DefinitionInstance def =
            enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals("353b0efa-cd76-4fbf-9b5b-dd68813b08da", def.getDefinition().getId().getLocalId());
    Mockito.verify(runTimeService, times(1)).processTriggerMessageV2(any());
  }

  @Test
  public void testEnableWorkflow_Recurrence_EventScheduleEnabled_ScheduleIdsPresent()
      throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    when(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(any()))
        .thenReturn(false);

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(
        "353b0efa-cd76-4fbf-9b5b-dd68813b08da", def.getDefinition().getId().getLocalId());
    Mockito.verify(runTimeService, times(0)).processTriggerMessageV2(any());
  }

  @Test
  public void testEnableWorkflow_Recurrence_EventScheduleDisabled_ScheduleIdsPresent()
      throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    when(triggerRecurringProcessHelper.isDefinitionSupportingRecurringProcess(any()))
        .thenReturn(false);

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals(
        "353b0efa-cd76-4fbf-9b5b-dd68813b08da", def.getDefinition().getId().getLocalId());
    Mockito.verify(runTimeService, times(0)).processTriggerMessageV2(any());
  }

  @Test
  public void testEnableWorkflow_Recurrence_Exception() throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    DefinitionDetails definitionDetailsDmn = buildDMNDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setRecordType(RecordType.STATEMENT);
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("HUB");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    Mockito.when(definitionServiceHelper.findByParentId("353b0efa-cd76-4fbf-9b5b-dd68813b08da"))
            .thenReturn(Optional.of(Collections.singletonList(definitionDetailsDmn)));

    Mockito.when(bpmnProcessor.processBpmn(any(), any(), anyBoolean()))
            .thenThrow(WorkflowGeneralException.class);

    try {
      enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage(), e.getMessage());
    }
  }

  @Test(expected = RuntimeException.class)
  public void testEnableWorkflowWithNoDMN() {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setRecordType(RecordType.STATEMENT);
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("HUB");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEnableWorkflow_Recurrence_Old_Templates() {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setTemplateData(BPMN_XML_OLD_TEMPLATE_WITH_RECURRENCE.getBytes());
    bpmnTemplateDetail.setTemplateCategory("HUB");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    DefinitionInstance def =
        enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
    Assert.assertEquals("def_id", def.getDefinition().getId().getLocalId());
  }

  @Test
  public void testEnableWorkflow_Recurrence_Exception_ERROR_STATE() throws IOException {
    DefinitionDetails definitionDetail = buildDefinitionDetails();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    bpmnTemplateDetail = new TemplateDetails();
    bpmnTemplateDetail.setTemplateName("customScheduledActions");
    bpmnTemplateDetail.setAllowMultipleDefinitions(true);
    bpmnTemplateDetail.setId("TEMPLATE-ID");
    bpmnTemplateDetail.setRecordType(RecordType.STATEMENT);
    bpmnTemplateDetail.setTemplateData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    bpmnTemplateDetail.setTemplateCategory("CUSTOM");
    definitionDetail.setTemplateDetails(bpmnTemplateDetail);
    definitionDetail.setWorkflowId("12345");

    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);

    Mockito.when(templateDetailsRepository.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));

    Mockito.when(definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetail);

    Mockito.when(definitionServiceHelper.findByParentId("353b0efa-cd76-4fbf-9b5b-dd68813b08da"))
            .thenReturn(Optional.of(Collections.singletonList(buildDMNDefinitionDetails())));

    State inputRequest = new State();
    inputRequest.addValue(AsyncTaskConstants.RECURRENCE_START_PROCESS_TASK_FAILURE, true);
    inputRequest.addValue(
        AsyncTaskConstants.RECURRENCE_START_PROCESS_EXCEPTION,
        new WorkflowGeneralException(WorkflowError.TRIGGER_START_PROCESS_ERROR));
    inputRequest.addValue(
        AsyncTaskConstants.START_RECURRING_PROCESS_ERROR_MESSAGE,
        WorkflowError.TRIGGER_START_PROCESS_ERROR);

    Mockito.when(bpmnProcessor.processBpmn(any(), any(), anyBoolean()))
            .thenReturn(buildTemplate());
    
    Mockito.when(runTimeService.processTriggerMessageV2(any()))
            .thenThrow(WorkflowGeneralException.class);
    try {
      enableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage(), e.getMessage());
    }

  }

  private DefinitionDetails buildDMNDefinitionDetails() {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("94f4777f-8da1-480f-8be6-f4b3835824a8");
    definitionDetailsDmn.setPlaceholderValue("dId");
    definitionDetailsDmn.setDefinitionName("statement");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    definitionDetailsDmn.setDefinitionData(CUSTOM_WORKFLOW_STATEMENTS_DMN_XML.getBytes());

    return definitionDetailsDmn;
  }

  private DefinitionDetails buildDefinitionDetails() {

    DefinitionDetails definitionDetail = new DefinitionDetails();
    definitionDetail.setDefinitionId("353b0efa-cd76-4fbf-9b5b-dd68813b08da");
    definitionDetail.setDefinitionName("statement");
    definitionDetail.setModelType(ModelType.BPMN);
    definitionDetail.setRecordType(RecordType.STATEMENT);
    definitionDetail.setStatus(Status.ENABLED);
    definitionDetail.setDescription("Description");
    definitionDetail.setDefinitionName("displayName");
    definitionDetail.setDefinitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes());
    return definitionDetail;
  }

  private Template buildTemplate() {
    Template template = new Template();
    template.setDisplayName("displayName");
    template.setWorkflowSteps(
        TestHelper.mockCustomWorkflowDefinition(
                RecordType.STATEMENT.getRecordType(),
                CustomWorkflowType.SCHEDULEDACTIONS.getActionKey())
            .getWorkflowSteps());

    return template;
  }
}
