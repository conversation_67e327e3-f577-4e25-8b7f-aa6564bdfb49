package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;

/**
 * Author: ragarwal7
 **/
public class InternalEventsUtilTest {

  @Test
  public void testBuildHeaders() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ENTITY_ID, "abc");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "key");
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offering");
    headers.put(EventHeaderConstants.TARGET_ASSET_ALIAS, "targetAssetAlias");

    EventHeaderEntity eventHeaderEntity = InternalEventsUtil
        .buildEventHeader(headers, PublishEventType.SERVICE_TASK, EventEntityType.SERVICE_TASK);

    Assert.assertEquals("abc", eventHeaderEntity.getEntityId());
    Assert.assertEquals("key", eventHeaderEntity.getIdempotencyKey());
    Assert.assertEquals("offering", eventHeaderEntity.getOfferingId());
    Assert.assertEquals(PublishEventType.SERVICE_TASK, eventHeaderEntity.getPublishEventType());
    Assert.assertEquals(EventEntityType.SERVICE_TASK, eventHeaderEntity.getEventEntityType());
    Assert.assertEquals("tid", eventHeaderEntity.getTid());
    Assert.assertEquals("targetAssetAlias", eventHeaderEntity.getTargetAssetAlias());

  }


  /**
   * With Context tid
   */
  @Test
  public void test_buildEventHeader() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .id("ext1").workerId("worker1")
        .taskAttributes(TaskAttributes.builder().runtimeAttributes(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(definitionDtls)
        .build();

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.INTUIT_TID, "tid1");

    taskRequest.setTxnId("txn1");
    EventHeaderEntity eventHeader = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
        contextHandler, PublishEventType.EXTERNAL_TASK);
    Assert.assertNotNull(eventHeader);

  }

  /**
   * No Context Tid.
   */
  @Test
  public void test_buildEventHeader_noTid() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .id("ext1").workerId("test")
        .taskAttributes(TaskAttributes.builder().runtimeAttributes(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(definitionDtls)
        .build();

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.clear();
    taskRequest.setTxnId("txn1");
    EventHeaderEntity eventHeader = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
        contextHandler, PublishEventType.EXTERNAL_TASK);
    Assert.assertNotNull(eventHeader);
    Assert.assertNotNull(eventHeader.getTid());

  }


  /**
   * With Context tid
   */
  @Test
  public void test_buildEventHeader_workflowStatetransition() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .id("ext1").workerId("worker1")
        .taskAttributes(TaskAttributes.builder().runtimeAttributes(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(definitionDtls)
        .build();

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.addKey(WASContextEnums.INTUIT_TID, "tid1");

    taskRequest.setTxnId("txn1");
    EventHeaderEntity eventHeader = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
        contextHandler, PublishEventType.WORKFLOW_TRANSITION_EVENTS);
    Assert.assertNotNull(eventHeader);

  }

  /**
   * No Context Tid.
   */
  @Test
  public void test_buildEventHeader_noTid_workflowStatetransition() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .id("ext1").workerId("worker1")
        .taskAttributes(TaskAttributes.builder().runtimeAttributes(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(definitionDtls)
        .build();

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.clear();
    taskRequest.setTxnId("txn1");
    EventHeaderEntity eventHeader = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
        contextHandler, PublishEventType.WORKFLOW_TRANSITION_EVENTS);
    Assert.assertNotNull(eventHeader);
    Assert.assertNotNull(eventHeader.getTid());
    Assert.assertEquals("ext1", eventHeader.getIdempotencyKey());
    Assert.assertEquals("ext1", eventHeader.getEntityId());
  }
  
  @Test
  public void test_buildEventHeader_noTid_externalTask() {
    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder()
        .id("ext1").workerId("worker1")
        .taskAttributes(TaskAttributes.builder().runtimeAttributes(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(definitionDtls)
        .build();

    WASContextHandler contextHandler = new MDCContextHandler();
    contextHandler.clear();
    taskRequest.setTxnId("txn1");
    EventHeaderEntity eventHeader = InternalEventsUtil.buildEventHeader(taskRequest, processDetails,
        contextHandler, PublishEventType.EXTERNAL_TASK);
    Assert.assertNotNull(eventHeader);
    Assert.assertNotNull(eventHeader.getTid());
    Assert.assertEquals("ext1", eventHeader.getIdempotencyKey());
    Assert.assertEquals("ext1:worker1", eventHeader.getEntityId());
  }


  @Test
  public void test_buildWorkflowStateTransitionEventPayload_start() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().processInstanceId("pid1")
        .taskType(TaskType.HUMAN_TASK)
        .status("created").workerId("worker1")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes)
        		.variables(runtimeAttributes).build())
        .command(TaskCommand.CREATE).id("ext1").build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    WorkflowStateTransitionEvents workflowStateTransitionEvents = InternalEventsUtil
        .buildTaskStateTransitionEventPayload(taskRequest, processDetails, Instant.now().toEpochMilli());
    Assert.assertNotNull(workflowStateTransitionEvents);
    Assert.assertEquals("created", workflowStateTransitionEvents.getEventType());
    Assert.assertEquals("created", workflowStateTransitionEvents.getStatus());
    Assert.assertEquals("activity",workflowStateTransitionEvents.getActivityMetadata().getScope());
    Assert.assertEquals("ext1", workflowStateTransitionEvents.getActivityMetadata()
    		.getExternalTaskId());
  }


  @Test
  public void test_buildWorkflowStateTransitionEventPayload_complete() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
    	.processInstanceId("pid1")
        .taskType(TaskType.HUMAN_TASK)
        .activityType(BpmnComponentType.SERVICE_TASK.getName())
        .txnId("txn1").status("completed")
        .workerId("worker1")
        .command(TaskCommand.COMPLETE)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes)
        		.variables(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    WorkflowStateTransitionEvents workflowStateTransitionEvents = InternalEventsUtil
        .buildTaskStateTransitionEventPayload(taskRequest, processDetails, Instant.now().toEpochMilli());
    Assert.assertNotNull(workflowStateTransitionEvents);
    Assert.assertEquals("txn1", workflowStateTransitionEvents.getTxnId());
    Assert.assertEquals(BpmnComponentType.SERVICE_TASK.getName(), workflowStateTransitionEvents.getActivityType());
    Assert.assertEquals("completed", workflowStateTransitionEvents.getStatus());
    Assert.assertEquals("completed", workflowStateTransitionEvents.getEventType());
    Assert.assertEquals("ext1", workflowStateTransitionEvents.getActivityMetadata()
    		.getExternalTaskId());
    Assert.assertEquals("activity",workflowStateTransitionEvents.getActivityMetadata().getScope());
  }


  @Test
  public void test_buildWorkflowStateTransitionEventPayload_noTimestamp() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("pid1")
        .taskType(TaskType.HUMAN_TASK)
        .activityType(BpmnComponentType.SERVICE_TASK.getName())
        .txnId("txn1").status("completed")
        .workerId("worker1")
        .command(TaskCommand.COMPLETE)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
            .runtimeAttributes(runtimeAttributes)
            .variables(runtimeAttributes).build())
        .build();

    TemplateDetails templateDtls = TemplateDetails.builder().offeringId("offer1")
        .templateName("template1").build();
    DefinitionDetails definitionDtls = DefinitionDetails.builder().templateDetails(templateDtls)
        .definitionId("def1").version(1).recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1").ownerId(1l)
        .definitionDetails(definitionDtls).build();

    WorkflowStateTransitionEvents workflowStateTransitionEvents = InternalEventsUtil
        .buildTaskStateTransitionEventPayload(taskRequest, processDetails, null);
    Assert.assertNotNull(workflowStateTransitionEvents);
    Assert.assertEquals("txn1", workflowStateTransitionEvents.getTxnId());
    Assert.assertEquals(BpmnComponentType.SERVICE_TASK.getName(), workflowStateTransitionEvents.getActivityType());
    Assert.assertEquals("completed", workflowStateTransitionEvents.getStatus());
    Assert.assertEquals("completed", workflowStateTransitionEvents.getEventType());
    Assert.assertEquals("ext1", workflowStateTransitionEvents.getActivityMetadata()
        .getExternalTaskId());
    Assert.assertEquals("activity",workflowStateTransitionEvents.getActivityMetadata().getScope());
    Assert.assertNotNull(workflowStateTransitionEvents.getTimestamp());
  }
  
  @Test
  public void test_commandToEventType(){
	  Assert.assertEquals(ActivityConstants.TASK_EVENT_TYPE_UPDATE, 
			  InternalEventsUtil.commandToEventType(TaskCommand.UPDATE));
	  Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, 
			  InternalEventsUtil.commandToEventType(TaskCommand.CREATE));
	  Assert.assertEquals(ActivityConstants.TASK_STATUS_FAILED, 
			  InternalEventsUtil.commandToEventType(TaskCommand.FAILED));
	  Assert.assertEquals(ActivityConstants.TASK_STATUS_COMPLETE, 
			  InternalEventsUtil.commandToEventType(TaskCommand.COMPLETE));
  }

}
