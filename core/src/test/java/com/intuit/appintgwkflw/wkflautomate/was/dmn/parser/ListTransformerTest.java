package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR> sbaliarsing
 */
public class ListTransformerTest {

  @InjectMocks private ListTransformer listTransformer;

  String parameterType = "LIST";
  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testDataType() {
    String dataType = listTransformer.getDataType();
    Assert.assertNotNull(dataType);
    Assert.assertEquals(String.class.getSimpleName(), dataType);
  }

  @Test
  public void testTransformRulesForDMN_LineItemAttrSupportedTrue() {
    String expressionWithoutAND = "CONTAINS test";
    String parameterName = "location";
    String expressionWithAND = "CONTAINS 1 && NOT_CONTAINS 2";
    String expressionWithAll = "CONTAINS ALL_Location";

    // check the case when AND operator is not present
    String exp = listTransformer.transformToDmnFriendlyExpression(expressionWithoutAND, parameterName, parameterType, true);
    Assert.assertEquals("containsAnyElement([\"test\"], location)", exp);
    // check the case when AND operator is present
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAND, parameterName, parameterType, true);
    Assert.assertEquals("containsAnyElement([\"1\"], location) "
        + "&& not(containsAnyElement([\"2\"], location))", exp);
    // check for select ALL operator
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAll, parameterName, parameterType, true);
    Assert.assertEquals("CONTAINS ALL_Location", exp);
    // check for invalid operation for ANY_MATCH in list
  }
  @Test
  public void testTransformRulesForDMN_LineItemAttrSupportedFalse() {
    String expressionWithoutAND = "CONTAINS test";
    String parameterName = "location";
    String expressionWithAND = "CONTAINS 1 && NOT_CONTAINS 2";
    String expressionWithAll = "CONTAINS ALL_Location";

    // check the case when AND operator is not present
    String exp = listTransformer.transformToDmnFriendlyExpression(expressionWithoutAND, parameterName, parameterType, false);
    Assert.assertEquals("location.equals(\"test\")", exp);
    // check the case when AND operator is present
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAND, parameterName, parameterType, false);
    Assert.assertEquals("location.equals(\"1\") && !location.equals(\"2\")", exp);
    // check for select ALL operator
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAll, parameterName, parameterType, false);
    Assert.assertEquals("CONTAINS ALL_Location", exp);
    // check for invalid operation for ANY_MATCH in list
  }

  @Test
  public void testTransformRulesForDMN_LineItemAttrNotSupported() {
    String expressionWithoutAND = "CONTAINS test";
    String parameterName = "location";
    String expressionWithAND = "CONTAINS 1 && NOT_CONTAINS 2";
    String expressionWithAll = "CONTAINS ALL_Location";

    // check the case when AND operator is not present
    String exp = listTransformer.transformToDmnFriendlyExpression(expressionWithoutAND, parameterName, parameterType, false);
    Assert.assertEquals("location.equals(\"test\")", exp);
    // check the case when AND operator is present
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAND, parameterName, parameterType, false);
    Assert.assertEquals("location.equals(\"1\") && !location.equals(\"2\")", exp);
    // check for select ALL operator
    exp = listTransformer.transformToDmnFriendlyExpression(expressionWithAll, parameterName, parameterType, false);
    Assert.assertEquals("CONTAINS ALL_Location", exp);
    // check for invalid operation for ANY_MATCH in list

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testInvalidOperation() {
    String invalidExpression = "CREATE 1";
    listTransformer.transformToDmnFriendlyExpression(invalidExpression, "Location", parameterType, false);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testInvalidOperationAnyMatch() {
    String invalidAnyMatchExpression = "ANY_MATCH test";
    String parameterName = "Location";
    listTransformer.transformToDmnFriendlyExpression(invalidAnyMatchExpression, parameterName, parameterType, false);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testInvalidExpression() {
    String invalidExpression = "CONTAINS";
    listTransformer.transformToDmnFriendlyExpression(invalidExpression, "Location", parameterType, false);
  }

  @Test
  public void testGetName() {
    DMNSupportedOperator dmnSupportedOperator = listTransformer.getName();
    Assert.assertNotNull(dmnSupportedOperator);
    Assert.assertEquals(DMNSupportedOperator.LIST, dmnSupportedOperator);
  }

  @Test
  public void testDefaultRule() {
    String expression = "CONTAINS test";
    String default_exp = listTransformer.defaultRule("xyz", expression);
    Assert.assertEquals(expression, default_exp);
  }

  @Test
  public void testTransformRulesForUI() {
    String expressionWithOr = "Location.equals(\"1\") || Location.equals(\"2\") ";
    String expressionWithAnd = "!Location.equals(\"1\") && !Location.equals(\"2\") ";
    String parameterName = "Location";

    String output = listTransformer.transformToUserFriendlyExpression(expressionWithOr, parameterName);
    Assert.assertEquals(output, "CONTAINS 1,2");

    output = listTransformer.transformToUserFriendlyExpression(expressionWithAnd, parameterName);
    Assert.assertEquals(output, "NOT_CONTAINS 1,2");
  }

  @Test
  public void testTransformRulesForUI_ContainsAnyElement() {
    String parameterName = "location";

    String expressionContains = "containsAnyElement([\"1\", \"2\"], location)";
    String output = listTransformer.transformToUserFriendlyExpression(expressionContains, parameterName);
    Assert.assertEquals(output, "CONTAINS 1,2");

    String expressionNotContains = "not(containsAnyElement([\"3\", \"4\"], location))";
    output = listTransformer.transformToUserFriendlyExpression(expressionNotContains, parameterName);
    Assert.assertEquals(output, "NOT_CONTAINS 3,4");
  }
}
