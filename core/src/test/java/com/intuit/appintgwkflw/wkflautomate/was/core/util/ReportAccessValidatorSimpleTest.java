package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ReportAccessValidationConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Simple test to verify the report access security fix works correctly.
 * This test focuses on the core security functionality without complex mocking.
 */
@ExtendWith(MockitoExtension.class)
class ReportAccessValidatorSimpleTest {

    @Mock
    private AccessVerifier accessVerifier;
    
    @Mock
    private WASContextHandler contextHandler;
    
    @Mock
    private IXPManager ixpManager;
    
    private ReportAccessValidator reportAccessValidator;
    
    private static final String TEST_REALM_ID = "123456789";
    private static final String TEST_REPORT_ID = "sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
    private static final String FEATURE_FLAG = "SBSEG-QBO-was-report-access-validation";
    
    @BeforeEach
    void setUp() {
        ReportAccessValidationConfig config = new ReportAccessValidationConfig();
        config.setEnabled(true);
        config.setFeatureFlag(FEATURE_FLAG);
        
        reportAccessValidator = new ReportAccessValidator(accessVerifier, contextHandler, ixpManager, config);
        
        // Default mocks - feature flag enabled
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(true))).thenReturn(true);
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(TEST_REALM_ID);
    }
    
    @Test
    void testSecurityFix_AuthorizedUser_AllowsAccess() {
        // Arrange - User has proper permissions
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert - Should allow access
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        // Verify that access was checked
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testSecurityFix_UnauthorizedUser_BlocksAccess() {
        // Arrange - User does NOT have permissions (simulating the attack scenario)
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        // Act & Assert - Should block access and throw security exception
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        // Verify the security error
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("User does not have permission to create workflows with report"));
        assertTrue(exception.getMessage().contains(TEST_REPORT_ID));
        
        // Verify that access was checked
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testSecurityFix_FeatureFlagDisabled_BypassesValidation() {
        // Arrange - Feature flag is disabled
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(true))).thenReturn(false);
        
        // Act & Assert - Should bypass validation when feature flag is off
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        // Verify that access verification was NOT called when feature is disabled
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testSecurityFix_ConditionalExpression_ExtractsAndValidatesReportId() {
        // Arrange - Conditional expression like in the attack payload
        String maliciousExpression = "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert - Should extract report ID and validate it
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(maliciousExpression));
        
        // Verify that the report ID was extracted and validated
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testSecurityFix_ConditionalExpression_BlocksMaliciousAccess() {
        // Arrange - Malicious conditional expression with unauthorized user
        String maliciousExpression = "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        // Act & Assert - Should block the malicious access attempt
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportIdsInExpression(maliciousExpression));
        
        // Verify the security error
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("User does not have permission to create workflows with report"));
        
        // Verify that access was checked
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testSecurityFix_EmptyExpression_DoesNothing() {
        // Act & Assert - Empty expressions should be ignored
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(""));
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(null));
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression("CONTAINS some_other_value"));
        
        // Verify that no access verification was performed
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testSecurityFix_ServiceException_FailsSecurely() {
        // Arrange - Access verifier throws exception (service unavailable)
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create"))
            .thenThrow(new RuntimeException("AuthZ service unavailable"));
        
        // Act & Assert - Should fail securely (deny access)
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        // Verify secure failure
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("Report access validation failed"));
        
        // Verify that access was attempted
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
}
