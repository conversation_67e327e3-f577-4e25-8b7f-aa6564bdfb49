package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.EndEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * Test class for DynamicBpmnImplicitElementsHelper class.
 */
@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnImplicitElementsHelperTest {

  @InjectMocks private DynamicBpmnImplicitElementsHelper dynamicBpmnImplicitElementsHelper;

  @Mock
  private DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  @Mock
  private EndEventFlowNodeProcessor endEventFlowNodeProcessor;

  private BpmnModelInstance bpmnModelInstance;

  private BpmnModelInstance baseTemplateBpmnModelInstance;
  @Before
  public void setUp() {

    baseTemplateBpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");

  }

  @Test
  public void testAddImplicitElementsFromBaseTemplateWhenTargetNodeIsNotPresent() {

    bpmnModelInstance = Bpmn.createExecutableProcess()
        .startEvent("startEvent")
        .businessRuleTask("decisionElement")
        .done();

    when(dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(any())).thenReturn(endEventFlowNodeProcessor);

    dynamicBpmnImplicitElementsHelper.addImplicitElementsFromBaseTemplate(bpmnModelInstance, baseTemplateBpmnModelInstance);

    Mockito.verify(endEventFlowNodeProcessor,times(1)).addImplicitEvents(any(),any(),any(),any(),any());
  }

  @Test
  public void testAddImplicitElementsFromBaseTemplateWhenTargetNodeIsPresent() {

    bpmnModelInstance = Bpmn.createExecutableProcess()
        .startEvent("startEvent")
        .businessRuleTask("decisionElement")
        .callActivity("sendForApproval")
        .endEvent("endEvent")
        .done();
    dynamicBpmnImplicitElementsHelper.addImplicitElementsFromBaseTemplate(bpmnModelInstance, baseTemplateBpmnModelInstance);

    Mockito.verify(endEventFlowNodeProcessor,Mockito.never()).addImplicitEvents(any(),any(),any(),any(),any());

    Assert.assertTrue(DynamicBpmnUtil.isSourceAndTargetNodesConnected(
        bpmnModelInstance.getModelElementById("decisionElement"),
        bpmnModelInstance.getModelElementById("endEvent")
    ));
  }

}
