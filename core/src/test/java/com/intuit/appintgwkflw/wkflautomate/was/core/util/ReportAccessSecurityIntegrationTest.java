package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ReportAccessValidationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WASContextEnums;
import com.intuit.ixp.manager.IXPManager;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Integration test to verify the complete report access security fix.
 * This test simulates the actual attack scenario and verifies it's blocked.
 */
@ExtendWith(MockitoExtension.class)
class ReportAccessSecurityIntegrationTest {

    @Mock
    private CustomWorkflowConfig customWorkflowConfig;
    
    @Mock
    private DefinitionServiceHelper definitionServiceHelper;
    
    @Mock
    private AccessVerifier accessVerifier;
    
    @Mock
    private WASContextHandler contextHandler;
    
    @Mock
    private IXPManager ixpManager;
    
    private ReportAccessValidationConfig config;
    private ReportAccessValidator reportAccessValidator;
    private FilterParameterExtractorUtil filterParameterExtractorUtil;
    
    private static final String TEST_REALM_ID = "123456789";
    private static final String MALICIOUS_REPORT_ID = "sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
    private static final String FEATURE_FLAG = "SBSEG-QBO-was-report-access-validation";
    
    @BeforeEach
    void setUp() {
        config = new ReportAccessValidationConfig();
        config.setEnabled(true);
        config.setFeatureFlag(FEATURE_FLAG);
        
        reportAccessValidator = new ReportAccessValidator(accessVerifier, contextHandler, ixpManager, config);
        filterParameterExtractorUtil = new FilterParameterExtractorUtil(
            customWorkflowConfig, definitionServiceHelper, reportAccessValidator);
        
        // Default mocks
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(TEST_REALM_ID);
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(TEST_REALM_ID), eq(true))).thenReturn(true);
    }
    
    @Test
    void testMaliciousWorkflowCreation_BlockedBySecurityFix() {
        // Arrange - Create a malicious workflow definition similar to the attack payload
        Definition maliciousDefinition = createMaliciousReportWorkflow();
        
        // Mock access verifier to deny access (simulating unauthorized user)
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        // Act & Assert - The security fix should block this attempt
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> filterParameterExtractorUtil.getFilterParameterDetails(maliciousDefinition, new HashMap<>(), "step1"));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("User does not have permission to create workflows with report"));
        
        // Verify that access verification was attempted
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testLegitimateWorkflowCreation_AllowedBySecurityFix() {
        // Arrange - Create a legitimate workflow definition
        Definition legitimateDefinition = createMaliciousReportWorkflow(); // Same structure but user has access
        
        // Mock access verifier to grant access (simulating authorized user)
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert - The security fix should allow this
        assertDoesNotThrow(() -> 
            filterParameterExtractorUtil.getFilterParameterDetails(legitimateDefinition, new HashMap<>(), "step1"));
        
        // Verify that access verification was performed
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testNonReportWorkflow_BypassesSecurityValidation() {
        // Arrange - Create a non-report workflow (e.g., invoice workflow)
        Definition invoiceDefinition = createInvoiceWorkflow();
        
        // Act & Assert - Should not trigger report validation
        assertDoesNotThrow(() -> 
            filterParameterExtractorUtil.getFilterParameterDetails(invoiceDefinition, new HashMap<>(), "step1"));
        
        // Verify that access verification was NOT called for non-report workflows
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testSecurityFixDisabled_AllowsAllAccess() {
        // Arrange - Disable the security fix via feature flag
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(TEST_REALM_ID), eq(true))).thenReturn(false);
        
        Definition maliciousDefinition = createMaliciousReportWorkflow();
        
        // Act & Assert - Should allow access when security fix is disabled
        assertDoesNotThrow(() -> 
            filterParameterExtractorUtil.getFilterParameterDetails(maliciousDefinition, new HashMap<>(), "step1"));
        
        // Verify that access verification was NOT called when feature is disabled
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testMultipleReportIds_ValidatesAll() {
        // Arrange - Create workflow with multiple report IDs (potential bulk exfiltration attempt)
        Definition multiReportDefinition = createMultiReportWorkflow();
        
        // Mock access verifier to deny access for the second report
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create"))
            .thenReturn(true)  // First report allowed
            .thenReturn(false); // Second report denied
        
        // Act & Assert - Should fail on the second report
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> filterParameterExtractorUtil.getFilterParameterDetails(multiReportDefinition, new HashMap<>(), "step1"));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        
        // Verify that both reports were checked
        verify(accessVerifier, times(2)).verifyUserAccess("customScheduledActions", "create");
    }
    
    /**
     * Creates a malicious workflow definition that attempts to access unauthorized reports.
     * This simulates the actual attack payload from the security vulnerability.
     */
    private Definition createMaliciousReportWorkflow() {
        Definition definition = new Definition();
        definition.setRecordType("report");
        definition.setDisplayName("TestReport");
        
        WorkflowStep workflowStep = new WorkflowStep();
        WorkflowStepCondition condition = new WorkflowStepCondition();
        
        RuleLine ruleLine = new RuleLine();
        RuleLine.Rule rule = new RuleLine.Rule();
        rule.setParameterName("ReportId");
        rule.setConditionalExpression("CONTAINS " + MALICIOUS_REPORT_ID);
        rule.setParameterType("LIST");
        
        ruleLine.setRules(Arrays.asList(rule));
        condition.setRuleLines(Arrays.asList(ruleLine));
        workflowStep.setWorkflowStepCondition(condition);
        
        definition.setWorkflowSteps(Arrays.asList(workflowStep));
        
        return definition;
    }
    
    private Definition createInvoiceWorkflow() {
        Definition definition = new Definition();
        definition.setRecordType("invoice");
        
        WorkflowStep workflowStep = new WorkflowStep();
        WorkflowStepCondition condition = new WorkflowStepCondition();
        
        RuleLine ruleLine = new RuleLine();
        RuleLine.Rule rule = new RuleLine.Rule();
        rule.setParameterName("Amount");
        rule.setConditionalExpression("GT 1000");
        
        ruleLine.setRules(Arrays.asList(rule));
        condition.setRuleLines(Arrays.asList(ruleLine));
        workflowStep.setWorkflowStepCondition(condition);
        
        definition.setWorkflowSteps(Arrays.asList(workflowStep));
        
        return definition;
    }
    
    private Definition createMultiReportWorkflow() {
        Definition definition = new Definition();
        definition.setRecordType("report");
        
        // First workflow step with first report ID
        WorkflowStep step1 = new WorkflowStep();
        WorkflowStepCondition condition1 = new WorkflowStepCondition();
        RuleLine ruleLine1 = new RuleLine();
        RuleLine.Rule rule1 = new RuleLine.Rule();
        rule1.setParameterName("ReportId");
        rule1.setConditionalExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
        ruleLine1.setRules(Arrays.asList(rule1));
        condition1.setRuleLines(Arrays.asList(ruleLine1));
        step1.setWorkflowStepCondition(condition1);
        
        // Second workflow step with second report ID
        WorkflowStep step2 = new WorkflowStep();
        WorkflowStepCondition condition2 = new WorkflowStepCondition();
        RuleLine ruleLine2 = new RuleLine();
        RuleLine.Rule rule2 = new RuleLine.Rule();
        rule2.setParameterName("ReportId");
        rule2.setConditionalExpression("CONTAINS sbg:3b29fe9g-e6f1-5d78-a031-4d4aecf340d3");
        ruleLine2.setRules(Arrays.asList(rule2));
        condition2.setRuleLines(Arrays.asList(ruleLine2));
        step2.setWorkflowStepCondition(condition2);
        
        definition.setWorkflowSteps(Arrays.asList(step1, step2));
        
        return definition;
    }
}
