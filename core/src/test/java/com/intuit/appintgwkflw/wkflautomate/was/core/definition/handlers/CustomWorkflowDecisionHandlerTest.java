package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import lombok.SneakyThrows;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Input;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.InputExpression;
import org.camunda.bpm.model.dmn.instance.Output;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.List;

/**
 * Unit Tests for Config
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
public class CustomWorkflowDecisionHandlerTest {
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  private CustomWorkflowConfig customWorkflowConfig;
  private Definition customDefinition;
  private Definition customDefinitionForOr;
  @MockBean  private FeatureFlagManager featureFlagManager;

  private static final String CUSTOM_WORKFLOW_DMN_SKELETON = "dmn/customWorkflow.dmn";
  private static final String CUSTOM_WORKFLOW_WITH_CUSTOM_FIELDS_DMN_SKELETON = "dmn/decision_invoiceReminderWithCustomFields.dmn";
  private  Definition definitionWithCustomField;
  private Definition definitionWithCustomFieldBill;
  private Definition definitionWithCustomFieldBillForOr;
  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    Definition definition = TestHelper.mockDefinitionEntity();
    Definition definitionForOr = TestHelper.mockDefinitionEntity();
    definition.setDisplayName("byo");
    definition.setName("customReminder");
    definitionForOr.setDisplayName("byo");
    definitionForOr.setName("customReminder");
    definition.setRecordType(RecordType.INVOICE.getRecordType().toLowerCase());
    definition
        .getWorkflowSteps()
        .get(0)
        .getWorkflowStepCondition()
        .setId(GlobalId.create("124", "decisionElement"));
    definitionForOr.setRecordType(RecordType.INVOICE.getRecordType().toLowerCase());
    definitionForOr
            .getWorkflowSteps()
            .get(0)
            .getWorkflowStepCondition()
            .setId(GlobalId.create("125", "decisionElement"));

    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(customWorkflowConfig, featureFlagManager);
    customDefinition = prepareCustomDefinitionData(definition);
    customDefinitionForOr = prepareCustomDefinitionDataForOr(definitionForOr);
    definitionWithCustomField = TestHelper.mockDefinitionEntityWithCustomFieldNew();
    definitionWithCustomFieldBill = TestHelper.mockDefinitionEntityWithCustomFieldBill();
    definitionWithCustomFieldBillForOr = TestHelper.mockDefinitionEntityWithCustomFieldBillForOr();
    MockitoAnnotations.initMocks(this);

  }

  private Definition prepareCustomDefinitionData(Definition definition) {
    List<RuleLine> ruleLines =
        definition.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines();
    RuleLine ruleLineDmn1 =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
            .rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnAmount"));
    RuleLine ruleLineDmn2 =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
            .rule(new RuleLine.Rule().conditionalExpression("BF 1").parameterName("CreateDate"));
    ruleLines.add(ruleLineDmn1);
    ruleLines.add(ruleLineDmn2);
    return definition;
  }
  private Definition prepareCustomDefinitionDataForOr(Definition customDefinitionForOr) {
    List<RuleLine> ruleLines =
            customDefinitionForOr.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines();
    RuleLine ruleLineDmn1 =
            new RuleLine()
                    .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
                    .rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnAmount").parameterType(FieldTypeEnum.DOUBLE));
    ruleLineDmn1.rule(new RuleLine.Rule().conditionalExpression("GT 0").parameterName("TxnBall").parameterType(FieldTypeEnum.DOUBLE));
    RuleLine ruleLineDmn2 =
            new RuleLine()
                    .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
                    .rule(new RuleLine.Rule().conditionalExpression("BF 1").parameterName("CreateDate").parameterType(FieldTypeEnum.STRING));
    ruleLineDmn2.rule(new RuleLine.Rule().conditionalExpression("GT 0").parameterName("TxnBall").parameterType(FieldTypeEnum.DOUBLE));
    RuleLine ruleLineDmn3 =
            new RuleLine()
                    .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
                    .rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnAmount").parameterType(FieldTypeEnum.DOUBLE));
    ruleLineDmn3.rule(new RuleLine.Rule().conditionalExpression("NOT_CONTAINS 123").parameterName("id").parameterType(FieldTypeEnum.STRING));
    RuleLine ruleLineDmn4 =
            new RuleLine()
                    .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
                    .rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnAmount").parameterType(FieldTypeEnum.DOUBLE));
    ruleLineDmn4.rule(new RuleLine.Rule().conditionalExpression("GT 500").parameterName("TxnBalanceAmount").parameterType(FieldTypeEnum.DOUBLE));
    ruleLines.add(ruleLineDmn1);
    ruleLines.add(ruleLineDmn2);
    ruleLines.add(ruleLineDmn3);
    ruleLines.add(ruleLineDmn4);
    return customDefinitionForOr;
  }
  @Test
  public void testDmnCreationSkeletonForOrforfeel() {
    DmnModelInstance modelInstance =
        Dmn.readModelFromStream(
            CustomWorkflowDecisionHandler.class
                .getClassLoader()
                .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON));
    customWorkflowDecisionHandler.createDecisionInputs(modelInstance, customDefinitionForOr, true);
    Assert.assertNotNull(modelInstance);
    Assert.assertNotNull(modelInstance.getDefinitions());
    Assert.assertNotNull(modelInstance.getModelElementsByType(Decision.class));
    Assert.assertEquals(1, modelInstance.getModelElementsByType(Decision.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(DecisionTable.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(OutputEntry.class).size());

    Collection<Input> inputElements = modelInstance.getModelElementsByType(Input.class);
    Assert.assertEquals(6, inputElements.size());
    Input inputElement = inputElements.stream().findFirst().get();
    // Label is same as parameter name
    Assert.assertEquals("TxnAmount", inputElement.getLabel());
    // variable name is same as name
    Assert.assertEquals("TxnAmount", inputElement.getCamundaInputVariable());
    InputExpression inputExpression =
        inputElement.getChildElementsByType(InputExpression.class).stream()
            .findFirst()
            .orElse(null);
    Assert.assertNotNull(inputExpression);
    Assert.assertEquals("double", inputExpression.getTypeRef());
    // variable name is same as id
    Assert.assertEquals("TxnAmount", inputExpression.getRawTextContent());

    Collection<Output> outputElements = modelInstance.getModelElementsByType(Output.class);
    Assert.assertEquals(1, outputElements.size());
    Output outputElement = outputElements.stream().findFirst().get();
    Assert.assertEquals("decisionResult", outputElement.getLabel());
    Assert.assertEquals("boolean", outputElement.getTypeRef());

    Collection<InputEntry> inputEntries = modelInstance.getModelElementsByType(InputEntry.class);
    Assert.assertEquals(6, inputEntries.size());
    InputEntry inputEntry = inputEntries.stream().findFirst().orElse(null);
    Assert.assertNotNull(inputEntry);
    Assert.assertEquals("", inputEntry.getTextContent());
  }
  @Test
  public void testDmnWithCustomFieldCreationSkeleton() {
    DmnModelInstance modelInstance =
        Dmn.readModelFromStream(
            CustomWorkflowDecisionHandlerTest.class
                .getClassLoader()
                .getResourceAsStream(CUSTOM_WORKFLOW_WITH_CUSTOM_FIELDS_DMN_SKELETON));

    customWorkflowDecisionHandler.createDecisionInputs(modelInstance, definitionWithCustomField, false);
    Assert.assertNotNull(modelInstance);
    Assert.assertNotNull(modelInstance.getDefinitions());
    Assert.assertNotNull(modelInstance.getModelElementsByType(Decision.class));
    Assert.assertEquals(1, modelInstance.getModelElementsByType(Decision.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(DecisionTable.class).size());
    Assert.assertEquals(3, modelInstance.getModelElementsByType(OutputEntry.class).size());

    Collection<Input> inputElements = modelInstance.getModelElementsByType(Input.class);
    Assert.assertEquals(4, inputElements.size());
    Input inputElement = inputElements.stream().findFirst().get();
    // Label is same as parameter name
    Assert.assertEquals("CF36000000000001557156", inputElement.getLabel());
    // variable name is same as name
    Assert.assertEquals("CF36000000000001557156", inputElement.getCamundaInputVariable());
    InputExpression inputExpression =
        inputElement.getChildElementsByType(InputExpression.class).stream()
            .findFirst()
            .orElse(null);
    Assert.assertNotNull(inputExpression);
    Assert.assertEquals("string", inputExpression.getTypeRef());
    // variable name is same as id
    Assert.assertEquals("${CF36000000000001557156}", inputExpression.getRawTextContent());

    Input inputElement2 = inputElements.stream().skip(1).findFirst().get();
    // Label is same as parameter name
    Assert.assertEquals("CF3600000000000155715", inputElement2.getLabel());
    // variable name is same as name
    Assert.assertEquals("CF3600000000000155715", inputElement2.getCamundaInputVariable());
    InputExpression inputExpression2 =
        inputElement2.getChildElementsByType(InputExpression.class).stream()
            .findFirst()
            .orElse(null);
    Assert.assertNotNull(inputExpression2);
    Assert.assertEquals("double", inputExpression2.getTypeRef());
    // variable name is same as id
    Assert.assertEquals("${CF3600000000000155715}", inputExpression2.getRawTextContent());

    Collection<Output> outputElements = modelInstance.getModelElementsByType(Output.class);
    Assert.assertEquals(1, outputElements.size());
    Output outputElement = outputElements.stream().findFirst().get();
    Assert.assertEquals("decisionResult", outputElement.getLabel());
    Assert.assertEquals("boolean", outputElement.getTypeRef());

    Collection<InputEntry> inputEntries = modelInstance.getModelElementsByType(InputEntry.class);
    Assert.assertEquals(6, inputEntries.size());
    InputEntry inputEntry = inputEntries.stream().findFirst().orElse(null);
    Assert.assertNotNull(inputEntry);
    Assert.assertEquals(
        "CF36000000000001557156.equals(\"1\") || CF36000000000001557156.equals(\"2\") || CF36000000000001557156.equals(\"3\")",
        inputEntry.getTextContent());
    InputEntry inputEntry2 = inputEntries.stream().skip(1).findFirst().orElse(null);
    Assert.assertNotNull(inputEntry2);
    Assert.assertEquals("CF3600000000000155715 >= 500", inputEntry2.getTextContent());
  }
  @Test
  public void testDmnWithCustomFieldCreationSkeletonForOr() {
    DmnModelInstance modelInstance =
            Dmn.readModelFromStream(
                    CustomWorkflowDecisionHandler.class
                            .getClassLoader()
                            .getResourceAsStream(CUSTOM_WORKFLOW_WITH_CUSTOM_FIELDS_DMN_SKELETON));

    customWorkflowDecisionHandler.createDecisionInputs(modelInstance, customDefinitionForOr, false);
    Assert.assertNotNull(modelInstance);
    Assert.assertNotNull(modelInstance.getDefinitions());
    Assert.assertNotNull(modelInstance.getModelElementsByType(Decision.class));
    Assert.assertEquals(1, modelInstance.getModelElementsByType(Decision.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(DecisionTable.class).size());

    Collection<Input> inputElements = modelInstance.getModelElementsByType(Input.class);
    Assert.assertEquals(8, inputElements.size());
    Input inputElement = inputElements.stream().findFirst().get();
    // Label is same as parameter name
    Assert.assertEquals("CF36000000000001557156", inputElement.getLabel());
    // variable name is same as name
    Assert.assertEquals("CF36000000000001557156", inputElement.getCamundaInputVariable());
    InputExpression inputExpression =
            inputElement.getChildElementsByType(InputExpression.class).stream()
                    .findFirst()
                    .orElse(null);
    Assert.assertNotNull(inputExpression);
    Assert.assertEquals("string", inputExpression.getTypeRef());
    // variable name is same as id
    Assert.assertEquals("${CF36000000000001557156}", inputExpression.getRawTextContent());

    Collection<Output> outputElements = modelInstance.getModelElementsByType(Output.class);
    Assert.assertEquals(1, outputElements.size());
    Output outputElement = outputElements.stream().findFirst().get();
    Assert.assertEquals("decisionResult", outputElement.getLabel());
    Assert.assertEquals("boolean", outputElement.getTypeRef());
  }

  @Test(expected = NullPointerException.class)
  public void testException() {
    CustomWorkflowConfig spy = Mockito.spy(customWorkflowConfig);
    Mockito.when(spy.getRecordObjForType("invoice")).thenThrow(NullPointerException.class);
    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(spy, featureFlagManager);
    customWorkflowDecisionHandler.createDecisionInputs(
        Dmn.readModelFromStream(
            CustomWorkflowDecisionHandlerTest.class
                .getClassLoader()
                .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON)),
        customDefinition, false);
  }
  @Test(expected = NullPointerException.class)
  public void testExceptionForOr() {
    CustomWorkflowConfig spy = Mockito.spy(customWorkflowConfig);
    Mockito.when(spy.getRecordObjForType("invoice")).thenThrow(NullPointerException.class);
    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(spy, featureFlagManager);
    customWorkflowDecisionHandler.createDecisionInputs(
            Dmn.readModelFromStream(
                    CustomWorkflowDecisionHandler.class
                            .getClassLoader()
                            .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON)),
            customDefinitionForOr, false);
  }

  @Test(expected = NullPointerException.class)
  public void testExceptionInvalidRecordType() {
    Mockito.when(customWorkflowConfig.getRecordObjForType("abc").getAttributes())
        .thenThrow(Exception.class);

    customWorkflowDecisionHandler.createDecisionInputs(
        Dmn.readModelFromStream(
            CustomWorkflowDecisionHandlerTest.class
                .getClassLoader()
                .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON)),
        customDefinition, false);
  }

  @Test
  public void testDmnWithCustomFieldBillCreationSkeleton() {
    DmnModelInstance modelInstance =
            Dmn.readModelFromStream(
                    CustomWorkflowDecisionHandlerTest.class
                            .getClassLoader()
                            .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON));

    customWorkflowDecisionHandler.createDecisionInputs(
            modelInstance, definitionWithCustomFieldBill, false);
    Assert.assertNotNull(modelInstance);
    Assert.assertNotNull(modelInstance.getDefinitions());
    Assert.assertNotNull(modelInstance.getModelElementsByType(Decision.class));
    Assert.assertEquals(1, modelInstance.getModelElementsByType(Decision.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(DecisionTable.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(OutputEntry.class).size());

    Collection<Input> inputElements = modelInstance.getModelElementsByType(Input.class);
    Assert.assertEquals(0, inputElements.size());
  }
  @Test
  public void testDmnWithCustomFieldBillCreationSkeletonForOr() {
    DmnModelInstance modelInstance =
            Dmn.readModelFromStream(
                    CustomWorkflowDecisionHandler.class
                            .getClassLoader()
                            .getResourceAsStream(CUSTOM_WORKFLOW_DMN_SKELETON));

    customWorkflowDecisionHandler.createDecisionInputs(
            modelInstance, definitionWithCustomFieldBillForOr, false);
    Assert.assertNotNull(modelInstance);
    Assert.assertNotNull(modelInstance.getDefinitions());
    Assert.assertNotNull(modelInstance.getModelElementsByType(Decision.class));
    Assert.assertEquals(1, modelInstance.getModelElementsByType(Decision.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(DecisionTable.class).size());
    Assert.assertEquals(1, modelInstance.getModelElementsByType(OutputEntry.class).size());

    Collection<Input> inputElements = modelInstance.getModelElementsByType(Input.class);
    Assert.assertEquals(0, inputElements.size());
  }

}
