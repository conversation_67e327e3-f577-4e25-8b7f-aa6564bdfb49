package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IS_RECURRING_ENABLED;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.Constants;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.FilterParameterExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CloseTaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProjectType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CreateMultiActionStepProcessorTest {

  @InjectMocks
  public CreateMultiActionStepProcessor createMultiActionStepProcessor;
  private DefinitionInstance definitionInstance = new DefinitionInstance();
  private Definition multiConditionDefinition;
  private CustomWorkflowConfig customWorkflowConfig;
  private BpmnModelInstance bpmnModelInstance;
  @Mock
  private WASContextHandler wasContextHandler;
  @Mock
  private FilterParameterExtractorUtil filterParameterExtractorUtil;

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  @Before
  public void setup() throws Exception {
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    customWorkflowConfig = TestHelper.loadCustomConfig();
    when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    definitionInstance =
        new DefinitionInstance(
            multiConditionDefinition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    definitionInstance.setPlaceholderValue(
        multiStepPlaceholderExtractor.extractPlaceholderValue(
            definitionInstance));
  }

  @Test
  public void testPlaceholderSubstitution() {
//   find active workflow step
    LinkedList<String> activities = new LinkedList<>();
    activities.add("11");
    Map<String, String> outgoing = createMultiActionStepProcessor.processWorkflowStep("test-1",
        multiConditionDefinition.getWorkflowSteps().get(1), definitionInstance,
        activities, new HashSet<>());
    Assert.assertNotNull(
        definitionInstance.getActivityInstanceMap().get("test-1").getUserAttributes());
    Assert.assertNotNull(outgoing);
  }

  @Test
  public void testPlaceholderSubstitutionForEssBasedWorkflows() {
    Definition singleStepReminderWithCompositeStepDefinition =
        TestHelper.mockSingleStepRecurringReminderDefinitionEntity();

    singleStepReminderWithCompositeStepDefinition.getWorkflowSteps()
        .get(1).getActionGroup().getAction().getParameters().add(
            new InputParameter().parameterName(FILTER_CLOSE_TASK_CONDITIONS).fieldValue(CloseTaskType.TXN_SENT.name()));

    definitionInstance =
        new DefinitionInstance(
            singleStepReminderWithCompositeStepDefinition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    definitionInstance.setPlaceholderValue(
        multiStepPlaceholderExtractor.extractPlaceholderValue(definitionInstance));

    Map<String, HandlerDetails.ParameterDetails> filterParameterDetailsMap = new HashMap<>();
    HandlerDetails.ParameterDetails isRecurring = new HandlerDetails.ParameterDetails();
    isRecurring.setFieldValue(Collections.singletonList("true"));
    filterParameterDetailsMap.put(IS_RECURRING_ENABLED, isRecurring);
    when(filterParameterExtractorUtil.getFilterParameterDetails(any(), any(), any()))
        .thenReturn(filterParameterDetailsMap);

    LinkedList<String> activities = new LinkedList<>();
    activities.add("11");
    Map<String, String> outgoing = createMultiActionStepProcessor.processWorkflowStep("test-1",
        singleStepReminderWithCompositeStepDefinition.getWorkflowSteps().get(1), definitionInstance,
        activities, new HashSet<>());
    Assert.assertNotNull(
        definitionInstance.getActivityInstanceMap().get("test-1").getUserAttributes());
    Assert.assertNotNull(
        definitionInstance.getActivityInstanceMap().get("test-1").getUserAttributes());
    Assert.assertNotNull(outgoing);

  }

  @Test
  public void testPlaceholderSubstitutionForProjectTypeAndTaskType() {
    Definition singleStepReminderWithCompositeStepDefinition =
        TestHelper.mockSingleStepRecurringReminderDefinitionEntity();

    singleStepReminderWithCompositeStepDefinition.getWorkflowSteps()
        .get(1).getActionGroup().getAction().getParameters().add(
            new InputParameter().parameterName(FILTER_CLOSE_TASK_CONDITIONS).fieldValue(CloseTaskType.TXN_SENT.name()));

    definitionInstance =
        new DefinitionInstance(
            singleStepReminderWithCompositeStepDefinition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    definitionInstance.setPlaceholderValue(
        multiStepPlaceholderExtractor.extractPlaceholderValue(definitionInstance));

    LinkedList<String> activities = new LinkedList<>();
    activities.add("sendForReminder-1");

    createMultiActionStepProcessor.processWorkflowStep("test-1",
        singleStepReminderWithCompositeStepDefinition.getWorkflowSteps().get(1), definitionInstance,
        activities, new HashSet<>());

    Assert.assertNotNull(definitionInstance.getActivityInstanceMap().get("test-1").getUserAttributes());
    Assert.assertNotNull(definitionInstance.getActivityInstanceMap().get("test-1").getChildActivityInstances());

    HashMap<String, Object> parentParameters = (HashMap<String, Object>) definitionInstance.getActivityInstanceMap().get("test-1").getUserAttributes().get("parameters");
    Assert.assertNotNull(parentParameters);
    Assert.assertEquals(parentParameters.size(), 4);

    Assert.assertEquals(definitionInstance.getActivityInstanceMap().get("test-1").getChildActivityInstances().size(), 1);

    HashMap<String, Object> childTaskParameters = (HashMap<String, Object>) definitionInstance.getActivityInstanceMap().get("test-1").getChildActivityInstances().get("createTask").getUserAttributes().get("parameters");
    Assert.assertNotNull(childTaskParameters);
    Assert.assertEquals(childTaskParameters.size(), 5);

    Map<String, Object> projectType = (Map<String, Object>) childTaskParameters.get(Constants.PROJECT_TYPE);
    Assert.assertNotNull(projectType);
    Assert.assertEquals(projectType.get("fieldValue"), Collections.singletonList(ProjectType.QB_INVOICE_UNSENT_REMINDER.name()));

    Map<String, Object> taskType = (Map<String, Object>) childTaskParameters.get(Constants.TASK_TYPE);
    Assert.assertNotNull(taskType);
    Assert.assertEquals(taskType.get("fieldValue"), Collections.singletonList(ProjectType.QB_INVOICE_UNSENT_REMINDER.getTaskType().name()));

  }

  @Test(expected = WorkflowGeneralException.class)
  public void testPlaceholderSubstitutionWithoutCloseTask() {
    Definition singleStepReminderWithCompositeStepDefinition =
        TestHelper.mockSingleStepRecurringReminderDefinitionEntity();

    definitionInstance =
        new DefinitionInstance(
            singleStepReminderWithCompositeStepDefinition,
            bpmnModelInstance,
            Collections.emptyList(),
            TemplateDetails.builder().templateCategory(TemplateCategory.CUSTOM.name()).build());

    MultiStepPlaceholderExtractor multiStepPlaceholderExtractor =
        new MultiStepPlaceholderExtractor(customWorkflowConfig, wasContextHandler);
    definitionInstance.setPlaceholderValue(
        multiStepPlaceholderExtractor.extractPlaceholderValue(definitionInstance));

    LinkedList<String> activities = new LinkedList<>();
    activities.add("sendForReminder-1");

    createMultiActionStepProcessor.processWorkflowStep("test-1",
        singleStepReminderWithCompositeStepDefinition.getWorkflowSteps().get(1), definitionInstance,
        activities, new HashSet<>());
  }

}
