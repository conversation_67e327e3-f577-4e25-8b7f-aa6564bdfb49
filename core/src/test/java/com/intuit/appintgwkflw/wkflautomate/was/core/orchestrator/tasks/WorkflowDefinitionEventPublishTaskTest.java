package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowDefinitionEventPublishTaskTest {

  private WorkflowDefinitionEventPublishTask workflowDefinitionEventPublishTask;

  @Mock private EventPublisherCapability eventPublisherCapability;
  @Mock private EventPublisherUtil eventPublisherUtil;

  @Before
  public void setUp() {
    workflowDefinitionEventPublishTask =
        new WorkflowDefinitionEventPublishTask(eventPublisherCapability, eventPublisherUtil);
  }

  @Test
  public void testExecute() {
    State state = new State();
    TemplateDetails bpmnTemplateDetails = new TemplateDetails();
    bpmnTemplateDetails.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, bpmnTemplateDetails);
    state.addValue(WorkflowConstants.INTUIT_TID, "intuit_tid");

    try {
      workflowDefinitionEventPublishTask.execute(state);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testExecuteTemplateIdMissing() {
    State state = new State();
    TemplateDetails bpmnTemplateDetails = new TemplateDetails();
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, bpmnTemplateDetails);

    try {
      state = workflowDefinitionEventPublishTask.execute(state);

      Assert.assertNotNull(
          state.getAll().get(AsyncTaskConstants.WORKFLOW_DEF_EVENT_PUBLISH_EXCEPTION));
    } catch (WorkflowEventException e) {
      Assert.assertNotNull(e);
    }
  }

  @Test
  public void testExecuteGenericException() {
    State state = new State();
    TemplateDetails bpmnTemplateDetails = new TemplateDetails();
    bpmnTemplateDetails.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, bpmnTemplateDetails);
    state.addValue(WorkflowConstants.INTUIT_TID, "intuit_tid");

    Mockito.doThrow(new RuntimeException())
        .when(eventPublisherCapability)
        .publish(Mockito.any(), Mockito.any());

    try {
      state = workflowDefinitionEventPublishTask.execute(state);

      Assert.assertNotNull(
          state.getAll().get(AsyncTaskConstants.WORKFLOW_DEF_EVENT_PUBLISH_EXCEPTION));
    } catch (Exception e) {
      Assert.assertNotNull(e);
    }
  }
}
