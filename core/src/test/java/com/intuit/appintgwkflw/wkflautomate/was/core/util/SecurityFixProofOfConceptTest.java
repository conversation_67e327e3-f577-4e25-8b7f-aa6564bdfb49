package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ReportAccessValidationConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Proof of concept test to demonstrate our security fix works.
 * This directly tests the ReportAccessValidator without complex dependencies.
 */
@ExtendWith(MockitoExtension.class)
class SecurityFixProofOfConceptTest {

    @Mock
    private AccessVerifier accessVerifier;
    
    @Mock
    private WASContextHandler contextHandler;
    
    @Mock
    private IXPManager ixpManager;
    
    @Test
    void proofOfConcept_SecurityFixBlocksUnauthorizedAccess() {
        System.out.println("\n🔒 SECURITY FIX PROOF OF CONCEPT 🔒");
        
        // Setup
        ReportAccessValidationConfig config = new ReportAccessValidationConfig();
        config.setEnabled(true);
        config.setFeatureFlag("SBSEG-QBO-was-report-access-validation");
        
        ReportAccessValidator validator = new ReportAccessValidator(
            accessVerifier, contextHandler, ixpManager, config);
        
        // Mock setup
        when(ixpManager.getBoolean(eq("SBSEG-QBO-was-report-access-validation"), eq(true))).thenReturn(true);
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123456789");
        
        String maliciousReportId = "sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
        String attackPayload = "CONTAINS " + maliciousReportId;
        
        System.out.println("📋 Testing attack scenario:");
        System.out.println("   Report ID: " + maliciousReportId);
        System.out.println("   Attack payload: " + attackPayload);
        
        // Test 1: Unauthorized user (simulating terminated employee)
        System.out.println("\n🚫 Test 1: Unauthorized user attempts access");
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        try {
            validator.validateReportIdsInExpression(attackPayload);
            fail("❌ SECURITY BREACH: Unauthorized access was allowed!");
        } catch (WorkflowGeneralException ex) {
            System.out.println("   ✅ SUCCESS: Unauthorized access blocked!");
            System.out.println("   📝 Error: " + ex.getMessage());
            assertTrue(ex.getMessage().contains("User does not have permission"));
        }
        
        // Test 2: Authorized user
        System.out.println("\n✅ Test 2: Authorized user attempts access");
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        try {
            validator.validateReportIdsInExpression(attackPayload);
            System.out.println("   ✅ SUCCESS: Authorized user can access report");
        } catch (Exception ex) {
            fail("❌ FAILURE: Authorized user was blocked: " + ex.getMessage());
        }
        
        // Test 3: Feature flag disabled (bypass mode)
        System.out.println("\n🏳️ Test 3: Feature flag disabled (bypass mode)");
        when(ixpManager.getBoolean(eq("SBSEG-QBO-was-report-access-validation"), eq(true))).thenReturn(false);
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        try {
            validator.validateReportIdsInExpression(attackPayload);
            System.out.println("   ✅ SUCCESS: Validation bypassed when feature flag disabled");
        } catch (Exception ex) {
            fail("❌ FAILURE: Validation should be bypassed when feature flag is disabled");
        }
        
        // Test 4: Multiple report IDs
        System.out.println("\n📊 Test 4: Multiple report IDs in expression");
        when(ixpManager.getBoolean(eq("SBSEG-QBO-was-report-access-validation"), eq(true))).thenReturn(true);
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        String multiReportPayload = "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2 OR sbg:3b29fe9g-e6f1-5d78-a031-4d4aecf340d3";
        
        try {
            validator.validateReportIdsInExpression(multiReportPayload);
            fail("❌ SECURITY BREACH: Multiple unauthorized report access was allowed!");
        } catch (WorkflowGeneralException ex) {
            System.out.println("   ✅ SUCCESS: Multiple unauthorized report access blocked!");
        }
        
        System.out.println("\n🎉 PROOF OF CONCEPT COMPLETE 🎉");
        System.out.println("✅ Security fix successfully prevents unauthorized report access");
        System.out.println("✅ Authorized users can still access reports normally");
        System.out.println("✅ Feature flag provides runtime control");
        System.out.println("✅ Multiple report IDs are properly validated");
        
        // Verify interactions
        verify(accessVerifier, atLeast(3)).verifyUserAccess("customScheduledActions", "create");
        verify(ixpManager, atLeast(3)).getBoolean(eq("SBSEG-QBO-was-report-access-validation"), eq(true));
    }
    
    @Test
    void proofOfConcept_RegexExtractionWorks() {
        System.out.println("\n🔍 REGEX EXTRACTION TEST 🔍");
        
        ReportAccessValidationConfig config = new ReportAccessValidationConfig();
        config.setEnabled(true);
        config.setFeatureFlag("SBSEG-QBO-was-report-access-validation");
        
        ReportAccessValidator validator = new ReportAccessValidator(
            accessVerifier, contextHandler, ixpManager, config);
        
        when(ixpManager.getBoolean(eq("SBSEG-QBO-was-report-access-validation"), eq(true))).thenReturn(true);
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("123456789");
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Test various report ID formats
        String[] testExpressions = {
            "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2",
            "EQUALS sbg:12345678-1234-1234-1234-123456789012",
            "IN [sbg:abcdefgh-abcd-abcd-abcd-abcdefghijkl]",
            "NOT_CONTAINS some_other_value", // Should not trigger validation
            ""  // Empty should not trigger validation
        };
        
        for (String expression : testExpressions) {
            System.out.println("Testing: " + expression);
            try {
                validator.validateReportIdsInExpression(expression);
                System.out.println("   ✅ Processed successfully");
            } catch (Exception ex) {
                System.out.println("   ❌ Error: " + ex.getMessage());
            }
        }
        
        System.out.println("🎯 Regex extraction test complete");
    }
}
