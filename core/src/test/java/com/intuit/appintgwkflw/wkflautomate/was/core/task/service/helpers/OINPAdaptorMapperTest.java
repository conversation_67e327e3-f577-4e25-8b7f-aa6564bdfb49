package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.EventMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class OINPAdaptorMapperTest {

  @Test
  public void testMappingWithIdempotencyKey(){
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setIdempotencyKey("key");
    notificationTask.setId("id");
    OINPEventRequest oinpEventRequest = OINPAdaptorMapper.mapOINPRequestData(notificationTask);
    Assert.assertEquals("key", oinpEventRequest.getSourceObjectId());
  }

  @Test
  public void testMappingWithoutIdempotencyKey(){
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setId("id");
    OINPEventRequest oinpEventRequest = OINPAdaptorMapper.mapOINPRequestData(notificationTask);
    Assert.assertEquals("id", oinpEventRequest.getSourceObjectId());
  }

  @Test
  public void testBatchMapping(){
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setId("id");
    BatchNotificationTask batchNotificationTask = new BatchNotificationTask();
    batchNotificationTask.setNotificationTaskList(Collections.singletonList(notificationTask));
    List<OINPEventRequest> oinpEventRequest =
        OINPAdaptorMapper.mapOINPRequestData(batchNotificationTask);
    Assert.assertEquals(1, oinpEventRequest.size());
    Assert.assertEquals("id", oinpEventRequest.get(0).getSourceObjectId());
  }

  @Test
  public void testWithMetaDataNotNull(){
    NotificationTask notificationTask = new NotificationTask();
    notificationTask.setId("id");
    String eventMetaDataJson = "{\"authId\": 123}";
    EventMetaData eventMetaData = ObjectConverter.fromJson(eventMetaDataJson, EventMetaData.class);
    Assert.assertNotNull(eventMetaData);
    notificationTask.setNotificationMetaData(eventMetaData);
    OINPEventRequest oinpEventRequest = OINPAdaptorMapper.mapOINPRequestData(notificationTask);
    Assert.assertEquals("id", oinpEventRequest.getSourceObjectId());
    Assert.assertEquals(123L, oinpEventRequest.getEventMetaData().getAuthId().longValue());
    eventMetaDataJson = ObjectConverter.toJson(eventMetaData);
    Assert.assertNotNull(eventMetaDataJson);
    Assert.assertTrue(eventMetaDataJson.contains("was-notify"));
  }
}
