package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionDisableCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

/** <AUTHOR> */
public class DefinitionDisableCommandTest {

  @InjectMocks private DefinitionDisableCommand command;

  @Mock private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  @Mock private TriggerDetailsRepository triggerDetailsRepository;

  @Mock private EventScheduleHelper eventScheduleHelper;

  @Mock
  private FeatureManager featureManager;

  @Mock private SchedulingService schedulingService;

  private UpdateEventScheduleTask updateStatusEventSchedulerTask;
  private UpdateEventSchedulingTask updateEventSchedulingTask;

  private Definition definition = TestHelper.mockDefinitionEntity();
  private static final String REALM_ID = "12345";
  private Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  private TemplateDetails bpmnTemplateDetail = TestHelper.mockTemplateDetails("TEST");

  @Captor private ArgumentCaptor<CorrelateAllMessage> argumentCaptor;

  @Captor private ArgumentCaptor<CorrelateAllMessageAsync> asyncArgumentCaptor;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    when(triggerDetailsRepository.findByTemplateDetails(any())).thenReturn(Optional.empty());
    when(triggerDetailsRepository.findByTemplateDetails(bpmnTemplateDetail))
        .thenReturn(
            Optional.of(
                Collections.singletonList(
                    TriggerDetails.builder().triggerName("deleted_voided_disable").build())));
    updateStatusEventSchedulerTask =
            mock(UpdateEventScheduleTask.class);
    updateEventSchedulingTask = mock(UpdateEventSchedulingTask.class);
    when(updateStatusEventSchedulerTask.execute(any()))
            .thenReturn(mock(State.class));
    when(updateEventSchedulingTask.execute(any())).thenReturn(mock(State.class));
    when(eventScheduleHelper.prepareScheduleStatusUpdateTask(any(), any())).thenReturn(null);
    when(schedulingService.isEnabled((DefinitionDetails) any(), Mockito.any())).thenReturn(false);
  }

  @Test
  public void testDisableNoInflightProcess() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Mockito.verify(updateStatusEventSchedulerTask, times(0)).execute(any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisabledWithRunTimeException() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    when(bpmnEngineRunTimeServiceRest.correlateAllMessage(any()))
        .thenThrow(new WorkflowGeneralException(new RuntimeException("Error")));
    try {
      // it is async if exception is thrown it is logged
      command.execute(definitionInstance, REALM_ID);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Assert.assertEquals("Error", e.getCause().getMessage());
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(any()))
        .thenThrow(new WorkflowGeneralException(new RuntimeException("Error")));
    try {
      // it is async if exception is thrown it is logged
      command.execute(definitionInstance, REALM_ID);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Assert.assertEquals("Error", e.getCause().getMessage());
    }
  }

  @Test
  public void testDisableWithInflightProcess() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(Mockito.any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Mockito.verify(updateStatusEventSchedulerTask, times(0)).execute(Mockito.any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(Mockito.any()))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisableWithInflightProcessWithoutTriggerDetails() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(Mockito.any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(
            Optional.of(
                Collections.singletonList(
                    TriggerDetails.builder().triggerName("delete_event").build())));
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(Mockito.any()))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisableWithInflightProcessWithoutTriggerDetailsEmpty() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(Mockito.any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    when(triggerDetailsRepository.findByTemplateDetails(any()))
        .thenReturn(Optional.ofNullable(null));
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(Mockito.any()))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisableWithInflightProcessWithUpdatedDefinitionVersion() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithNonZeroVersion(
            definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setDefinitionDetailsList(Collections.singletonList(definitionDetail));
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(Mockito.any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(Mockito.any()))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testCustomDisable() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(
            definition, TestHelper.mockTemplateDetails("CUSTOM"), authorization
        );
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(argumentCaptor.capture());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys());
      Assert.assertNotNull(argumentCaptor.getValue().getCorrelationKeys().get(CorrelationKeysEnum.DEFINITION_KEY.getName()));
      Assert.assertEquals(argumentCaptor.getValue().getMessageName(), CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);

    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest)
          .correlateAllMessageAsync(asyncArgumentCaptor.capture());
      Assert.assertNotNull(asyncArgumentCaptor.getValue().getProcessInstanceQuery());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getName(),
          CorrelationKeysEnum.DEFINITION_KEY.getName());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getProcessInstanceQuery().getVariables().get(0).getValue(),
          definitionDetail.getDefinitionKey());
      Assert.assertEquals(
          asyncArgumentCaptor.getValue().getMessageName(),
          CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testTriggerNotFoundForNonCustomDefinitionDisable() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    TemplateDetails sampleTemplateDetails = TestHelper.mockTemplateDetails("SAMPLE");
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, sampleTemplateDetails, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition,  null, null, null);
    List<DefinitionDetails> list = new ArrayList<>();
    list.add(definitionDetail);
    definitionInstance.setDefinitionDetailsList(list);
    definitionInstance.setDefinitionDetails(definitionDetail);

    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
    } catch (Exception e) {
      e.printStackTrace();
      Assert.fail();
    }
  }

  @Test
  public void testDisableDefinitionWithUpdateEventScheduleTaskExecuted() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateStatusEventSchedulerTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Mockito.verify(updateStatusEventSchedulerTask, times(1)).execute(any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
        .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(any())).thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisableDefinitionWithUpdateEventScheduleTaskExecutedScheduling() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateEventSchedulingTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Mockito.verify(updateEventSchedulingTask, times(1)).execute(any());
    } catch (Exception e) {
      Assert.fail();
    }

    when(featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, REALM_ID))
            .thenReturn(true);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateAllMessageAsync(any())).thenReturn(true);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessageAsync(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }

  @Test
  public void testDisableDefinitionWithUpdateEventScheduleTaskExecutedMigration() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
            TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    definitionInstance.setDefinitionDetails(definitionDetail);
    definitionInstance.setWorkflowId("wkid");
    ProcessDetails pDetails = new ProcessDetails();
    pDetails.setProcessId("pId");
    List<ProcessDetails> processDetails = Collections.singletonList(pDetails);
    definitionInstance.setProcessDetails(processDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(any())).thenReturn(true);
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.DISABLED);
    List<Task> updateEventScheduleTasks = new ArrayList<>();
    updateEventScheduleTasks.add(updateStatusEventSchedulerTask);
    updateEventScheduleTasks.add(updateEventSchedulingTask);
    Mockito.when(eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(any(), any(), any(), any())).thenReturn(updateEventScheduleTasks);
    try {
      command.execute(definitionInstance, REALM_ID);
      Mockito.verify(bpmnEngineRunTimeServiceRest).correlateAllMessage(any());
      Mockito.verify(updateEventSchedulingTask, times(1)).execute(any());
      Mockito.verify(updateStatusEventSchedulerTask, times(1)).execute(any());
    } catch (Exception e) {
      Assert.fail();
    }
  }
}
