package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ReportAccessValidationConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReportAccessValidatorTest {

    @Mock
    private AccessVerifier accessVerifier;
    
    @Mock
    private WASContextHandler contextHandler;
    
    @Mock
    private IXPManager ixpManager;
    
    private ReportAccessValidator reportAccessValidator;
    
    private static final String TEST_REALM_ID = "123456789";
    private static final String TEST_REPORT_ID = "sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
    private static final String FEATURE_FLAG = "SBSEG-QBO-was-report-access-validation";
    
    @BeforeEach
    void setUp() {
        ReportAccessValidationConfig config = new ReportAccessValidationConfig();
        config.setEnabled(true);
        config.setFeatureFlag(FEATURE_FLAG);

        reportAccessValidator = new ReportAccessValidator(accessVerifier, contextHandler, ixpManager, config);

        // Default mocks
        when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(TEST_REALM_ID);
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(true))).thenReturn(true);
    }
    
    @Test
    void testValidateReportAccess_Success() {
        // Arrange
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testValidateReportAccess_AccessDenied() {
        // Arrange
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(false);
        
        // Act & Assert
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("User does not have permission to create workflows with report"));
    }
    
    @Test
    void testValidateReportAccess_FeatureFlagDisabled() {
        // Arrange
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(true))).thenReturn(false);
        
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        // Verify that access verification is not called when feature flag is disabled
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testValidateReportAccess_BlankReportId() {
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(""));
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(null));
        
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testValidateReportIdsInExpression_Success() {
        // Arrange
        String conditionalExpression = "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2";
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(conditionalExpression));
        
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testValidateReportIdsInExpression_MultipleReportIds() {
        // Arrange
        String conditionalExpression = "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2 OR sbg:3b29fe9g-e6f1-5d78-a031-4d4aecf340d3";
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(conditionalExpression));
        
        // Should validate both report IDs
        verify(accessVerifier, times(2)).verifyUserAccess("customScheduledActions", "create");
    }
    
    @Test
    void testValidateReportIdsInExpression_NoReportIds() {
        // Arrange
        String conditionalExpression = "CONTAINS some_other_value";
        
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(conditionalExpression));
        
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testValidateReportIdsInExpression_BlankExpression() {
        // Act & Assert
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(""));
        assertDoesNotThrow(() -> reportAccessValidator.validateReportIdsInExpression(null));
        
        verify(accessVerifier, never()).verifyUserAccess(anyString(), anyString());
    }
    
    @Test
    void testValidateReportAccess_AccessVerifierException() {
        // Arrange
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create"))
            .thenThrow(new RuntimeException("AuthZ service unavailable"));
        
        // Act & Assert
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("Report access validation failed"));
    }
    
    @Test
    void testValidateReportAccess_ContextHandlerException() {
        // Arrange
        when(contextHandler.get(WASContextEnums.OWNER_ID))
            .thenThrow(new RuntimeException("Context not available"));
        
        // Act & Assert
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
        assertTrue(exception.getMessage().contains("Unable to validate report realm"));
    }
    
    @Test
    void testValidateReportAccess_IXPManagerException() {
        // Arrange
        when(ixpManager.getBoolean(eq(FEATURE_FLAG), eq(true)))
            .thenThrow(new RuntimeException("IXP service unavailable"));
        when(accessVerifier.verifyUserAccess("customScheduledActions", "create")).thenReturn(true);
        
        // Act & Assert - Should default to enabled and continue validation
        assertDoesNotThrow(() -> reportAccessValidator.validateReportAccess(TEST_REPORT_ID));
        
        verify(accessVerifier).verifyUserAccess("customScheduledActions", "create");
    }
}
