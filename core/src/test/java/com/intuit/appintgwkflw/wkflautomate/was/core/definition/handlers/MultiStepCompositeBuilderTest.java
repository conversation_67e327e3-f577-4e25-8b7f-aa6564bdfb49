package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IS_RECURRING_ENABLED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.RECUR_FREQUENCY;
import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepCompositeBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepCompositeBuilderTest {

  MultiStepCompositeBuilder multiStepCompositeBuilder;

  private MultiStepConditionBuilder multiStepConditionBuilder;

  private MultiStepActionBuilder multiStepActionBuilder;

  @Mock
  private WASContextHandler wasContextHandler;
  @Mock
  private TemplateActionBuilder actionBuilder;
  @Mock
  private TemplateConditionBuilder conditionBuilder;

  private CustomWorkflowConfig customWorkflowConfig;

  @Before
  public void setUp() throws Exception {
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    multiStepConditionBuilder = new MultiStepConditionBuilder(wasContextHandler, conditionBuilder);
    multiStepActionBuilder = new MultiStepActionBuilder(wasContextHandler, actionBuilder);
    multiStepCompositeBuilder = new MultiStepCompositeBuilder(multiStepConditionBuilder,
        multiStepActionBuilder);
  }

  @Test
  public void testProcessWorkflowStep() {
    Steps configStep = new Steps();
    configStep.setStepId(2);
    configStep.setStepType(StepTypeEnum.CONDITION.name());
    Next yesPath = new Next();
    yesPath.setType(StepTypeEnum.ACTION.value());
    yesPath.setStepId("3");
    yesPath.setLabel(NextLabelEnum.YES.value());
    configStep.setNexts(Arrays.asList(yesPath));

    Attribute txnAttr = new Attribute();
    txnAttr.setName("TxnDueDays");
    txnAttr.setId("txnDueDays");
    txnAttr.setType("days");
    txnAttr.setDefaultValue("3");
    txnAttr.setDefaultOperator("BF");
    configStep.setAttributes(Arrays.asList(txnAttr));

    Map<Integer, Steps> configStepIdMap = new HashMap<>();
    // Creating next action step
    Steps nextStep = new Steps();
    nextStep.setStepId(3);
    nextStep.setAction(WorkflowConstants.SEND_FOR_REMINDER_ACTION_ID);
    nextStep.setStepType(StepTypeEnum.ACTION.name());

    Attribute isRecurring = new Attribute();
    isRecurring.setId(IS_RECURRING_ENABLED);
    isRecurring.setDefaultValue("true");

    Attribute filterCloseTaskConditions = new Attribute();
    filterCloseTaskConditions.setId(FILTER_CLOSE_TASK_CONDITIONS);
    filterCloseTaskConditions.setDefaultValue("txn_paid");

    Attribute recurFrequency = new Attribute();
    recurFrequency.setId(RECUR_FREQUENCY);
    recurFrequency.setDefaultValue("0");

    nextStep.setAttributes(Arrays.asList(isRecurring, filterCloseTaskConditions, recurFrequency));

    configStepIdMap.put(2, configStep);
    configStepIdMap.put(3, nextStep);

    Record record = customWorkflowConfig.getRecordObjForType(RecordType.INVOICE.name());
    String actionKey = CustomWorkflowType.REMINDER.getActionKey();

    Action action = new Action();
    List<InputParameter> parameters = new ArrayList<>();
    InputParameter parameter1 = new InputParameter();
    parameter1.setParameterName(IS_RECURRING_ENABLED);

    InputParameter parameter2 = new InputParameter();
    parameter2.setParameterName(FILTER_CLOSE_TASK_CONDITIONS);

    InputParameter parameter3 = new InputParameter();
    parameter2.setParameterName(RECUR_FREQUENCY);
    action.setParameters(Arrays.asList(parameter1, parameter2, parameter3));

    action.setParameters(parameters);
    Mockito.when(actionBuilder.buildTemplateStepAction(any(), any(), any())).thenReturn(action);
    WorkflowStep compositeStep = multiStepCompositeBuilder.processWorkflowStep(
        record,
        actionKey,
        false,
        configStep,
        null,
        configStepIdMap);

    Assert.assertNotNull(compositeStep);
    Assert.assertNotNull(compositeStep.getActionGroup());
    Assert.assertEquals(StepTypeEnum.WORFKLOWSTEP, compositeStep.getStepType());


  }
}