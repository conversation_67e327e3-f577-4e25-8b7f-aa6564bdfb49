package com.intuit.appintgwkflw.wkflautomate.was.core.definition.throttle;

import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleServiceHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleDefinitionsPerWorkflowTest {

  @Mock private ProceedingJoinPoint proceedingJoinPoint;
  @Mock private DefinitionInstance definitionInstance;
  @Mock private ThrottleConfigs throttleConfigs;
  @Mock private ThrottleHelper throttleHelper;
  @Mock private DefinitionDetailsRepository dbRepository;
  @Mock private TemplateDetails templateDetails;
  @InjectMocks
  @Spy
  private ThrottleDefinitionsPerWorkflow throttleHandler;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(throttleHandler, "throttleHelper", throttleHelper);
    ReflectionTestUtils.setField(throttleHandler, "throttleConfigs", throttleConfigs);
  }

  @Test
  public void testGetAttribute() {
    Assert.assertEquals(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, throttleHandler.getAttribute());
  }

  // When throttling config not present
  @Test
  public void testCheckAndExecuteWhenThrottlingNotPresent() throws Throwable {
    ThrottleServiceHandlers.addHandler(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, throttleHandler);
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    boolean response = throttleHandler.canContinueExecution(proceedingJoinPoint);
    Assert.assertTrue(response);
    Mockito.verify(definitionInstance, Mockito.times(0)).getDefinitionDetails();
  }

  // When throttling disabled
  @Test
  public void testIsThrottlingEnabled_WhenThrottlingDisabledForDefinitionsPerWorkflow() {
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    Mockito.when(throttleConfigs.isDefinitionsPerWorkflowInTimeframe()).thenReturn(false);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
  }

  // When throttling is enabled for definitions per workflow in config; but no threshold configs present for workflow (disabled for workflow/neither configs present for workflow nor default configs present)
  @Test
  public void testIsThrottlingEnabled_WhenThrottlingEnabledForDefinitionsPerWorkflowInConfigButBypassedLater() {
    Mockito.when(throttleConfigs.isDefinitionsPerWorkflowInTimeframe()).thenReturn(true);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(false);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
  }

  @Test
  public void testNoTimeframeConfigPresent() {
    Mockito.when(throttleConfigs.isDefinitionsPerWorkflowInTimeframe()).thenReturn(true);
    Mockito.when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(throttleHelper.isThrottlingEnabledForWorkflow("invAppWorkflow", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)).thenReturn(true);
    when(throttleHelper.isTimeframeDefined("invAppWorkflow", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)).thenReturn(false);
    Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
  }

  @Test
  public void getDefinitionCountFromDB() {
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
    Mockito.verify(dbRepository).getCountOfDefinitionsPerWorkflowInTimeframe(Mockito.eq("invAppWorkflow"), Mockito.any(Timestamp.class), Mockito.any(Timestamp.class));
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingEnabled_ErrorBreached() throws Throwable {

    try {
      when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
      when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
      when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");

      when(dbRepository.getCountOfDefinitionsPerWorkflowInTimeframe(Mockito.eq("invAppWorkflow"), Mockito.any(Timestamp.class), Mockito.any(Timestamp.class))).thenReturn(5);

      when(throttleHelper.getThreshold("invAppWorkflow", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)).thenReturn(3);

      throttleHandler.execute(proceedingJoinPoint);
      Assert.fail();
    }
    catch (Exception e) {
      Assert.assertEquals(String.format(WorkflowError.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_THRESHOLD_BREACHED.getErrorMessage(), "invAppWorkflow"), e.getMessage());
      Mockito.verify(proceedingJoinPoint, Mockito.times(0)).proceed();
    }
  }

  @Test
  public void testCheckAndExecuteWhenThrottlingEnabled_WarnBreached() throws Throwable {
    Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
    throttleWarnDiffMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, 2);

    when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getTemplateName()).thenReturn("invAppWorkflow");
    when(dbRepository.getCountOfDefinitionsPerWorkflowInTimeframe(Mockito.eq("invAppWorkflow"), Mockito.any(Timestamp.class), Mockito.any(Timestamp.class))).thenReturn(2);
    when(throttleHelper.getThreshold("invAppWorkflow", ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)).thenReturn(3);

    boolean response = throttleHandler.execute(proceedingJoinPoint);
    Assert.assertTrue(response);
    Mockito.verify(throttleHandler).executeWarn(proceedingJoinPoint, 3);
  }

  @Test
  public void testGetWarnDiff_WarnConfigPresent() {
    Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
    throttleWarnDiffMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, 10);

    when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);
    Assert.assertEquals((Integer) 10, throttleHandler.getWarnDiff());
  }

  @Test
  public void testGetWarnDiff_WarnConfigNotPresent() {
    Assert.assertEquals((Integer) 0, throttleHandler.getWarnDiff());
  }

  @Test
  public void testExecuteWarn() {
    try {
      throttleHandler.executeWarn(proceedingJoinPoint, 15);
    }
    catch (Exception e) {
      Assert.fail();
    }
  }

  private Object[] joinPointArgs() {
    Object[] args = new Object[1];
    args[0] = definitionInstance;
    return args;
  }
}

