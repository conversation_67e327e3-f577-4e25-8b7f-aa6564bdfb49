package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.Map;

public class DefaultPlaceholderUserVariableExtractorTest {

    @InjectMocks
    private DefaultPlaceholderUserVariableExtractor defaultPlaceholderUserVariableExtractor;

    private DefinitionDetails definitionDetails;
    private ProcessDetails processDetails;
    private ActivityProgressDetails activityProgressDetails;


    @Before
    public void init() {
        MockitoAnnotations.openMocks(this);
        definitionDetails = DefinitionDetails.builder()
                .definitionId("test-def-id")
                .definitionKey("customApproval_9130357096596496_4e0d5de3-484c-4365-88e1-0162e7c7b82e")
                .placeholderValue("{\"user_meta_data\": {\"intuit_was_locale\": \"en_us\"}, \"user_variables\": {\"approval:createTask\": {\"selected\": true, \"parameters\": {\"Assignee\": {\"fieldValue\": [\"9130358018375316\"]}, \"TaskName\": {\"fieldValue\": [\"Approval due for Invoice [[DocNumber]]\"]}, \"TaskType\": {\"fieldValue\": [\"QB_INVOICE\"]}, \"CloseTask\": {\"fieldValue\": [\"txn_approval_changed\"]}, \"ProjectType\": {\"fieldValue\": [\"QB_INVOICE_APPROVAL\"]}}}, \"approval:sendCompanyEmail\": {\"selected\": true, \"parameters\": {\"CC\": {\"fieldValue\": []}, \"BCC\": {\"fieldValue\": []}, \"SendTo\": {\"fieldValue\": [\"[[CompanyEmail]]\"]}, \"IsEmail\": {\"fieldValue\": [\"true\"]}, \"Message\": {\"fieldValue\": [\"Hi,\\n\\nInvoice [[DocNumber]] is pending approval. Please approve it at the earliest using this task manager link - https://app.e2e.qbo.intuit.com/app/taskmanager.\\n\\nNote that invoices that are not approved for more than 30 days will be auto rejected.\\n\\nThanks,\\n[[CompanyName]]\"]}, \"Subject\": {\"fieldValue\": [\"Review Invoice [[DocNumber]]\"]}, \"consolidateNotifications\": {\"fieldValue\": [\"false\"]}}}, \"approval:sendPushNotification\": {\"selected\": true, \"parameters\": {\"SendTo\": {\"fieldValue\": [\"9130358018375286\"]}, \"Message\": {\"fieldValue\": [\"Go to QuickBooks to view it.\"]}, \"Subject\": {\"fieldValue\": [\"An Invoice needs your attention\"]}, \"IsMobile\": {\"fieldValue\": [\"true\"]}, \"NotificationAction\": {\"fieldValue\": [\"qb001://open/invoice/?id=[[TxnId]]&companyid=[[RealmId]]\"]}}}}, \"process_variables\": {\"Id\": {\"type\": \"String\"}, \"TxnDate\": {\"type\": \"string\"}, \"DocNumber\": {\"type\": \"string\"}, \"TxnAmount\": {\"type\": \"double\"}, \"TxnDueDate\": {\"type\": \"string\"}, \"createTask\": {\"type\": \"String\", \"value\": \"true\"}, \"entityType\": {\"type\": \"String\"}, \"CompanyName\": {\"type\": \"string\"}, \"CompanyEmail\": {\"type\": \"string\"}, \"CustomerName\": {\"type\": \"string\"}, \"CustomerEmail\": {\"type\": \"string\"}, \"TxnSendStatus\": {\"type\": \"string\"}, \"intuit_userid\": {\"type\": \"string\"}, \"intuit_realmid\": {\"type\": \"String\"}, \"TxnBalanceAmount\": {\"type\": \"double\"}, \"entityChangeType\": {\"type\": \"String\"}, \"sendCompanyEmail\": {\"type\": \"String\", \"value\": \"true\"}, \"sendPushNotification\": {\"type\": \"String\", \"value\": \"true\"}}}")
                .build();
        processDetails = ProcessDetails.builder()
                .processId("test-id")
                .definitionDetails(definitionDetails)
                .ownerId(1234556L)
                .recordId("22734")
                .processStatus(ProcessStatus.ACTIVE)
                .build();
        activityProgressDetails = ActivityProgressDetails.builder()
                .id("createTask:b2754689-d383-11ed-9878-f6372692eefb")
                .name("Create project service task")
                .attributes("{\"runtimeAttributes\":{\"parameterDetails\":\"{}\",\"handlerDetails\":\"{}\",\"entityChangeType\":\"created\",\"entityType\":\"Invoice\",\"TxnDate\":\"2020-01-31\",\"CompanyEmail\":\"\",\"TxnAmount\":7898.0,\"intuit_userid\":\"-9130357096595396\",\"sendCompanyEmail\":\"true\",\"TxnBalanceAmount\":null,\"CompanyName\":\"\",\"TxnDueDate\":\"\",\"templateName\":\"invoiceapproval\",\"CustomerEmail\":\"\",\"DocNumber\":\"1013\",\"sendPushNotification\":\"true\",\"definitionKey\":\"customApproval_9130357096596496_4e0d5de3-484c-4365-88e1-0162e7c7b82e\",\"Id\":\"22734\",\"CustomerName\":\"\",\"createTask\":\"true\",\"taskDetails\":\"{ \\\"required\\\": true}\",\"TxnSendStatus\":\"\",\"intuit_realmid\":\"9130357096596496\",\"decisionResult\":true},\"modelAttributes\":{\"events\":\"[\\\"start\\\"]\",\"auditMessage\":\"Task assigned to {}\"}}")
                .processDetails(processDetails)
                .build();
    }

    @Test
    public void testExecute(){
        Map<String, String> userPlaceholderAttributes = defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails, null);
        Assert.assertNotNull(userPlaceholderAttributes);
        Assert.assertNotNull(userPlaceholderAttributes.get("Assignee"));
        Assert.assertNotNull(userPlaceholderAttributes.get("TaskName"));
    }

    @Test
    public void testExecute_sendCompanyEmail(){
        activityProgressDetails.setId("sendCompanyEmail:b2754689-d383-11ed-9878-f6372692eefb");
        Map<String, String> userPlaceholderAttributes = defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails, null);
        Assert.assertNotNull(userPlaceholderAttributes);
        Assert.assertNotNull(userPlaceholderAttributes.get("IsEmail"));
        Assert.assertNotNull(userPlaceholderAttributes.get("Message"));
    }

    @Test
    public void testExecute_NPE_WithoutUserVariables(){
        definitionDetails.setPlaceholderValue("{\"user_meta_data\": {\"intuit_was_locale\": \"en_us\"}}");
        Map<String, String> userPlaceholderAttributes = defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails, null);
        Assert.assertEquals(userPlaceholderAttributes.size(), 0);
    }

    @Test
    public void testExecute_NPE_WithoutActivityId(){
        activityProgressDetails.setId("randomTestId");
        Map<String, String> userPlaceholderAttributes = defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails, null);
        Assert.assertEquals(userPlaceholderAttributes.size(), 0);
    }

    @Test
    public void testExecuteNullPlaceholders(){
        definitionDetails.setPlaceholderValue(null);
        State state = new State();
        state.addValue(WorkflowConstants.ACTIVITY_PROGRESS_DETAILS, activityProgressDetails);
        Map<String, String> userPlaceholderVariables = defaultPlaceholderUserVariableExtractor.getUserVariablesForActivity(activityProgressDetails, null);
        Assert.assertEquals(0,userPlaceholderVariables.size());
    }

}
