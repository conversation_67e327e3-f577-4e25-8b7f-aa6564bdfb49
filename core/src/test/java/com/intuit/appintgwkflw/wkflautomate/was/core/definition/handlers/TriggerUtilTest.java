package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.WorkflowsBulkAction;
import com.intuit.v4.WorkflowsBulkAction.WorkflowBulkActionDetails;
import java.util.ArrayList;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 */
public class TriggerUtilTest {

  private TriggerUtil triggerUtil;

  @Test
  public void success() {

    WorkflowBulkActionDetails workflowBulkActionDetails = new WorkflowBulkActionDetails();
    Map<String, Object> triggerPayload = triggerUtil.prepareTriggerPayloadForDowngrade("123",workflowBulkActionDetails);
    Assert.assertNotNull(triggerPayload);
    Map<String, Object> entityObj = (Map<String, Object>) triggerPayload.get(WorkflowConstants.ENTITY);
    Assert.assertEquals(Collections.EMPTY_LIST, ((Map<String, Object>)entityObj.get(RecordType.SUBSCRIPTION.getDisplayValue())).get(WorkflowConstants.SUBSCRIPTIONS));
  }
}
