package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SystemTemplateUtilTest {

  @Test
  public void checkNonRealmSystemUserSuccess(){
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);
    WASContext.setNonRealmSystemUserContext(true);
    try{
      SystemTemplateUtil.checkNonRealmSystemUser(templateDetails);
    }
    finally {
      WASContext.clear();
    }
  }

  @Test
  public void checkRealmUserSystemTemplate(){
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.SYSTEM);
    SystemTemplateUtil.checkNonRealmSystemUser(templateDetails);
  }

  @Test
  public void checkRealmUserUserTemplate(){
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.USER);
    SystemTemplateUtil.checkNonRealmSystemUser(templateDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void checkSystemUserWithException(){
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setDefinitionType(DefinitionType.USER);
    WASContext.setNonRealmSystemUserContext(true);
    try{
      SystemTemplateUtil.checkNonRealmSystemUser(templateDetails);
    }
    finally {
      WASContext.clear();
    }
  }
}
