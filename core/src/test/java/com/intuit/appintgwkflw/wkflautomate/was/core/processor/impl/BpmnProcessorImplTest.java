package com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtilTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DaysTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.ConditionalParameter;
import com.intuit.v4.workflows.definitions.InputParameter;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@Import({BpmnProcessorImpl.class, WorkflowGlobalConfiguration.class})
public class BpmnProcessorImplTest {

    private static DmnModelInstance dmnModelInstance =
            Dmn.readModelFromStream(
                    BpmnProcessorUtilTest.class
                            .getClassLoader()
                            .getResourceAsStream("dmn/decision_invoiceunsentreminder.dmn"));
    @MockBean
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    @Autowired
    private BpmnProcessorImpl bpmnProcessor;
    @Autowired
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    @MockBean
    private WASContextHandler wasContextHandler;
    @MockBean
    private TemplateBuilder templateBuilder;
    @MockBean
    private TranslationService translationService;
    @MockBean
    private TemplateLabelsService templateLabelsService;

    @MockBean
    private FeatureFlagManager featureFlagManager;
    @MockBean
    private MultiStepConfig multiStepConfig;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DAYS, new DaysTransformer());
        ReflectionTestUtils.setField(bpmnProcessor, "translationService", TestHelper.initTranslationService());
        ReflectionTestUtils.setField(bpmnProcessor, "wasContextHandler", wasContextHandler);
        Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("en_US");
        Mockito.when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
    }

    @Test
    public void testPrepareDefaultRule() {
        DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
        final String parameter = "Customer";
        BpmnProcessorImpl bpmnProcessor = new BpmnProcessorImpl<>();

        List<RuleLine> rules =
                bpmnProcessor.prepareDefaultRule(
                        parameter,
                        dmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
                                .findFirst()
                                .get());

        Assert.assertNotNull(rules);
        Assert.assertEquals(1, rules.size());
        Assert.assertEquals(1, rules.get(0).getRules().size());
        Assert.assertEquals(
                "CONTAINS ALL_Customer", rules.get(0).getRules().get(0).getConditionalExpression());
    }

    @Test
    public void testGetMappedActionKey() throws IOException {
        //Using bankDepositReminder template where actionKey = ${sendReminder == true}
        BpmnModelInstance bpmnModelInstanceSD =
                BpmnProcessorUtil.readBPMN(
                        IOUtils.toByteArray(
                                BpmnProcessorUtilTest.class
                                        .getClassLoader()
                                        .getResourceAsStream("bpmn/bankDepositReminderSDEF.bpmn")));

        DmnModelInstance dmnModelInstanceSD =
                Dmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream("dmn/decision_bankdepositremindersingle.dmn"));

        TemplateDetails bpmnDetails = TemplateDetails.builder().build();
        bpmnDetails.setRecordType(RecordType.INVOICE);
        bpmnDetails.setTemplateName("customApproval");
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstanceSD,
                Collections.singletonList(dmnModelInstanceSD),
                bpmnDetails);
        Template t =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create("REALM_ID", "LOCAL_ID"),
                                true);

        Assert.assertNotNull(t);
        t.getWorkflowSteps().forEach(workflowStep -> {
            workflowStep.getActions().forEach(action -> {
                Assert.assertEquals("sendReminder", action.getActionKey());
            });
        });
    }

    @Test
    public void testGetMappedActionKeyFail() throws IOException {

        //Using bankDepositReminder template where actionKey = ""
        BpmnModelInstance bpmnModelInstanceSD =
                BpmnProcessorUtil.readBPMN(
                        IOUtils.toByteArray(
                                BpmnProcessorUtilTest.class
                                        .getClassLoader()
                                        .getResourceAsStream("bpmn/bankDepositReminderSDEFFail.bpmn")));

        DmnModelInstance dmnModelInstanceSD =
                Dmn.readModelFromStream(
                        BpmnProcessorUtilTest.class
                                .getClassLoader()
                                .getResourceAsStream("dmn/decision_bankdepositremindersingle.dmn"));

        TemplateDetails bpmnDetails = TemplateDetails.builder().build();
        bpmnDetails.setRecordType(RecordType.INVOICE);
        bpmnDetails.setTemplateName("customApproval");

        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstanceSD,
                Collections.singletonList(dmnModelInstanceSD),
                bpmnDetails);

        Template t =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create("REALM_ID", "LOCAL_ID"),
                                true);

        Assert.assertNotNull(t);
        t.getWorkflowSteps().forEach(workflowStep -> {
            workflowStep.getActions().forEach(action -> {
                Assert.assertEquals("{}", action.getActionKey());
            });
        });

    }

    @Test
    public void testActionAndConditionsOfBankDepositReminderForEn_US() throws IOException {
        Map<String, String> actionMap =
                Map.of(
                        "Subject",
                        "Reminder: You’ve got bank deposits to record",
                        "Message",
                        "Hi [[Company Name]],\n"
                                + "\n"
                                + "You’ve got bank deposits to record. Review them now: https://silver-develop.qbo.intuit.com/login?pagereq=https://silver-develop.qbo.intuit.com/app/deposit");

        Map<String, String> conditionMap =
                Map.of(
                        "LT",
                        "is less than",
                        "GTE",
                        "is greater than or equal to",
                        "LTE",
                        "is less than or equal to",
                        "EQ",
                        "is equal to",
                        "GT",
                        "is greater than");
        testActionAndConditionsOfBankDepositReminder(actionMap, conditionMap);
    }

    @Test
    public void testActionAndConditionsOfBankDepositReminderForCa_FR() throws IOException {
        Map<String, String> actionMap =
                Map.of(
                        "Subject",
                        "Rappel : Vous avez des dépôts bancaires à enregistrer",
                        "Message",
                        "Bonjour [[Company Name]],\n"
                                + "\n"
                                + "Vous avez des dépôts bancaires à enregistrer. Vérifiez-les maintenant : https://silver-develop.qbo.intuit.com/login?pagereq=https://silver-develop.qbo.intuit.com/app/deposit");

        Map<String, String> conditionMap =
                Map.of(
                        "LT",
                        "est inférieur à",
                        "GTE",
                        "est supérieur ou égal à",
                        "LTE",
                        "est inférieur ou égal à",
                        "EQ",
                        "est égal à",
                        "GT",
                        "est supérieur à");
        Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("fr_CA");
        testActionAndConditionsOfBankDepositReminder(actionMap, conditionMap);
    }

    private void testActionAndConditionsOfBankDepositReminder(
            Map<String, String> actionMap, Map<String, String> conditionMap) throws IOException {

        BpmnModelInstance bpmnModelInstance =
                Bpmn.readModelFromStream(
                        IOUtils.toInputStream(
                                TestHelper.readResourceAsString("bpmn/bankDepositReminder.bpmn"),
                                Charset.defaultCharset()));

        DmnModelInstance dmnModelInstance =
                Dmn.readModelFromStream(
                        IOUtils.toInputStream(TestHelper.readResourceAsString("dmn/decision_bankDepositReminder.dmn"), Charset.defaultCharset()));

        TemplateDetails bpmnDetails = TemplateDetails.builder().build();
        bpmnDetails.setRecordType(RecordType.INVOICE);
        bpmnDetails.setTemplateName("customApproval");

        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.STRING, new StringDataTypeTransformer());
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT, new DefaultDataTypeTransformer(workflowGlobalConfiguration));

        Template t =
                (Template)
                        bpmnProcessor.processBpmn(
                                definitionInstance,
                                GlobalId.create("REALM_ID", "LOCAL_ID"),
                                true);

        Assert.assertNotNull(t);
        Assert.assertEquals(1, t.getWorkflowSteps().size());

        // Actions
        List<WorkflowStep.ActionMapper> actionMappers = t.getWorkflowSteps().get(0).getActions();
        Assert.assertEquals(2, actionMappers.size());
        // sendNotification_bankdepositreminder
        Assert.assertNotNull(actionMappers.get(0).getAction());
        Action sendNotificationBankdepositreminder = actionMappers.get(0).getAction();

        Assert.assertEquals("Send notification", sendNotificationBankdepositreminder.getName());
        Assert.assertEquals(12, sendNotificationBankdepositreminder.getParameters().size());
        List<InputParameter> inputParameters = sendNotificationBankdepositreminder.getParameters();
        inputParameters.forEach(
                inputParameter -> {
                    if (actionMap.containsKey(inputParameter.getParameterName())) {
                        Assert.assertEquals(
                                actionMap.get(inputParameter.getParameterName()),
                                inputParameter.getFieldValues().get(0));
                    }
                });
        // Conditions
        WorkflowStepCondition workflowStepCondition =
                t.getWorkflowSteps().get(0).getWorkflowStepCondition();
        Assert.assertNotNull(workflowStepCondition);
        List<ConditionalParameter> conditionalParameters =
                workflowStepCondition.getConditionalInputParameters();
        Assert.assertEquals(1, conditionalParameters.size());
        ConditionalParameter conditionalParameter = conditionalParameters.get(0);
        Assert.assertEquals(conditionMap.size(), conditionalParameter.getSupportedOperators().size());
        conditionalParameter
                .getSupportedOperators()
                .forEach(
                        operator -> {
                            Assert.assertEquals(true, conditionMap.containsKey(operator.getSymbol()));
                            Assert.assertEquals(
                                    conditionMap.get(operator.getSymbol()), operator.getDescription());
                        });
    }
}
