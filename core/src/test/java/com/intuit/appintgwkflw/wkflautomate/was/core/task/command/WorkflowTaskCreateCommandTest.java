package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.PublishEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.StateTransitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * Create Command is responsible to create entry of transaction and activity in DB to maintain
 * state.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskCreateCommandTest {

  @InjectMocks
  private WorkflowTaskCreateCommand createCommand;

  @Mock
  private WorkflowHumanTask workflowHumanTask;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private ActivityProgressDetailsRepository progressDetailRepo;

  @Mock
  private ProcessDetailsRepository processDetailRepo;

  @Mock
  private EventPublisherCapability eventPublisherCapability;

  @Mock
  private ActivityDetailsRepository activityDetailRepo;

  @Mock
  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @Mock
  private PublishEventHandler publishEventHandler;
  
  @Mock
  private StateTransitionService stateTransitionService;

  @Before
  public void init() {
    ReflectionTestUtils.setField(createCommand, "contextHandler", contextHandler);
    ReflectionTestUtils
        .setField(createCommand, "eventPublisherCapability", eventPublisherCapability);
    ReflectionTestUtils.setField(createCommand, "publishEventHandler", publishEventHandler);
    ReflectionTestUtils.setField(createCommand, "stateTransitionService", stateTransitionService);
  }

  /**
   * No task handler found.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_Failure() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(taskAttributes).taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    WorkflowTasks.addWorkflowTask(TaskType.SYSTEM_TASK, null);

    createCommand.execute(taskRequest);
  }


  /**
   * No task handler found.
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_ProcessNotFound() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    WorkflowTasks.addWorkflowTask(TaskType.SYSTEM_TASK, null);

    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    createCommand.execute(taskRequest);
  }


  /**
   * State is open, transaction get has nothing.
   */
  @Test
  public void execute_stateOpen_TxnGetFails() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .publishExternalTaskEvent(true).publishWorkflowStateTransitionEvent(true)
        .taskType(TaskType.HUMAN_TASK).workerId("worker1")
        .build();

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_OPEN).attributes("{}").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetail));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);
    WorkflowTaskResponse getResponse = WorkflowTaskResponse.builder()
        .status(ActivityConstants.TASK_STATUS_FAILED).build();
    Mockito.when(workflowHumanTask.get(Mockito.any(HumanTask.class))).thenReturn(getResponse);

    WorkflowTaskResponse createResponse = WorkflowTaskResponse.builder()
        .status(ActivityConstants.TASK_STATUS_CREATED).txnId("t2").build();
    Mockito.when(workflowHumanTask.create(Mockito.any(HumanTask.class))).thenReturn(createResponse);

    Mockito.when(taskDBOperationManager
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class)))
        .thenReturn(activityProgressDetail);

    Mockito.doNothing().when(stateTransitionService).publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());

    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("t2", response.getTxnId());
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).create(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class));
    
    Mockito.verify(stateTransitionService, Mockito.times(1))
    	.publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());
  }

  /**
   * State is open, transaction is created.
   */
  @Test
  public void execute_stateOpen_TxnSuccess() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .publishExternalTaskEvent(true).publishWorkflowStateTransitionEvent(true)
        .taskType(TaskType.HUMAN_TASK).workerId("worker1")
        .build();

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_OPEN).attributes("{}").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetail));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);
    WorkflowTaskResponse getResponse = WorkflowTaskResponse.builder()
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .txnId("t2").build();
    Mockito.when(workflowHumanTask.get(Mockito.any(HumanTask.class))).thenReturn(getResponse);

    Mockito.when(taskDBOperationManager
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class)))
        .thenReturn(activityProgressDetail);
    
    Mockito.doNothing().when(stateTransitionService).publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());
   
    createCommand.execute(taskRequest);

    Mockito.verify(workflowHumanTask, Mockito.never()).create(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class));
    Mockito.verify(stateTransitionService, Mockito.times(1))
    	.publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());
  }

  /**
   * State is created.
   */
  @Test
  public void execute_stateCreated() {

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");
    modelAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");
    modelAttributes.put("domain", "QB_LIVE");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .publishExternalTaskEvent(true)
        .publishWorkflowStateTransitionEvent(true)
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(activityAttributes.getModelAttributes())
        		.runtimeAttributes(activityAttributes.getRuntimeAttributes())
        		.variables(runtimeAttributes).build())
        .build();

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").build())
        .attributes("{}").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(activityProgressDetail));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));
    
    Mockito.doNothing().when(stateTransitionService).publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());

    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("txn1", response.getTxnId());
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertNotNull(response.getResponseMap());
    Assert.assertNotNull(
        response.getResponseMap().get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE));

    Mockito.verify(workflowHumanTask, Mockito.never()).create(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.never())
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class));
    Mockito.verify(taskDBOperationManager, Mockito.never())
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class));
    Mockito.verify(stateTransitionService, Mockito.times(1))
		.publishEvent(Mockito.any(WorkflowTaskRequest.class),
				Mockito.any(ProcessDetails.class), Mockito.anyString());
  }

  /**
   * State is not present.
   */
  @Test
  public void execute_NoStatePresent() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put(WorkFlowVariables.EVENTS.getName(), "[\"created\"]");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .publishExternalTaskEvent(true).publishWorkflowStateTransitionEvent(true)
        .taskType(TaskType.HUMAN_TASK).workerId("worker1")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes)
        		.variables(runtimeAttributes).build())
        .build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    WorkflowTaskResponse createResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status(ActivityConstants.TASK_STATUS_CREATED).build();
    Mockito.when(workflowHumanTask.create(Mockito.any(HumanTask.class))).thenReturn(createResponse);

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_OPEN).attributes("{}").build();

    Mockito.when(taskDBOperationManager
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class)))
        .thenReturn(activityProgressDetail);

    Mockito.when(taskDBOperationManager
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class))).thenReturn(activityProgressDetail);

    Mockito.doNothing().when(stateTransitionService).publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());
    
    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("t2", response.getTxnId());

    Mockito.verify(workflowHumanTask, Mockito.times(1)).create(Mockito.any());
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class));
    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class));

    Mockito.verify(stateTransitionService, Mockito.times(1)).publishEvent(Mockito.any(WorkflowTaskRequest.class),
            Mockito.any(ProcessDetails.class), Mockito.anyString());
  }

  /**
   * State is not present, downstream adaptor fails to create transaction.
   */
  @Test(expected = WorkflowRetriableException.class)
  public void execute_NoStatePresent_DBSaveFail() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes)
        		.variables(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    WorkflowTaskResponse createResponse = WorkflowTaskResponse.builder().txnId("t2")
        .status(ActivityConstants.TASK_STATUS_CREATED).build();
    Mockito.when(workflowHumanTask.create(Mockito.any(HumanTask.class))).thenReturn(createResponse);

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_CREATED).attributes("{}").build();

    Mockito.when(taskDBOperationManager
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class))).thenReturn(activityProgressDetail);

    Mockito.when(taskDBOperationManager
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskResponse.class)))
        .thenThrow(WorkflowRetriableException.class);

    createCommand.execute(taskRequest);

  }

  /**
   * State is not present, DB save fails.
   */
  @Test(expected = WorkflowRetriableException.class)
  public void execute_NoStatePresent_downstreamCreateFail() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes)
        		.variables(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.doThrow(WorkflowRetriableException.class).when(workflowHumanTask)
        .create(Mockito.any(HumanTask.class));

    createCommand.execute(taskRequest);
  }

  @Test
  public void testCommand() {
    WorkflowTaskCommand taskCommand = new WorkflowTaskCreateCommand(null, null, null);
    Assert.assertEquals("TaskCommand should be create.", TaskCommand.CREATE, taskCommand.command());
  }


  /**
   * State is not present. SkipCallback = true.
   */
  @Test
  public void execute_NoStatePresent_skipCallback_true() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");
    
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .skipCallback(true).txnId("txn1")
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1")
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes)
        		.variables(runtimeAttributes).build())
        .build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_OPEN).attributes("{}").build();

    Mockito.when(taskDBOperationManager
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class)))
        .thenReturn(activityProgressDetail);

    Mockito.when(taskDBOperationManager
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class))).thenReturn(activityProgressDetail);

    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("txn1", response.getTxnId());
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertNotNull(response.getResponseMap());
    Assert.assertNotNull(
        response.getResponseMap().get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE));

    Mockito.verify(workflowHumanTask, Mockito.never()).create(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class));

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class));

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .saveCreateStateInDB(Mockito.any(Task.class), Mockito.any(ActivityProgressDetails.class),
            Mockito.any());

    Mockito.verify(eventPublisherCapability, Mockito.never())
        .publish(Mockito.any(), Mockito.any());
  }


  /**
   * State is present. SkipCallback = true.
   */
  @Test
  public void execute_StatePresent_skipCallback_true() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .skipCallback(true).txnId("txn1")
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .taskAttributes(TaskAttributes.builder()
        		.modelAttributes(modelDefAttributes)
        		.runtimeAttributes(runtimeDefAttributes)
        		.variables(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1")
        .build();

    ActivityProgressDetails activityProgressDetail = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_OPEN)
        .attributes("{}").build();

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(activityProgressDetail));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(workflowHumanTask.typeReference()).thenReturn(new TypeReference<HumanTask>() {
    });
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);

    Mockito.when(taskDBOperationManager
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class)))
        .thenReturn(activityProgressDetail);

    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("txn1", response.getTxnId());
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertNotNull(response.getResponseMap());
    Assert.assertNotNull(
        response.getResponseMap().get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE));

    Mockito.verify(workflowHumanTask, Mockito.never()).create(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class));

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class));

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .saveCreateStateInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class), Mockito.any());

    Mockito.verify(eventPublisherCapability, Mockito.never())
        .publish(Mockito.any(), Mockito.any());
  }

  /**
   * Case of FinalRetry failure on EventPublish. 
   * State is present in Activity and Transaction.
   * Camunda Cockpit Retry.
   */
  @Test
  public void execute_StatePresent_TxnPresent() {

    Map<String, Object> runtimeAttributes = new HashMap<>();

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");
    modelDefAttributes.put("domain", "QB_LIVE");

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id("ext1")
        .processInstanceId("proc1").command(TaskCommand.CREATE)
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .activityId("actId").activityName("actName")
        .skipCallback(true).txnId("txn1")
        .taskAttributes(TaskAttributes.builder()
        	.modelAttributes(modelDefAttributes)
        	.runtimeAttributes(runtimeDefAttributes)
        	.variables(runtimeAttributes).build())
        .publishExternalTaskEvent(false).publishWorkflowStateTransitionEvent(false)
        .taskType(TaskType.HUMAN_TASK)
        .workerId("worker1")
        .build();

    ActivityProgressDetails.ActivityProgressDetailsBuilder activityProgressDetailBuilder
        = ActivityProgressDetails.builder().id("ext1")
        .status(ActivityConstants.TASK_STATUS_FAILED)
        .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").build())
        .attributes("{}");

    Mockito.when(progressDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(activityProgressDetailBuilder.build()));

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(taskDBOperationManager
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class)))
        .thenReturn(
            activityProgressDetailBuilder.status(ActivityConstants.TASK_STATUS_CREATED).build());

    WorkflowTaskResponse response = createCommand.execute(taskRequest);
    Assert.assertEquals("txn1", response.getTxnId());
    Assert.assertEquals(ActivityConstants.TASK_STATUS_CREATED, response.getStatus());
    Assert.assertNotNull(response.getResponseMap());
    Assert.assertNotNull(
        response.getResponseMap().get(ActivityConstants.WORKFLOW_DEFAULT_TRANSACTION_VARIABLE));

    Mockito.verify(workflowHumanTask, Mockito.never()).create(Mockito.any());

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .prepareActivityProgressAndSaveInOpenState(Mockito.any(Task.class),
            Mockito.any(ProcessDetails.class));

    Mockito.verify(taskDBOperationManager, Mockito.times(1))
        .saveCreateStateInActivityProgressDB(Mockito.any(ActivityProgressDetails.class),
            Mockito.any(WorkflowTaskRequest.class));

    Mockito.verify(taskDBOperationManager, Mockito.never())
        .saveCreateStateInDB(Mockito.any(Task.class),
            Mockito.any(ActivityProgressDetails.class), Mockito.any());

    Mockito.verify(eventPublisherCapability, Mockito.never())
        .publish(Mockito.any(), Mockito.any());
  }


  @Test(expected = WorkflowNonRetriableException.class)
  public void test_optionalActivityProgressDetail() {
    createCommand.getActivityProgressDetail(Optional.ofNullable(null));
  }

}
