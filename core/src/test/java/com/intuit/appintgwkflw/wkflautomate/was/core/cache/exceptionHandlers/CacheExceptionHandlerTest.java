package com.intuit.appintgwkflw.wkflautomate.was.core.cache.exceptionHandlers;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheRetriableException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.client.RedisNodeNotFoundException;
import org.redisson.client.WriteRedisConnectionException;

@RunWith(MockitoJUnitRunner.class)
public class CacheExceptionHandlerTest {

    CacheExceptionHandler cacheExceptionHandler;

    @Before
    public void init() {
        cacheExceptionHandler = new CacheExceptionHandler();
    }

    @Test(expected = CacheNonRetriableException.class)
    public void testHandleCacheNonRetriableException() {
        cacheExceptionHandler.handleCacheException(
                new RedisNodeNotFoundException("RedisNodeNotFoundException")
        );
    }

    @Test(expected = CacheRetriableException.class)
    public void testHandleCacheRetriableException() {
        cacheExceptionHandler.handleCacheException(
                new WriteRedisConnectionException("WriteRedisConnectionException")
        );
    }
}
