package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.v4.workflows.definitions.LookupKey;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RunWith(MockitoJUnitRunner.class)
public class LookupKeysMapperTest {

  @InjectMocks
  LookupKeysMapper lookupKeysMapper = new LookupKeysMapper();

  @Test
  public void getListOfLookupKeysTest(){
    Map<String, String> lookupKeys = new HashMap<>();
    lookupKeys.put("envelopeId","1234");
    String lookupKeysString = ObjectConverter.toJson(lookupKeys);
    List<LookupKey> lookupKeyList = lookupKeysMapper.getListOfLookupKeysFromString(lookupKeysString).get();
    Assert.assertEquals(lookupKeyList.size(), 1);
    Assert.assertEquals(lookupKeyList.get(0).getKey(), "envelopeId");
    Assert.assertEquals(lookupKeyList.get(0).getValue(), "1234");
  }

  @Test
  public void getListOfLookupKeysNullTest(){
    Optional<List<LookupKey>> lookupKeyList = lookupKeysMapper.getListOfLookupKeysFromString(null);
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void getListOfLookupKeysEmptyTest(){
    Optional<List<LookupKey>> lookupKeyList = lookupKeysMapper.getListOfLookupKeysFromString("");
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void getListOfLookupKeysNullMapTest(){
    String lookupKeysString = "invalid string";
    Optional<List<LookupKey>> lookupKeyList = lookupKeysMapper.getListOfLookupKeysFromString(lookupKeysString);
    Assert.assertEquals(lookupKeyList, Optional.empty());
  }

  @Test
  public void getMapOfLookupKeysTest(){
    List<LookupKey> lookupKeyList = new ArrayList<>();
    LookupKey lookupKey  = new LookupKey();
    lookupKey.setKey("envelopeId");
    lookupKey.setValue("1234");
    lookupKeyList.add(lookupKey);
    Map<String, String> lookupKeyMap = LookupKeysMapper.getMapOfLookupKeysFromList(lookupKeyList).get();
    Assert.assertEquals(lookupKeyMap.size(), 1);
    Assert.assertNotNull(lookupKeyMap.get("envelopeId"));
    Assert.assertEquals(lookupKeyMap.get("envelopeId"), "1234");
  }

  @Test
  public void getMapOfLookupKeysNullTest(){
    Optional<Map<String, String>> lookupKeyMap = LookupKeysMapper.getMapOfLookupKeysFromList(null);
    Assert.assertEquals(lookupKeyMap, Optional.empty());
  }

  @Test
  public void getMapOfLookupKeysEmptyTest(){
    Optional<Map<String, String>> lookupKeyMap = LookupKeysMapper.getMapOfLookupKeysFromList(Collections.emptyList());
    Assert.assertEquals(lookupKeyMap, Optional.empty());
  }
}
