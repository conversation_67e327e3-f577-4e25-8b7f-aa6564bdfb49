package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.Operation;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4GraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4QueryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.v4.Error;
import com.intuit.v4.Query;
import com.intuit.v4.work.Project;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class ProjectTaskAdaptorTest {

  @InjectMocks
  private ProjectTaskAdaptor projectTaskAdaptor;

  @Mock
  private V4GraphqlClient v4GraphqlClient;
  
  @Mock
  private V4QueryHelper v4QueryHelper;


  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testProjectServiceCallSuccessful() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        response(project).error(false).build();
    Mockito.when(v4GraphqlClient.write(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);

    Project result = projectTaskAdaptor.executeProject(project,
        null, new WorkflowTaskConfigDetails(), Operation.CREATE, "123");
    Assert.assertEquals("Project 1", result.getName());
  }

  @Test
  public void testProjectServiceCallSuccessful_customHeader() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        response(project).error(false).build();
    Mockito.when(v4GraphqlClient.write(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);

    Map<String, String> customHeaders = new HashMap<>();
    customHeaders.put("target_app", "TTLIVE");
    customHeaders.put("target_domain", "abc1");
    
    Project result = projectTaskAdaptor.executeProject(project,
        customHeaders, new WorkflowTaskConfigDetails(), Operation.CREATE, "123");
    Assert.assertEquals("Project 1", result.getName());
  }


  @Test
  public void testProjectServiceCallFailed() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    Error error = new Error();
    error.setCode("007");
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        errors(Collections.singletonList(error)).error(true).build();
    Mockito.when(v4GraphqlClient.write(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);

    Set<String> nonRetryErrorCodes = new HashSet<>();
    nonRetryErrorCodes.add("006");
    nonRetryErrorCodes.add("007");
    WorkflowTaskConfigDetails taskConfig = new WorkflowTaskConfigDetails();
    taskConfig.setNonRetryErrorCodes(nonRetryErrorCodes);
    
    Exception exception = Assertions.assertThrows(WorkflowNonRetriableException.class,
        () -> projectTaskAdaptor
            .executeProject(project, 
                null, taskConfig, Operation.CREATE,"123"));
    Assert.assertEquals(
    		String.format(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.getErrorMessage(),error.getCode()),
    		exception.getMessage());
  }


  @Test
  public void testProjectServiceCallFailed_RetriableException() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    Error error = new Error();
    error.setCode("007");
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        errors(Collections.singletonList(error)).error(true).build();
    Mockito.when(v4GraphqlClient.write(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);

    Set<String> nonRetryErrorCodes = new HashSet<>();
    nonRetryErrorCodes.add(WorkflowError.ACTION_NOT_FOUND.name());

    WorkflowTaskConfigDetails taskConfig = new WorkflowTaskConfigDetails();
    taskConfig.setNonRetryErrorCodes(nonRetryErrorCodes);
    Exception exception = Assertions.assertThrows(WorkflowRetriableException.class,
        () -> projectTaskAdaptor
            .executeProject(project, 
                null, taskConfig, Operation.CREATE,"123"));
    Assert.assertEquals(
    		String.format(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.getErrorMessage(), error.getCode()),
    		exception.getMessage());
  }


  @Test
  public void testProjectServiceReadById() {
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        response(new Project().name("Test")).error(false).build();
    Mockito.when(v4GraphqlClient.read(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);
    Project project = projectTaskAdaptor
        .readProjectById("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29109347",
             new WorkflowTaskConfigDetails(), "123");
    Assert.assertEquals("Test", project.getName());
  }

  @Test
  public void testProjectServiceReadByIdempotenceId() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    WASV4GraphqlResponse<List<Project>> creationResponse = WASV4GraphqlResponse
        .<List<Project>>builder().
            response(Collections.singletonList(project)).error(false).build();
    Mockito.when(v4QueryHelper.buildV4Query(Mockito.any())).thenReturn(new Query());
    Mockito.when(v4GraphqlClient.readList(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);
    Project result = projectTaskAdaptor
        .readProjectByIdempotencyId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29109347",
            new WorkflowTaskConfigDetails(),"123", Collections.emptyMap());
    Assert.assertNotNull(result);
  }

  @Test
  public void testProjectServiceReadByIdempotenceId_emptyList() {
    WASV4GraphqlResponse<List<Project>> creationResponse = WASV4GraphqlResponse
        .<List<Project>>builder().
            response(Collections.emptyList()).error(false).build();
    Mockito.when(v4QueryHelper.buildV4Query(Mockito.any())).thenReturn(new Query());
    Mockito.when(v4GraphqlClient.readList(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);
    Project result = projectTaskAdaptor
        .readProjectByIdempotencyId("djQuMTo5MTMwMzU0OTY2NTg5MzM2OjY4ZDAxMTQ3ZGQ:29109347",
            new WorkflowTaskConfigDetails(),"123", Collections.emptyMap());
    Assert.assertNull(result);
  }

  @Test
  public void testProjectServiceCallFailed_NoErrorCode() {
    Project project = new Project();
    project.setName("Project 1");
    project.setStatus("created");
    Error error = new Error();
    WASV4GraphqlResponse<Project> creationResponse = WASV4GraphqlResponse.<Project>builder().
        errors(Collections.singletonList(error)).error(true).build();
    Mockito.when(v4GraphqlClient.write(Mockito.any()))
        .thenReturn((WASV4GraphqlResponse) creationResponse);

    Set<String> nonRetryErrorCodes = new HashSet<>();
    nonRetryErrorCodes.add("006");
    nonRetryErrorCodes.add("007");
    WorkflowTaskConfigDetails taskConfig = new WorkflowTaskConfigDetails();
    taskConfig.setNonRetryErrorCodes(nonRetryErrorCodes);

    Exception exception = Assertions.assertThrows(WorkflowRetriableException.class,
        () -> projectTaskAdaptor
            .executeProject(project,
                null, taskConfig, Operation.CREATE,"123"));
    Assert.assertEquals(
        String.format(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.getErrorMessage(), "UNKOWN_ERROR_CODE"),
        exception.getMessage());
  }

}
