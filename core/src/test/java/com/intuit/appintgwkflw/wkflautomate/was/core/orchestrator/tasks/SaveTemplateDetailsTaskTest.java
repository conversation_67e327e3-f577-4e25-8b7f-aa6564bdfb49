package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;

public class SaveTemplateDetailsTaskTest {

  private State state;
  private TemplateDetailsRepository templateDetailsRepository = Mockito.mock(TemplateDetailsRepository.class);
  private TriggerDetailsRepository triggerDetailsRepository = Mockito.mock(TriggerDetailsRepository.class);
  private TemplateDomainEventHandler templateDomainEventHandler = Mockito.mock(TemplateDomainEventHandler.class);

  private SaveTemplateDetailsTask saveTemplateDetailsTask;

  @Before
  public void setUp() {
    TemplateDetails bpmnTemplateDetails =
        TemplateDetails.builder().modelType(ModelType.BPMN).build();
    List<TemplateDetails> dmnTemplateDetailsList =
        Collections.singletonList(TemplateDetails.builder().modelType(ModelType.DMN).build());
    List<TriggerDetails> triggerDetailsList =
        Collections.singletonList(Mockito.mock(TriggerDetails.class));
    state = new State();
    state.addValue(AsyncTaskConstants.BPMN_DETAILS_KEY, bpmnTemplateDetails);
    state.addValue(AsyncTaskConstants.DMN_DETAILS_KEY, dmnTemplateDetailsList);
    state.addValue(AsyncTaskConstants.TRIGGER_DETAILS_KEY, triggerDetailsList);
    saveTemplateDetailsTask =
        new SaveTemplateDetailsTask(templateDetailsRepository, triggerDetailsRepository, templateDomainEventHandler);
  }

  @Test
  public void testExecute_NoDeployment() {
    saveTemplateDetailsTask.execute(state);
    Mockito.verify(templateDetailsRepository).saveAll(anyList());
    Mockito.verify(triggerDetailsRepository).saveAll(anyList());
  }

  @Test
  public void testExecute_WithDeployment() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedProcessDefinitions =
        new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("1234");
    deployedDefinition.setKey("invoiceapproval");
    deployedProcessDefinitions.put("1234", deployedDefinition);
    deployDefinitionResponse.setDeployedProcessDefinitions(deployedProcessDefinitions);
    state.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY, deployDefinitionResponse);
    saveTemplateDetailsTask.execute(state);
    Mockito.verify(templateDetailsRepository).saveAll(anyList());
    Mockito.verify(triggerDetailsRepository).saveAll(anyList());
  }

  @Test
  public void testExecute_Exception(){
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedProcessDefinitions =
            new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
            new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("1234");
    deployedDefinition.setKey("invoiceapproval");
    deployedProcessDefinitions.put("1234", deployedDefinition);
    deployDefinitionResponse.setDeployedProcessDefinitions(deployedProcessDefinitions);
    state.addValue(AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY, deployDefinitionResponse);
    Mockito.doThrow(
            new RuntimeException()).
            when(triggerDetailsRepository).saveAll(any());
    state = saveTemplateDetailsTask.execute(state);
    Assert.assertNotNull(state);
    Mockito.verify(templateDetailsRepository).saveAll(anyList());
  }
}
