package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionDisableCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DefinitionCommandsTest {

  @Mock private DefinitionDisableCommand command;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(command.getName()).thenReturn(CrudOperation.DISABLED.name());
    DefinitionCommands.addCommand(command.getName(), command);
  }

  @Test
  public void getTestNUll() {
    DefinitionCommand command = DefinitionCommands.getCommand(null);
    Assert.assertNull(command);
  }

  @Test
  public void getTestRuleHandler() {
    DefinitionCommand command = DefinitionCommands.getCommand(CrudOperation.DISABLED.name());
    Assert.assertNotNull(command);
  }
}
