package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the StartEventFlowNodeProcessor class.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class StartEventFlowNodeProcessorTest {

  @InjectMocks
  StartEventFlowNodeProcessor startEventFlowNodeProcessor;

  @Mock DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private FlowNode flowNode;
  private FlowNode baseTemplateFlowNode;
  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private SubProcess subProcess;
  private SubProcess baseTemplateSubprocess;

  @Before
  public void setup() {
    bpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    baseTemplateBpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
  }

  @Test
  public void testGetType() {
    Assert.assertEquals(BpmnComponentType.START_EVENT, startEventFlowNodeProcessor.getType());
  }

  @Test
  public void testAddExtensionElementsWithAllParametersAsNull() {
    startEventFlowNodeProcessor.addExtensionElements(null, null, null, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.never())
        .addAllValidExtensionElements(
            any(FlowNode.class), any(FlowNode.class), any(BpmnModelInstance.class));
  }

  @Test
  public void testAddExtensionElementsWithNullBaseTemplateFlowNodeAndNullBpmnModelInstance() {
    bpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    flowNode = bpmnModelInstance.getModelElementById("startEvent");
    startEventFlowNodeProcessor.addExtensionElements(
        flowNode, null, null, baseTemplateBpmnModelInstance);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddExtensionElementsWithNullFlowNodeAndNullBaseTemplateBpmnModelInstance() {
    bpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    startEventFlowNodeProcessor.addExtensionElements(
        null, baseTemplateFlowNode, bpmnModelInstance, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddEventToSubProcess() {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> processOptional = processes.stream().findFirst();
    processOptional.ifPresent(process -> process.builder().eventSubProcess().done());
    subProcess =
        bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst().get();

    Collection<Process> baseTemplateProcesses =
        baseTemplateBpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> baseTemplateProcessOptional = baseTemplateProcesses.stream().findFirst();
    baseTemplateProcessOptional.ifPresent(
        process ->
            process
                .builder()
                .eventSubProcess()
                .startEvent("subProcessStartNode")
                .escalation("escalationCode")
                .done());
    baseTemplateSubprocess =
        baseTemplateBpmnModelInstance.getModelElementsByType(SubProcess.class).stream()
            .findFirst()
            .get();

    startEventFlowNodeProcessor.addEventToSubProcess(
        null, subProcess, baseTemplateSubprocess, null);

    Collection<StartEvent> subprocessStartEvents =
        new ArrayList<>(subProcess.getChildElementsByType(StartEvent.class));
    Assert.assertEquals(1, subprocessStartEvents.size());

    Assert.assertEquals(
        "escalationCode",
        DynamicBpmnUtil.getEscalationCodeFromFlowNode(
            subprocessStartEvents.stream().findFirst().get()));
  }
}
