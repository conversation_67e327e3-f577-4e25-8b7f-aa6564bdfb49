package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.getGlobalId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BaseTemplateElementType;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * This class includes test cases for CallActivityWorkflowStepProcessor.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class CallActivityWorkflowStepProcessorTest {

  @InjectMocks
  CallActivityWorkflowStepProcessor callActivityWorkflowStepProcessor;

  private AbstractFlowNodeBuilder flowNodeBuilder;
  private WorkflowStep workflowStep;
  private WorkflowStep parentWorkflowStep;
  private Map<String, WorkflowStep> workflowStepMap;
  private Map<String, String> dynamicActivityIdMap;
  private Map<String, String> effectiveParentIdMap;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData;

  @Before
  public void setUp() {
    flowNodeBuilder = Bpmn.createExecutableProcess().startEvent("effectiveParentId");
    workflowStep = new WorkflowStep();
    workflowStep.setId(getGlobalId("1234"));
    parentWorkflowStep = new WorkflowStep();
    workflowStepMap = new HashMap<>();
    dynamicActivityIdMap = new HashMap<>();
    effectiveParentIdMap = new HashMap<>();
    dynamicBpmnWorkflowStepProcessorMetaData =
        DynamicBpmnWorkflowStepProcessorMetaData.builder()
            .flowNodeBuilder(flowNodeBuilder)
            .workflowStep(workflowStep)
            .parentWorkflowStep(parentWorkflowStep)
            .workflowStepMap(workflowStepMap)
            .effectiveParentIdMap(effectiveParentIdMap)
            .baseTemplateBpmnModelInstance(baseTemplateBpmnModelInstance)
            .build();
  }

  @Test
  public void testGetStepType() {
    Assert.assertEquals(StepTypeEnum.ACTION, callActivityWorkflowStepProcessor.getStepType());
  }

  @Test
  public void testProcessDynamicBpmnStepForActionStepType() {
    ActionGroup actionGroup = new ActionGroup();
    Action action = new Action();
    action.setId(getGlobalId("precannedConditionId"));
    actionGroup.setAction(action);

    workflowStep.setActionGroup(actionGroup);
    workflowStep.setStepType(StepTypeEnum.ACTION);

    effectiveParentIdMap.put(workflowStep.getId().getLocalId(), "effectiveParentId");
    dynamicActivityIdMap.put("1234", "1234");
    dynamicActivityIdMap.put("effectiveParentId", "effectiveParentId");

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().callActivity("precannedConditionId").done();

    CamundaProperty camundaProperty =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperty.class);
    CamundaProperties camundaProperties =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperties.class);
    camundaProperty.setCamundaName("elementType");
    camundaProperty.setCamundaValue(BaseTemplateElementType.EXPLICIT.getName());
    camundaProperties.addChildElement(camundaProperty);
    ExtensionElements extensionElements =
        baseTemplateBpmnModelInstance.newInstance(ExtensionElements.class);
    extensionElements.addChildElement(camundaProperties);

    baseTemplateBpmnModelInstance
        .getModelElementById("precannedConditionId")
        .addChildElement(extensionElements);

    dynamicBpmnWorkflowStepProcessorMetaData.setWorkflowStep(workflowStep);
    dynamicBpmnWorkflowStepProcessorMetaData.setEffectiveParentIdMap(effectiveParentIdMap);
    dynamicBpmnWorkflowStepProcessorMetaData.setBaseTemplateBpmnModelInstance(
        baseTemplateBpmnModelInstance);
    dynamicBpmnWorkflowStepProcessorMetaData.setDynamicActivityIdMap(dynamicActivityIdMap);

    callActivityWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);

    BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("effectiveParentId"),
            bpmnModelInstance.getModelElementById(workflowStep.getId().getLocalId())));
  }

  @Test
  public void testProcessDynamicBpmnStepForActionStepTypeWithBindingAsLatest() {
    ActionGroup actionGroup = new ActionGroup();
    Action action = new Action();
    action.setId(getGlobalId("precannedConditionId"));
    actionGroup.setAction(action);

    workflowStep.setActionGroup(actionGroup);
    workflowStep.setStepType(StepTypeEnum.ACTION);

    effectiveParentIdMap.put(workflowStep.getId().getLocalId(), "effectiveParentId");
    dynamicActivityIdMap.put("1234", "1234");
    dynamicActivityIdMap.put("effectiveParentId", "effectiveParentId");

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess()
            .startEvent()
            .callActivity("precannedConditionId")
            .camundaCalledElementBinding("latest")
            .done();

    CamundaProperty camundaProperty =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperty.class);
    CamundaProperties camundaProperties =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperties.class);
    camundaProperty.setCamundaName("elementType");
    camundaProperty.setCamundaValue(BaseTemplateElementType.EXPLICIT.getName());
    camundaProperties.addChildElement(camundaProperty);
    ExtensionElements extensionElements =
        baseTemplateBpmnModelInstance.newInstance(ExtensionElements.class);
    extensionElements.addChildElement(camundaProperties);

    baseTemplateBpmnModelInstance
        .getModelElementById("precannedConditionId")
        .addChildElement(extensionElements);

    dynamicBpmnWorkflowStepProcessorMetaData.setWorkflowStep(workflowStep);
    dynamicBpmnWorkflowStepProcessorMetaData.setEffectiveParentIdMap(effectiveParentIdMap);
    dynamicBpmnWorkflowStepProcessorMetaData.setBaseTemplateBpmnModelInstance(
        baseTemplateBpmnModelInstance);
    dynamicBpmnWorkflowStepProcessorMetaData.setDynamicActivityIdMap(dynamicActivityIdMap);

    callActivityWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);

    BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("effectiveParentId"),
            bpmnModelInstance.getModelElementById(workflowStep.getId().getLocalId())));
  }

  @Test
  public void testProcessDynamicBpmnStepForParentConditionStepType() {
    workflowStep.setStepType(StepTypeEnum.CONDITION);

    dynamicBpmnWorkflowStepProcessorMetaData.setWorkflowStep(workflowStep);
    callActivityWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);

    BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertFalse(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("effectiveParentId"),
            bpmnModelInstance.getModelElementById(workflowStep.getId().getLocalId())));
  }

  @Test
  public void testGetDynamicActivityId() {
    dynamicActivityIdMap.put("decisionElement", "businessRuleTask-1");
    dynamicActivityIdMap.put("sendForApproval", "callActivity-1");

    WorkflowStep effectiveParentWorkflowStep = new WorkflowStep();
    Assert.assertEquals(
        "callActivity-2",
            callActivityWorkflowStepProcessor.getDynamicActivityId(dynamicActivityIdMap, effectiveParentWorkflowStep));
  }
}
