package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TriggerTargetAPI;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class TriggerEventMetaDataUtilTest {

  private Map<String, Object> triggerMessage;
  private Map<String, Object> entity;
  private Map<String, String> eventHeaders;
  private Map<String, String> invoice;

  @Mock
  private WASContextHandler wasContextHandler;

  @Before
  public void init() {
    triggerMessage = new HashMap<>();

    invoice = new HashMap<>();
    invoice.put("TxnId", "400");

    entity = new HashMap<>();
    entity.put("invoice", invoice);

    eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityType", "Invoice");
    eventHeaders.put("entityId", "400");
  }

  @Test
  public void testUpdateWASContext() {
    triggerMessage.put("entity", entity);
    triggerMessage.put("eventHeaders", eventHeaders);

    when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("testOfferingId");

    TransactionEntity txnEntity = new TransactionEntity(triggerMessage);
    TriggerEventMetaDataUtil.updateWASContext(txnEntity, wasContextHandler);

    Mockito.verify(wasContextHandler, Mockito.times(1)).addKey(WASContextEnums.ENTITY_ID, "400");
    Mockito.verify(wasContextHandler, Mockito.times(1))
        .addKey(WASContextEnums.OFFERING_ID, "testOfferingId");
    Mockito.verify(wasContextHandler, Mockito.times(1))
        .addKey(WASContextEnums.IDEMPOTENCY_KEY, "approval_400");
  }

  @Test
  public void testPrepareTriggerEvent() {
    triggerMessage.put("entity", entity);
    triggerMessage.put("eventHeaders", eventHeaders);

    TransactionEntity txnEntity = new TransactionEntity(triggerMessage);

    Trigger trigger = TriggerEventMetaDataUtil.prepareTriggerEvent(txnEntity, null);
    Assert.assertEquals("approval", trigger.getMetaData().getWorkflow());
    Assert.assertEquals("invoice", trigger.getMetaData().getEntityType());
    Assert.assertEquals("created",
        trigger.getMetaData().getEntityChangeIdentifier().getMessageName());
    Assert.assertEquals("400", trigger.getMetaData().getEntityId());
    Assert.assertNull(trigger.getMetaData().getProviderWorkflowId());
    Assert.assertEquals(TriggerTargetAPI.TRIGGER_V2, trigger.getMetaData().getTargetApi());
    Assert.assertFalse(trigger.getMetaData().isBlockProcessOnSignalFailure());
  }

  @Test
  public void testPrepareTriggerEvent_v1Scenario() {
    triggerMessage.put("entity", entity);
    triggerMessage.put("eventHeaders", eventHeaders);

    TransactionEntity txnEntity = new TransactionEntity(triggerMessage);

    Trigger trigger = TriggerEventMetaDataUtil.prepareTriggerEvent(txnEntity,
        TriggerTargetAPI.TRIGGER_V1);
    Assert.assertEquals("approval", trigger.getMetaData().getWorkflow());
    Assert.assertEquals("invoice", trigger.getMetaData().getEntityType());
    Assert.assertEquals("created",
        trigger.getMetaData().getEntityChangeIdentifier().getMessageName());
    Assert.assertEquals("400", trigger.getMetaData().getEntityId());
    Assert.assertNull(trigger.getMetaData().getProviderWorkflowId());
    Assert.assertEquals(TriggerTargetAPI.TRIGGER_V1, trigger.getMetaData().getTargetApi());
    Assert.assertFalse(trigger.getMetaData().isBlockProcessOnSignalFailure());
  }
}
