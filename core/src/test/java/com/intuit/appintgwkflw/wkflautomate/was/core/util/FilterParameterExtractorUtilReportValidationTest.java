package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FilterParameterExtractorUtilReportValidationTest {

    @Mock
    private CustomWorkflowConfig customWorkflowConfig;
    
    @Mock
    private DefinitionServiceHelper definitionServiceHelper;
    
    @Mock
    private ReportAccessValidator reportAccessValidator;
    
    private FilterParameterExtractorUtil filterParameterExtractorUtil;
    
    @BeforeEach
    void setUp() {
        filterParameterExtractorUtil = new FilterParameterExtractorUtil(
            customWorkflowConfig, definitionServiceHelper, reportAccessValidator);
    }
    
    @Test
    void testGetFilterParameterDetails_ReportWorkflow_ValidatesAccess() {
        // Arrange
        Definition definition = createReportDefinition();
        Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
        String currentWorkflowStepId = "step1";
        
        // Act
        filterParameterExtractorUtil.getFilterParameterDetails(definition, workflowStepMap, currentWorkflowStepId);
        
        // Assert
        verify(reportAccessValidator).validateReportIdsInExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
    }
    
    @Test
    void testGetFilterParameterDetails_NonReportWorkflow_SkipsValidation() {
        // Arrange
        Definition definition = createInvoiceDefinition();
        Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
        String currentWorkflowStepId = "step1";
        
        // Act
        filterParameterExtractorUtil.getFilterParameterDetails(definition, workflowStepMap, currentWorkflowStepId);
        
        // Assert
        verify(reportAccessValidator, never()).validateReportIdsInExpression(anyString());
    }
    
    @Test
    void testGetFilterParameterDetails_ReportWorkflow_ValidationFails() {
        // Arrange
        Definition definition = createReportDefinition();
        Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
        String currentWorkflowStepId = "step1";
        
        doThrow(new WorkflowGeneralException(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, "Access denied"))
            .when(reportAccessValidator).validateReportIdsInExpression(anyString());
        
        // Act & Assert
        WorkflowGeneralException exception = assertThrows(WorkflowGeneralException.class, 
            () -> filterParameterExtractorUtil.getFilterParameterDetails(definition, workflowStepMap, currentWorkflowStepId));
        
        assertEquals(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS, exception.getWorkflowError());
    }
    
    @Test
    void testGetFilterParameterDetails_DefinitionDetails_ValidatesAccess() {
        // Arrange
        DefinitionDetails definitionDetails = createReportDefinitionDetails();
        List<DefinitionDetails> dmnDefinitions = createDmnDefinitionsList();
        
        when(definitionServiceHelper.findByParentId(anyString())).thenReturn(Optional.of(dmnDefinitions));
        
        // Act
        filterParameterExtractorUtil.getFilterParameterDetails(definitionDetails);
        
        // Assert
        verify(reportAccessValidator).validateReportIdsInExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
    }
    
    @Test
    void testGetFilterParameterDetails_DefinitionDetails_NonReportWorkflow_SkipsValidation() {
        // Arrange
        DefinitionDetails definitionDetails = createInvoiceDefinitionDetails();
        List<DefinitionDetails> dmnDefinitions = createDmnDefinitionsList();
        
        when(definitionServiceHelper.findByParentId(anyString())).thenReturn(Optional.of(dmnDefinitions));
        
        // Act
        filterParameterExtractorUtil.getFilterParameterDetails(definitionDetails);
        
        // Assert
        verify(reportAccessValidator, never()).validateReportIdsInExpression(anyString());
    }
    
    @Test
    void testGetFilterParameterDetails_MultipleReportIds_ValidatesAll() {
        // Arrange
        Definition definition = createReportDefinitionWithMultipleIds();
        Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
        String currentWorkflowStepId = "step1";
        
        // Act
        filterParameterExtractorUtil.getFilterParameterDetails(definition, workflowStepMap, currentWorkflowStepId);
        
        // Assert
        verify(reportAccessValidator).validateReportIdsInExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
        verify(reportAccessValidator).validateReportIdsInExpression("CONTAINS sbg:3b29fe9g-e6f1-5d78-a031-4d4aecf340d3");
    }
    
    private Definition createReportDefinition() {
        Definition definition = new Definition();
        definition.setRecordType("report");
        
        WorkflowStep workflowStep = new WorkflowStep();
        WorkflowStepCondition condition = new WorkflowStepCondition();
        
        RuleLine ruleLine = new RuleLine();
        RuleLine.Rule rule = new RuleLine.Rule();
        rule.setParameterName("ReportId");
        rule.setConditionalExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
        
        ruleLine.setRules(Arrays.asList(rule));
        condition.setRuleLines(Arrays.asList(ruleLine));
        workflowStep.setWorkflowStepCondition(condition);
        
        definition.setWorkflowSteps(Arrays.asList(workflowStep));
        
        return definition;
    }
    
    private Definition createInvoiceDefinition() {
        Definition definition = new Definition();
        definition.setRecordType("invoice");
        
        WorkflowStep workflowStep = new WorkflowStep();
        WorkflowStepCondition condition = new WorkflowStepCondition();
        
        RuleLine ruleLine = new RuleLine();
        RuleLine.Rule rule = new RuleLine.Rule();
        rule.setParameterName("Amount");
        rule.setConditionalExpression("GT 1000");
        
        ruleLine.setRules(Arrays.asList(rule));
        condition.setRuleLines(Arrays.asList(ruleLine));
        workflowStep.setWorkflowStepCondition(condition);
        
        definition.setWorkflowSteps(Arrays.asList(workflowStep));
        
        return definition;
    }
    
    private Definition createReportDefinitionWithMultipleIds() {
        Definition definition = new Definition();
        definition.setRecordType("report");
        
        WorkflowStep workflowStep1 = new WorkflowStep();
        WorkflowStepCondition condition1 = new WorkflowStepCondition();
        
        RuleLine ruleLine1 = new RuleLine();
        RuleLine.Rule rule1 = new RuleLine.Rule();
        rule1.setParameterName("ReportId");
        rule1.setConditionalExpression("CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
        
        ruleLine1.setRules(Arrays.asList(rule1));
        condition1.setRuleLines(Arrays.asList(ruleLine1));
        workflowStep1.setWorkflowStepCondition(condition1);
        
        WorkflowStep workflowStep2 = new WorkflowStep();
        WorkflowStepCondition condition2 = new WorkflowStepCondition();
        
        RuleLine ruleLine2 = new RuleLine();
        RuleLine.Rule rule2 = new RuleLine.Rule();
        rule2.setParameterName("ReportId");
        rule2.setConditionalExpression("CONTAINS sbg:3b29fe9g-e6f1-5d78-a031-4d4aecf340d3");
        
        ruleLine2.setRules(Arrays.asList(rule2));
        condition2.setRuleLines(Arrays.asList(ruleLine2));
        workflowStep2.setWorkflowStepCondition(condition2);
        
        definition.setWorkflowSteps(Arrays.asList(workflowStep1, workflowStep2));
        
        return definition;
    }
    
    private DefinitionDetails createReportDefinitionDetails() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setRecordType(RecordType.REPORT);
        definitionDetails.setDefinitionId("test-definition-id");
        return definitionDetails;
    }
    
    private DefinitionDetails createInvoiceDefinitionDetails() {
        DefinitionDetails definitionDetails = new DefinitionDetails();
        definitionDetails.setRecordType(RecordType.INVOICE);
        definitionDetails.setDefinitionId("test-definition-id");
        return definitionDetails;
    }
    
    private List<DefinitionDetails> createDmnDefinitionsList() {
        DefinitionDetails dmnDefinition = new DefinitionDetails();
        
        Map<String, Object> ruleLineVariables = new HashMap<>();
        Map<String, String> rule = new HashMap<>();
        rule.put(WorkflowConstants.PARAMETER_NAME, "ReportId");
        rule.put(WorkflowConstants.CONDITIONAL_EXPRESSION, "CONTAINS sbg:2a18ed8f-d5f0-4c67-9b20-3c39dbf239c2");
        
        ruleLineVariables.put(WorkflowConstants.RULE_LINE_VARIABLES, Arrays.asList(rule));
        
        JSONObject placeholderValue = new JSONObject(ruleLineVariables);
        dmnDefinition.setPlaceholderValue(placeholderValue.toString());
        
        return Arrays.asList(dmnDefinition);
    }
}
