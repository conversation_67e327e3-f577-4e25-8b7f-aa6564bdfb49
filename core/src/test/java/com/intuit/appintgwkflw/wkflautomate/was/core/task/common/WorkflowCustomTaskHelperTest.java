package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowCustomTaskHelperTest {


  @Mock
  private ProcessDetailsRepository processDetailRepo;

  @Mock
  private ActivityDetailsRepository activityDetailRepo;

  @Mock
  private ActivityProgressDetailsRepository activityProgressDetailsRepository;

  @Mock
  private WASContextHandler wasContextHandler;

  @Mock
  private WorkflowTaskConfig workflowTaskConfig;
  
  @Mock
  private WorkflowExternalTaskManager workflowTaskManager;

  @InjectMocks
  private WorkflowCustomTaskHelper workflowCustomTaskHelper;

  @Test
  public void test_prepareWorkflowTaskRequestForIncident_success() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(processDetails));

    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
        .modelAttributes(modelDefAttributes).runtimeAttributes(runtimeDefAttributes).build();

    ActivityDetail activityDetails = ActivityDetail.builder().id(10).activityId("actId1")
        .activityName("actName1").attributes(ObjectConverter.toJson(activityAttributes))
        .templateDetails(templateDetails).type(TaskType.HUMAN_TASK).build();

    Mockito
        .when(activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(), Mockito.any()))
        .thenReturn(Optional.ofNullable(activityDetails));

    WorkflowTaskRequest.WorkflowTaskRequestBuilder requestBuilder = workflowCustomTaskHelper
        .prepareWorkflowTaskRequestForIncident(workerActionRequest);
    Assert.assertNotNull(requestBuilder);
  }


  @Test
  public void test_prepareWorkflowTaskRequestForIncident_ActivityDefNotFound() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
        .templateDetails(templateDetails)
        .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().recordId("record1")
        .definitionDetails(definitionDetails).ownerId(1l).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.of(processDetails));

    Mockito
        .when(activityDetailRepo.findByTemplateDetailsAndActivityId(Mockito.any(), Mockito.any()))
        .thenReturn(Optional.ofNullable(null));

    WorkflowTaskRequest.WorkflowTaskRequestBuilder requestBuilder = workflowCustomTaskHelper
        .prepareWorkflowTaskRequestForIncident(workerActionRequest);
    Assert.assertNull(requestBuilder);

    Mockito.verify(processDetailRepo, Mockito.times(1)).findById(Mockito.anyString());
    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(Mockito.any(), Mockito.any());
  }


  @Test
  public void test_prepareWorkflowTaskRequestForIncident_ProcessDetailNotFound() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    WorkflowTaskRequest.WorkflowTaskRequestBuilder requestBuilder = workflowCustomTaskHelper
        .prepareWorkflowTaskRequestForIncident(workerActionRequest);
    Assert.assertNull(requestBuilder);

    Mockito.verify(processDetailRepo, Mockito.times(1)).findById(Mockito.anyString());
    Mockito.verify(activityDetailRepo, Mockito.never())
        .findByTemplateDetailsAndActivityId(Mockito.any(), Mockito.any());
  }

  /** 
   * Testing free text to work.
   */
  @Test
  public void test_processCustomTask_update_freeText() {
	  ExternalTaskCompleted event = ExternalTaskCompleted.builder().status("in-process1").build();
      Pair<String, String> taskIdAndWorkerId = new ImmutablePair<>("taskId","workerId");
      
      TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
      DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
          .templateDetails(templateDetails)
          .recordType(RecordType.INVOICE).build();
      ProcessDetails processDetails = ProcessDetails.builder().processId("pId1").recordId("record1")
          .definitionDetails(definitionDetails).ownerId(1l).build();
      
      Map<String, String> modelAttributes = new HashMap<>();
      modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
      modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

      Map<String, Object> runtimeAttributes = new HashMap<>();
      runtimeAttributes.put("assigneeId", "assignee1");

      WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
          .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();
      
      ActivityDetail activityDefinitionDetail = ActivityDetail.builder()
    		  .attributes(ObjectConverter.toJson(activityAttributes)).activityId("actId")
    		  .activityName("actName")
    	      .activityType("serviceTask")
    	      .type(TaskType.SYSTEM_TASK)
    		  .build();
      
      TransactionDetails txnDetails = TransactionDetails.builder().id(1l).status("created").build();
      
      ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
    		  .processDetails(processDetails).activityDefinitionDetail(activityDefinitionDetail)
    		  .attributes(ObjectConverter.toJson(activityAttributes)).txnDetails(txnDetails)
    		  .build();
      Mockito.when(workflowTaskManager.execute(Mockito.any(WorkflowTaskRequest.class)))
      .thenReturn(WorkflowTaskResponse.builder().build());
      
      ReflectionTestUtils.invokeMethod(workflowCustomTaskHelper, "processCustomTask", event, taskIdAndWorkerId, activityProgressDetails);
      
      Mockito.verify(workflowTaskManager, Mockito.times(1)).execute(Mockito.any(WorkflowTaskRequest.class));
  }

  @Test
  public void test_prepareWorkflowTaskRequestForIncident_Milestone() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put("events", "start");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest =
        WorkerActionRequest.builder()
            .activityId("act1")
            .extensionProperties(extensionProperties)
            .processInstanceId("processInstance1")
            .taskId("taskId1")
            .ownerId(1l)
            .workerId("worker1")
            .variableMap(variableMap1)
            .build();
    Mockito.when(processDetailRepo.findById(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    WorkflowTaskRequest.WorkflowTaskRequestBuilder requestBuilder =
        workflowCustomTaskHelper.prepareWorkflowTaskRequestForIncident(workerActionRequest);
    Assert.assertNull(requestBuilder);

    Mockito.verify(processDetailRepo, Mockito.times(1)).findById(Mockito.anyString());
    Mockito.verify(activityDetailRepo, Mockito.never())
        .findByTemplateDetailsAndActivityId(Mockito.any(), Mockito.any());
  }

  @Test
  public void shouldThrowRetriableException() {

    ExternalTaskCompleted externalTaskCompleted = ExternalTaskCompleted.builder().status("in-process1").build();

    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP, Instant.now().toString());
    headers.put(EventHeaderConstants.ENTITY_ID, "taskId:workerId");
    headers.put(EventHeaderConstants.ENTITY_TYPE, EventEntityType.TRIGGER.getEntityType());
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP_TYPE, "logAppendTime");

    TemplateDetails templateDetails = TemplateDetails.builder().offeringId("offering1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().version(1)
            .templateDetails(templateDetails)
            .recordType(RecordType.INVOICE).build();
    ProcessDetails processDetails = ProcessDetails.builder().processId("pId1").recordId("record1")
            .definitionDetails(definitionDetails).ownerId(1l).build();

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "SYSTEM_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("assigneeId", "assignee1");

    WorkflowActivityAttributes activityAttributes = WorkflowActivityAttributes.builder()
            .modelAttributes(modelAttributes).runtimeAttributes(runtimeAttributes).build();

    ActivityDetail activityDefinitionDetail = ActivityDetail.builder()
            .attributes(ObjectConverter.toJson(activityAttributes)).activityId("actId")
            .activityName("actName")
            .activityType("serviceTask")
            .type(TaskType.SYSTEM_TASK)
            .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
            .processDetails(processDetails).activityDefinitionDetail(activityDefinitionDetail)
            .attributes(ObjectConverter.toJson(activityAttributes))
            .build();

    when(workflowTaskConfig.isEnable()).thenReturn(true);
    when(activityProgressDetailsRepository.findById("taskId")).thenReturn(Optional.of(activityProgressDetails));

    assertThrows(WorkflowRetriableException.class, () ->
            workflowCustomTaskHelper.checkAndProcessCustomTask(externalTaskCompleted, headers, EventEntityType.EXTERNALTASKTEST));
  }
}
