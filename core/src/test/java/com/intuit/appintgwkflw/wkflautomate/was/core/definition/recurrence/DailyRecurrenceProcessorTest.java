package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import java.util.Arrays;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class DailyRecurrenceProcessorTest {

  @InjectMocks private DailyRecurrenceProcessor recurrenceProcessor;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRecurrenceDaily() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.DAILY, null, null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 * * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceDaily_Every4Days() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.DAILY, null, null, null, null, new DateTime());

    Assert.assertEquals("0 0 0 */4 * ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceDaily_Every2DaysInJanuaryAndDecember() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            2,
            RecurTypeEnum.DAILY,
            null,
            null,
            Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.DECEMBER),
            null,
            new DateTime());

    Assert.assertEquals("0 0 0 */2 1,12 ? *", recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRecurrenceDaily_Every35DaysInJanuaryAndDecember() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            35,
            RecurTypeEnum.DAILY,
            null,
            null,
            Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.DECEMBER),
            null,
            new DateTime());

    recurrenceProcessor.getRecurrence(recurrenceRule);
  }

  @Test
  public void testBuildESSRecurrencePattern_Daily() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.DAILY, null, null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(1)
            .type(RecurrencePatternType.DAILY);

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Daily_Every4Days() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            4, RecurTypeEnum.DAILY, null, null, null, null, new DateTime());

    RecurrencePattern recurrencePattern =
        new RecurrencePattern()
            .interval(4)
            .type(RecurrencePatternType.DAILY);

    Assert.assertEquals(
        recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }
}
