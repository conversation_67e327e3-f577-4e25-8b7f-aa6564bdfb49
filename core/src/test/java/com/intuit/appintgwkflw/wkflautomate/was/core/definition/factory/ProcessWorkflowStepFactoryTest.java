package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepCompositeBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.List;

public class ProcessWorkflowStepFactoryTest {
    Steps configStep = new Steps();
    @MockBean
    private MultiStepConditionBuilder multiStepConditionBuilder;
    @MockBean
    private MultiStepActionBuilder multiStepActionBuilder;
    @MockBean
    private MultiStepCompositeBuilder multiStepCompositeBuilder;
    @InjectMocks
    private ProcessWorkflowStepFactory processWorkflowStepFactory;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        configStep.setStepId(1);
        configStep.setStepType(StepTypeEnum.CONDITION.value());
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(NextTypeEnum.ACTION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
    }

    @Test
    public void testProcessStepWithConditionHandler() {
        Assert.assertEquals(multiStepConditionBuilder,
                processWorkflowStepFactory.getHandler(configStep));
    }

    @Test
    public void testProcessStepWithActionHandler() {
        configStep.setStepType(NextTypeEnum.ACTION.value());
        Assert.assertEquals(multiStepActionBuilder,
                processWorkflowStepFactory.getHandler(configStep));
    }

    @Test
    public void testProcessStepWithCompositeHandler() {
        configStep.setStepType(StepTypeEnum.CONDITION.value());
        Attribute attribute = new Attribute();
        attribute.setType(FieldTypeEnum.DAYS.name());
        configStep.setAttributes(List.of(attribute));
        Assert.assertEquals(multiStepCompositeBuilder,
            processWorkflowStepFactory.getHandler(configStep));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testProcessStepWithInvalidArgument() {
        configStep.setStepType("invalid");
        try {
            processWorkflowStepFactory.getHandler(configStep);
            Assert.fail("Method should throw exception");
        } catch (IllegalArgumentException e) {
            Assert.assertEquals("invalid not supported", e.getMessage());
            throw e;
        }
    }
}
