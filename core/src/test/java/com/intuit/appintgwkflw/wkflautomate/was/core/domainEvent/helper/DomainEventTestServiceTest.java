package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class DomainEventTestServiceTest {
  @InjectMocks private DomainEventTestServiceImpl domainEventTestService;
  @Mock private DomainEventRepository domainEventRepository;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testGetEventById() {
    UUID eventId = UUID.randomUUID();
    Mockito.when(domainEventRepository.findByEventId(eventId.toString()))
        .thenReturn(DomainEvent.builder().eventId(UUID.randomUUID()).build());
    domainEventTestService.getEventById(eventId.toString());
    Mockito.verify(domainEventRepository).findByEventId(Mockito.any());
  }

  @Test
  public void testGetEventsByPartitionKey() {
    String partitionKey = "partitionKey";
    Mockito.when(domainEventRepository.findDomainEventByPartitionKey(partitionKey))
        .thenReturn(
            Optional.ofNullable(List.of(DomainEvent.builder().eventId(UUID.randomUUID()).build())));
    domainEventTestService.getEventsByPartitionKey(partitionKey);
    Mockito.verify(domainEventRepository).findDomainEventByPartitionKey(Mockito.any());
  }

  @Test
  public void getTotalEventsPublished() {
    String partitionKey = "partitionKey";
    Mockito.when(domainEventRepository.countByPartitionKey(partitionKey)).thenReturn(1l);
    domainEventTestService.getCountByEventsPublishedByPartitionKey(partitionKey);
    Mockito.verify(domainEventRepository).countByPartitionKey(Mockito.any());
  }
}
