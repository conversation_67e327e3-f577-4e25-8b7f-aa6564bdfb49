package com.intuit.appintgwkflw.wkflautomate.was.event.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * <p>
 * This class checks if for a kafka message needs to be processed or skipped based on the FF.
 */

@Component
@AllArgsConstructor
public class FilterEventUtil {

  private final WASContextHandler contextHandler;

  private final FeatureFlagManager featureFlagManager;


  /**
   * @param eventEntityType eventEntityType
   * @return true if message needs to be dropped or processed.
   */
  public boolean evaluate(EventEntityType eventEntityType) {
    String featureFlagName = getFeatureFlagName(eventEntityType);
    Map<String, Object> contextMap = getContextMap();
    EventingLoggerUtil.logInfo(
        "Evaluating FF=%s eventEntityType=%s contextMap=%s",
        this.getClass().getSimpleName(), featureFlagName, eventEntityType, contextMap
    );
    return featureFlagManager.getBooleanWithContextMap(featureFlagName, false, getContextMap(),
        getOwnerId());
  }

  /**
   * This method returns the feature flag name on the basis of eventEntityType.
   *
   * @param eventEntityType
   * @return
   */
  private String getFeatureFlagName(EventEntityType eventEntityType) {
    return String.format(WorkflowConstants.NUMAFLOW_ENABLED_FF, eventEntityType.getEntityType());
  }

  /**
   * This method return the context map for feature flag evaluation.
   *
   * @return
   */
  public Map<String, Object> getContextMap() {
    Map<String, Object> contextMap = new HashMap<>();
    contextMap.put(WorkflowConstants.INTUIT_REALMID, contextHandler.get(WASContextEnums.OWNER_ID));
    contextMap.put(WorkflowConstants.OFFERING_ID, contextHandler.get(WASContextEnums.OFFERING_ID));
    return contextMap;
  }

  /**
   * This method returns the ownerId
   *
   * @return
   */
  private Long getOwnerId() {
    try {
      return Long.valueOf(contextHandler.get(WASContextEnums.OWNER_ID));
    } catch (NumberFormatException numberFormatException) {
      EventingLoggerUtil.logWarning(
          "Exception occurred while converting ownerId",
          this.getClass().getSimpleName(),
          ExceptionUtils.getStackTrace(numberFormatException));
    }
    return null;
  }

}
