package com.intuit.appintgwkflw.wkflautomate.was.event.gc;

import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * Configuration class for managing a thread pool used for executing failed events.
 *
 * <AUTHOR>
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "gc.thread-pool")
@ConditionalOnExpression("${gc.serviceEnabled:false}")
public class FailedEventExecutorThreadPool {
    private int minThreads;
    private int maxThreads;
    private int keepAliveTimeInSec;
    private int queueSize;

    @Bean(name = WorkflowConstants.GC_EXECUTOR_THREAD_BEAN)
    public ThreadPoolExecutor threadPoolSQSQueue() {
        return ThreadPoolExecutorFactory.createExecutor(
                WorkflowConstants.SQS_EXECUTOR_THREAD,
                queueSize,
                minThreads,
                maxThreads,
                keepAliveTimeInSec);
    }
}
