package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowCircuitOpenException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.adapter.RetryingMessageListenerAdapter;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class EventRetryHandler {

  private WASContextHandler contextHandler;

  /**
   * extract the consumer record and headers from the retry context and passed onto the
   * RecoveryCallbackHandler method to execute post retry failure actions
   *
   * @param context kafka retry context
   * @param callback recovery callback handler to handle post retry failure actions
   */
  public void handleRetryFailure(RetryContext context, RecoveryCallbackHandler callback) {
    ConsumerRecord<String, Result<FormatException, String>> consumerRecord =
        (ConsumerRecord<String, Result<FormatException, String>>)
            context.getAttribute(RetryingMessageListenerAdapter.CONTEXT_RECORD);
    WorkflowVerfiy.verifyNull(
        consumerRecord, WorkflowError.MISSING_CONSUMER_RECORD_DETAILS_RETRY_CONTEXT);
    Acknowledgment acknowledgment =
        (Acknowledgment)
            context.getAttribute(RetryingMessageListenerAdapter.CONTEXT_ACKNOWLEDGMENT);
    WorkflowVerfiy.verifyNull(
        acknowledgment, WorkflowError.MISSING_ACKNOWLEDGEMENT_DETAILS_RETRY_CONTEXT);
    Map<String, String> headers = EventUtil.transformHeader(consumerRecord.headers());
    headers.putIfAbsent(KafkaConstants.KAFKA_TOPIC_HEADER, consumerRecord.topic());

    try {
      EventUtil.populateEventContext(contextHandler, headers);
      EventingLoggerUtil.logInfo(
          "Event processing errored after %s attempt(s) for recordHeaders=%s | topic=%s",
          this.getClass().getSimpleName(),
          context.getRetryCount(),
          headers,
          consumerRecord.topic());

      callback.retryFailureHandler(consumerRecord, headers);
    } catch (Exception e) {
      EventingLoggerUtil.logError(
          "Error in consumer recovery callback. body=%s header=%s",
          e,
          this.getClass().getSimpleName(),
          consumerRecord.value().get(),
          ObjectConverter.toJson(headers));
    } finally {
      acknowledgment.acknowledge();
      contextHandler.clear();
    }
  }

  public boolean isRetryableException(RetryContext context) {
    return (context.getLastThrowable().getCause() instanceof WorkflowRetriableException);
  }

  public boolean isWorkflowCircuitOpenException(RetryContext context) {
    return (context.getLastThrowable().getCause() instanceof WorkflowCircuitOpenException);
  }
}
