package com.intuit.appintgwkflw.wkflautomate.was.event.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.v4.Authorization;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.support.MessageHeaderAccessor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEFAULT_OFFERING;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.REALM_ID;

@UtilityClass
public class EventUtil {

    public static final List<String> excludedHeaders =
        List.of("id", "timestamp");

  public Map<String, String> transformHeader(@Headers MessageHeaders messageHeaders) {
    final Map<String, String> customMessageHeaders = new HashMap<>();
    messageHeaders.forEach(
        (key, value) -> {
          if (value != null) customMessageHeaders.put(key, convertHeaderToString(value));
        });
    return customMessageHeaders;
  }

  public static MessageHeaders transformHeaders(Map<String, String> headers) {
    MessageHeaderAccessor accessor = new MessageHeaderAccessor();
    headers.forEach(accessor::setHeader);
    return accessor.getMessageHeaders();
  }

  public Map<String, String> transformHeader(org.apache.kafka.common.header.Headers headers) {
    final Map<String, String> customMessageHeaders = new HashMap<>();
    headers.forEach(
        header -> {
          if (header.value() != null) {
            customMessageHeaders.put(header.key(), convertHeaderToString(header.value()));
          }
        });
    return customMessageHeaders;
  }

  public String convertHeaderToString(Object value) {
    return (value instanceof byte[]) ? new String((byte[]) value) : value.toString();
  }

  /**
   * populates contexts for events
   *
   * @param contextHandler
   * @param headers
   */
  public void populateEventContext(WASContextHandler contextHandler, Map<String, String> headers) {

    contextHandler.addKey(WASContextEnums.IS_EVENT, Boolean.TRUE.toString());
    contextHandler.addKey(WASContextEnums.INTUIT_TID, headers.get(EventHeaderConstants.INTUIT_TID));
    contextHandler.addKey(WASContextEnums.ENTITY_ID, headers.get(EventHeaderConstants.ENTITY_ID));
    contextHandler.addKey(
        WASContextEnums.OFFERING_ID, headers.get(EventHeaderConstants.OFFERING_ID));
    contextHandler.addKey(WASContextEnums.HANDLER_ID, headers.get(EventHeaderConstants.HANDLER_ID));

    // Logging topic in case of DLQ's as the tid is same it as the main conusmer. So this will allow
    // to differentiate
    String isDLQ = WorkflowConstants.ZERO;
    if (isDLQ(headers.get(KafkaConstants.KAFKA_TOPIC_HEADER))) {
      isDLQ = WorkflowConstants.ONE;
    }
    contextHandler.addKey(WASContextEnums.IS_DLQ, isDLQ);

    String ownerId = getOwnerId(headers);
    WorkflowLogger.logInfo("event headers are %s ", headers);
    WorkflowLogger.logInfo(
        "intuit_userid in the event is %s ",
        headers.get(EventHeaderConstants.INTUIT_USER_ID)
    );
    if (StringUtils.isNotBlank(ownerId)) {
      Authorization auth = new Authorization().realm(ownerId);
      if (StringUtils.isNotBlank(headers.get(EventHeaderConstants.INTUIT_USER_ID))){
        auth.putAuthId(headers.get(EventHeaderConstants.INTUIT_USER_ID));
        contextHandler.addKey(WASContextEnums.INTUIT_USERID,headers.get(EventHeaderConstants.INTUIT_USER_ID));
        WorkflowLogger.logInfo(
            "intuit_userid is set in the auth header as %s ",
            auth.getAuthId()
        );
      }
      contextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, auth.toString());
      contextHandler.addKey(WASContextEnums.OWNER_ID, ownerId);
      WASContext.setAuthContext(auth);
    }
  }

  public boolean isDLQ(Object topicName) {
    return topicName != null
        && topicName instanceof String
        && ((String) topicName).endsWith(KafkaConstants.KAFKA_DLQ_SUFFIX);
  }

  public void setOfferingId(Map<String, String> headers) {
    WASContext.setOfferingId(ObjectUtils.isEmpty(headers.get(EventHeaderConstants.OFFERING_ID)) ? DEFAULT_OFFERING : convertHeaderToString(headers.get(EventHeaderConstants.OFFERING_ID)));
  }


  /**
   * This method returns all the dlq headers.
   *
   * @param headers
   * @return
   */
  public Map<String, String> getDLQHeaders(Map<String, String> headers, String topicName) {
    Map<String, String> dlqHeaders = new HashMap<>();
    dlqHeaders.put(EventHeaderConstants.INTUIT_TID,
        headers.getOrDefault(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString()));
    dlqHeaders.put(EventHeaderConstants.IDEMPOTENCY_KEY,
        headers.getOrDefault(EventHeaderConstants.IDEMPOTENCY_KEY, UUID.randomUUID().toString()));
    dlqHeaders.put(EventHeaderConstants.ENTITY_ID, headers.get(EventHeaderConstants.ENTITY_ID));
    dlqHeaders.put(EventHeaderConstants.OWNER_ID, headers.get(EventHeaderConstants.OWNER_ID));
    dlqHeaders.put(EventHeaderConstants.OFFERING_ID, headers.get(EventHeaderConstants.OFFERING_ID));
    dlqHeaders.put(EventHeaderConstants.DOMAIN_EVENT,
        headers.get(EventHeaderConstants.DOMAIN_EVENT));
    dlqHeaders.put(EventHeaderConstants.INTUIT_USER_ID,
        headers.get(EventHeaderConstants.INTUIT_USER_ID));
    dlqHeaders.put(KafkaHeaders.TOPIC, topicName);
    return dlqHeaders;

  }

  /**
   * This method converts bytes to string
   *
   * @param value
   * @return
   */
  public String convertBytesToString(byte[] value) {
    return new String(value);
  }

  /**
   * This method check is throwable instance is type of WorkflowEventException or not.
   * @param throwable
   * @return
   */
  public boolean isWorkflowEventException(Throwable throwable) {
    return throwable instanceof WorkflowEventException;
  }

  public MessageHeaders getGCHeaders(Map<String, String> headers, EventEntityType entityType){
    Map<String, String> gcHeaders = new HashMap<>(headers);
    gcHeaders = excludeHeaders(gcHeaders);
    gcHeaders.put(EventHeaderConstants.ENTITY_TYPE, entityType.toString());
    gcHeaders.put(REALM_ID, headers.getOrDefault(EventHeaderConstants.OWNER_ID, ""));
    return transformHeaders(gcHeaders);
  }



  public HashMap<String, Object> getGCMetadata(String topicName, String consumerGroup){
    HashMap<String, Object> metadata = new HashMap<>();
    metadata.put(EventHeaderConstants.TOPIC, topicName);
    metadata.put(EventHeaderConstants.CONSUMER_GROUP, consumerGroup);
    return metadata;
  }

  public String getOwnerId(Map<String, String> headers) {
    if(ObjectUtils.isEmpty(headers.get(EventHeaderConstants.OWNER_ID))){
      return headers.get(EventHeaderConstants.ACCOUNT_ID);
    }else{
      return headers.get(EventHeaderConstants.OWNER_ID);
    }
  }

  public Map<String, String> excludeHeaders(Map<String, String> headers) {
    excludedHeaders.forEach(headers::remove);
    return headers;
  }
}

