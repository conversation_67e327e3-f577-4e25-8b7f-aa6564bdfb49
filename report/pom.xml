<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>workflow-automation-service-aggregator</artifactId>
        <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
        <version>1.1.20</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>was-report</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-graphql-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-aop</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-app</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-batch</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-worker</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-eventconsumer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
            <artifactId>was-localisation</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <configuration>
                    <dataFile>${project.testresult.directory}/coverage/jacoco/jacoco.exec</dataFile>
                    <outputDirectory>${project.testresult.directory}/coverage/jacoco
                    </outputDirectory>
                    <excludes>
                        <exclude>**/dataaccess/entities/**</exclude>
                        <exclude>**/dataaccess/constants/**</exclude>
                        <exclude>**/entity/**</exclude>
                        <exclude>**/batch/**/*Constants*</exclude>
                        <exclude>**/batch/**/*Config*</exclude>
                        <exclude>**/batch/**/CacheWarmupImpl.java</exclude>
                        <exclude>**/core/**/Constants*</exclude>
                        <exclude>**/core/**/CamundaElements*</exclude>
                        <exclude>**/core/**/DefinitionInstance*</exclude>
                        <exclude>**/core/**/rxhooks/**/*</exclude>
                        <exclude>**/common/**/exception/**/*</exclude>
                        <exclude>**/common/**/config/**/*</exclude>
                        <exclude>**/common/**/aop/**/*</exclude>
                        <exclude>**/common/**/WebMvcConfig*</exclude>
                        <exclude>**/observability/SpanDecorator*</exclude>
                        <exclude>**/app/**/filter/**/*</exclude>
                        <exclude>**/app/**/controller/**/ProgressTrackingController*</exclude>
                        <exclude>**/app/**/controller/**/ProgressTrackingController*</exclude>
                        <exclude>**/app/config/FilterConfig*</exclude>
                        <exclude>**/app/config/GraphqlAutoConfiguration*</exclude>
                        <exclude>**/app/config/WASLocalisationConfig*</exclude>
                        <exclude>**/was/Application*</exclude>
                        <exclude>**/was/signalscience/SigScienceConfig*</exclude>
                        <exclude>**/event/**/KafkaConsumerConfig*</exclude>
                        <exclude>**/LoggerUtil*</exclude>
                        <exclude>**/event/consumer/config/*Config.*</exclude>
                    </excludes>
                    <!-- Sets the path to the file which contains the execution data. -->
                    <destFile>${project.testresult.directory}/coverage/jacoco/jacoco.exec</destFile>
                </configuration>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/target/output</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${session.executionRootDirectory}</directory>
                                    <includes>
                                        <include>**/target/was-app.jar</include>
                                        <include>**/target/surefire-reports/*.xml</include>
                                        <include>**/target/*.exec</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
