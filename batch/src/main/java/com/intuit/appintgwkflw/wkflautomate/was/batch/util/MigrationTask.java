package com.intuit.appintgwkflw.wkflautomate.was.batch.util;

import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.workflows.Definition;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;

/**
 * Task to execute the migration of a definition
 */
@AllArgsConstructor
public class MigrationTask implements Task {

  private final MigrationServiceHelper migrationServiceHelper;

  private final DefinitionDetails definitionDetails;

  private final BatchJobInitContext batchJobInitContext;

  @Override
  public State execute(State state) {
    try {
      String templateId = state.getValue(WorkflowConstants.TEMPLATE_ID);
      Definition migratedDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
              batchJobInitContext.generateAuthorization(definitionDetails),
              templateId);
      state.addValue(WorkflowConstants.MIGRATED_DEFINITION, migratedDefinition);
      WorkflowLogger.logInfo(
          "step=MigrationBatchProcessingComplete status=success definitionId=%s updatedTemplateId=%s updatedDefinitionId=%s",
          definitionDetails.getDefinitionId(), templateId, migratedDefinition.getId());
    } catch (Exception ex) {
      definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
      WorkflowLogger.logError(
          "step=MigrationProcessInitFailure, status=failed, error=%s definitionId=%s",
          ex.getStackTrace(), definitionDetails.getDefinitionId());
    }
    return state;
  }
}
