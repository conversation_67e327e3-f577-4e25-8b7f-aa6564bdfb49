<gradleEnterprise>
  <server>
    <url>https://gradleenterprise-e2e.intuit.com</url>
  </server>
  <buildCache>
    <remote>
      <storeEnabled>true</storeEnabled>
    </remote>
  </buildCache>
  <buildScan>
    <backgroundBuildScanUpload>#{env['CI'] == null}</backgroundBuildScanUpload>
    <publish>ALWAYS</publish>
    <capture>
      <goalInputFiles>true</goalInputFiles>
    </capture>
  </buildScan>
</gradleEnterprise>
