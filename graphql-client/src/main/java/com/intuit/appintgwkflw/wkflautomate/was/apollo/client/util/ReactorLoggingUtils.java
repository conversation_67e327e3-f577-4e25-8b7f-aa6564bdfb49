package com.intuit.appintgwkflw.wkflautomate.was.apollo.client.util;

import com.intuit.cto.general.logging.slf4j.MdcFrame;
import lombok.experimental.UtilityClass;
import org.slf4j.MDC;
import reactor.util.context.ContextView;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Provides utils to handles the MDC context propagation
 */
@UtilityClass
public class ReactorLoggingUtils {

    public static final String MDC_KEY = "MDC";

    /**
     *
     * @param contextView Reactor Context view
     * @return return MdcFrame with new context
     */
    public MdcFrame mdcFrameFromContext(ContextView contextView) {
        if (contextView == null) {
            return new MdcFrame();
        }
        Map<String, String> mdcMap = contextView.getOrDefault(MDC_KEY, getMDCCopy());
        return new MdcFrame().put(mdcMap);
    }

    /**
     * @return copy of MDC map in context of executing thread. Filters the null values in MDC
     */
    public Map<String, String> getMDCCopy() {
        return Optional.ofNullable(MDC.getCopyOfContextMap()).orElse(Collections.emptyMap())
                .entrySet()
                .stream()
                .filter(entry -> Objects.nonNull(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * Provide logging statements particularly, which should be executed within MDC Frame
     * @param contextView - Reactor context view
     * @param r - Logging statements to be executed
     */
    public void executeInContext(ContextView contextView, Runnable r) {
        try (MdcFrame mf = mdcFrameFromContext(contextView)) {
            r.run();
        }
    }
}
